version: '3'

# 网桥redis -> 方便相互通讯
networks:
  redis:

services:
  # 主
  master:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/redis:6.0.8                    # 镜像'redis:6.0.8'
    container_name: redis-master                                                      # 容器名为'redis-master'
    restart: unless-stopped                                                                   # 指定容器退出后的重启策略为始终重启，但是不考虑在Docker守护进程启动时就已经停止了的容器
    command: redis-server /etc/redis/redis.conf --requirepass 123456 --appendonly no # 启动redis服务并添加密码为：123456,默认不开启redis-aof方式持久化配置
    environment:                        # 设置环境变量,相当于docker run命令中的-e
      TZ: Asia/Shanghai
      LANG: en_US.UTF-8
    volumes:                            # 数据卷挂载路径设置,将本机目录映射到容器目录
      - "./redis-master-slave/master/data:/data"
      - "./redis-master-slave/master/config/redis.conf:/etc/redis/redis.conf"  # `redis.conf`文件内容`http://download.redis.io/redis-stable/redis.conf`
    ports:                              # 映射端口
      - "6380:6379"
    networks:
      - redis
  # 从1
  slave1:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/redis:6.0.8                   # 镜像'redis:6.0.8'
    container_name: redis-slave-1                                                    # 容器名为'redis-slave-1'
    restart: unless-stopped                                                                   # 指定容器退出后的重启策略为始终重启，但是不考虑在Docker守护进程启动时就已经停止了的容器
    command: redis-server /etc/redis/redis.conf --requirepass 123456 --appendonly no --slaveof www.zhengqingya.com 6380 --masterauth 123456 # 启动redis服务并添加密码为：123456,默认不开启redis-aof方式持久化配置,连接并认证master节点
    environment:                        # 设置环境变量,相当于docker run命令中的-e
      TZ: Asia/Shanghai
      LANG: en_US.UTF-8
    volumes:                            # 数据卷挂载路径设置,将本机目录映射到容器目录
      - "./redis-master-slave/slave-1/data:/data"
      - "./redis-master-slave/slave-1/config/redis.conf:/etc/redis/redis.conf"  # `redis.conf`文件内容`http://download.redis.io/redis-stable/redis.conf`
    ports:                              # 映射端口
      - "6381:6379"
    networks:
      - redis
  # 从2
  slave2:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/redis:6.0.8                   # 镜像'redis:6.0.8'
    container_name: redis-slave-2                                                    # 容器名为'redis-slave-2'
    restart: unless-stopped                                                                   # 指定容器退出后的重启策略为始终重启，但是不考虑在Docker守护进程启动时就已经停止了的容器
    command: redis-server /etc/redis/redis.conf --requirepass 123456 --appendonly no --slaveof www.zhengqingya.com 6380 --masterauth 123456 # 启动redis服务并添加密码为：123456,默认不开启redis-aof方式持久化配置,连接并认证master节点
    environment:                        # 设置环境变量,相当于docker run命令中的-e
      TZ: Asia/Shanghai
      LANG: en_US.UTF-8
    volumes:                            # 数据卷挂载路径设置,将本机目录映射到容器目录
      - "./redis-master-slave/slave-2/data:/data"
      - "./redis-master-slave/slave-2/config/redis.conf:/etc/redis/redis.conf"  # `redis.conf`文件内容`http://download.redis.io/redis-stable/redis.conf`
    ports:                              # 映射端口
      - "6382:6379"
    networks:
      - redis
