pipeline {
    agent any

    tools {
        maven 'Default'
    }

    options {
        timeout(time: 1, unit: 'HOURS')
        buildDiscarder(logRotator(numToKeepStr: '10'))
        disableConcurrentBuilds()
    }

    environment {
        DOCKER_REGISTRY = ''
        SKIP_BUILD = 'false'
        MAVEN_HOME = tool 'Default'
        MAVEN_LOCAL_REPO = "${JENKINS_HOME}/.m2/repository"
        MAVEN_OPTS = "-Dmaven.repo.local=${MAVEN_LOCAL_REPO}"
        CLEAN_CACHE = "${params.CLEAN_CACHE ?: 'false'}"
        CACHE_DAYS = "${params.CACHE_DAYS ?: '30'}"
    }

    stages {
        stage('Initialize Environment') {
            steps {
                script {
                    // 初始化模块相关的环境变量
                    env.MODULES = findModules()
                    echo "初始化检测到的模块: ${env.MODULES}"
                }
            }
        }

        stage('Check Tag Event') {
            steps {
                script {
                    try {
                        // 获取 Git 引用和标签信息
                        def gitRef = env.GIT_REF ?: sh(script: 'git symbolic-ref HEAD || git rev-parse HEAD', returnStdout: true).trim()
                        def tagName = env.TAG_NAME ?: sh(script: 'git describe --tags --exact-match HEAD || true', returnStdout: true).trim()
                        
                        echo """
                        ===== Git 信息 =====
                        Git Ref: ${gitRef}
                        标签名称: ${tagName}
                        ==================
                        """

                        // 检查是否是标签
                        if (tagName) {
                            echo "检测到标签 ${tagName}，开始构建和部署"
                            // 保存标签名到环境变量
                            env.TAG_VERSION = tagName
                            env.REDIS_CHANGED = 'true'
                            env.CAMEL_CHANGED = 'true'
                            env.COMMON_CHANGED = 'true'
                            env.WEB_CHANGED = 'true'
                        } else {
                            echo "不是标签事件，跳过构建"
                            env.SKIP_BUILD = 'true'
                            currentBuild.result = 'NOT_BUILT'
                            return
                        }
                    } catch (Exception e) {
                        echo "错误：${e.getMessage()}"
                        throw e
                    }
                }
            }
        }

        stage('Initialize') {
            when {
                expression { return !env.SKIP_BUILD.toBoolean() }
            }
            steps {
                script {
                    env.IMAGE_PREFIX = env.DOCKER_REGISTRY ? "${env.DOCKER_REGISTRY}/" : ''
                    // 使用保存的标签版本
                    env.BUILD_TAG = env.TAG_VERSION
                    env.REDIS_IMAGE = "${env.IMAGE_PREFIX}redis-learning:${env.BUILD_TAG}"
                    env.CAMEL_IMAGE = "${env.IMAGE_PREFIX}camel-learning:${env.BUILD_TAG}"
                    env.WEB_IMAGE = "${env.IMAGE_PREFIX}web-learning:${env.BUILD_TAG}"

                    // 检测变更的模块
                    env.CHANGED_MODULES = detectChangedModules()

                    echo """
                    ===== 初始化信息 =====
                    Tag版本: ${env.BUILD_TAG}
                    检测到的模块: ${env.MODULES}
                    变更的模块: ${env.CHANGED_MODULES}
                    ==================
                    """
                }
            }
        }

        stage('Build Modules') {
            when {
                expression { 
                    return !env.SKIP_BUILD.toBoolean() && 
                           (env.CHANGED_MODULES?.trim() || env.TAG_VERSION)
                }
            }
            steps {
                script {
                    try {
                        // 首先构建公共模块
                        sh """
                            mkdir -p ${MAVEN_LOCAL_REPO}
                            
                            # 安装父POM
                            echo "安装父 POM..."
                            ${MAVEN_HOME}/bin/mvn -B clean install -N ${MAVEN_OPTS}
                            
                            # 如果是标签构建或common模块有变更，就构建common模块
                            if [[ -n "${env.TAG_VERSION}" || "${env.CHANGED_MODULES}" == *"common-logging-spring-boot-starter"* ]]; then
                                echo "构建 common-logging-spring-boot-starter 模块..."
                                cd common-logging-spring-boot-starter
                                ${MAVEN_HOME}/bin/mvn -B clean install -DskipTests ${MAVEN_OPTS}
                                cd ..
                            fi
                        """

                        // 构建其他变更的模块
                        def modulesToBuild = env.TAG_VERSION ? env.MODULES.split(',') : env.CHANGED_MODULES.split(',')
                        modulesToBuild.each { module ->
                            if (module && module != 'common-logging-spring-boot-starter') {
                                sh """
                                    echo "构建 ${module} 模块..."
                                    cd ${module}
                                    ${MAVEN_HOME}/bin/mvn -B clean package -DskipTests ${MAVEN_OPTS}
                                    cd ..
                                """
                            }
                        }
                    } catch (Exception e) {
                        echo "构建失败：${e.getMessage()}"
                        currentBuild.result = 'FAILURE'
                        error "构建模块失败"
                    }
                }
            }
        }

        stage('Build Images') {
            when {
                expression { return !env.SKIP_BUILD.toBoolean() && env.CHANGED_MODULES != '' }
            }
            steps {
                script {
                    env.CHANGED_MODULES.split(',').each { module ->
                        if (module != 'common-logging-spring-boot-starter') {
                            def imageTag = "${env.IMAGE_PREFIX}${module}:${env.BUILD_TAG}"
                            buildDockerImage(module, imageTag)
                        }
                    }
                }
            }
        }

        stage('Deploy') {
            when {
                expression { return !env.SKIP_BUILD.toBoolean() }
            }
            steps {
                script {
                    deployServices()
                }
            }
        }

        stage('Clean Cache') {
            when {
                expression { return env.CLEAN_CACHE == 'true' }
            }
            steps {
                script {
                    echo "开始清理 Maven 缓存..."
                    sh """
                        echo "清理 ${CACHE_DAYS} 天前的缓存文件..."
                        find ${MAVEN_LOCAL_REPO} -type f -atime +${CACHE_DAYS} -delete || true
                        echo "清理空目录..."
                        find ${MAVEN_LOCAL_REPO} -type d -empty -delete || true
                        echo "清理快照版本..."
                        find ${MAVEN_LOCAL_REPO} -name "*SNAPSHOT*" -type f -delete || true
                        echo "Maven 缓存清理完成"
                        echo "当前缓存大小："
                        du -sh ${MAVEN_LOCAL_REPO}
                    """
                }
            }
        }
    }

    post {
        success {
            script {
                if (!env.SKIP_BUILD.toBoolean()) {
                    echo "构建成功！"
                }
            }
        }
        failure {
            script {
                if (!env.SKIP_BUILD.toBoolean()) {
                    echo "构建失败！"
                }
            }
        }
        always {
            cleanWs()
        }
    }
}

// 辅助函数
def buildDockerImage(String module, String tag) {
    echo "构建 ${module} 镜像..."
    def oldImages = sh(
        script: """
            docker images --format '{{.Repository}}:{{.Tag}}' | grep "^${env.IMAGE_PREFIX}${module}:" || true
        """,
        returnStdout: true
    ).trim()
    
    sh """
        docker build -t ${tag} \
            --build-arg JAR_FILE=target/*.jar \
            -f ${module}/Dockerfile ${module}
    """
    
    if (oldImages) {
        echo "清理旧的 ${module} 镜像，保留最新的3个版本..."
        sh """
            docker images --format '{{.Repository}}:{{.Tag}}' ${env.IMAGE_PREFIX}${module} | 
            grep -v '${tag}' | 
            sort -r | 
            tail -n +4 | 
            xargs -r docker rmi || true
        """
    }
}

def deployServices() {
    sh '''
        echo "=== 开始部署服务 ==="
        docker compose down || true
        
        # 创建.env文件并写入头部注释
        echo "# Docker Compose环境变量配置 - 创建于 $(date)" > .env
        echo "# 由Jenkins自动生成，请勿手动修改" >> .env
    '''
    
    // 为每个模块设置环境变量
    env.MODULES.split(',').each { module ->
        if (module != 'common-logging-spring-boot-starter') {
            def envVarName = "${module.toUpperCase().replace('-', '_')}_IMAGE"
            def imageValue = "${env.IMAGE_PREFIX}${module}:${env.BUILD_TAG}"
            sh """
                echo "${envVarName}=${imageValue}" >> .env
            """
        }
    }
    
    sh '''
        echo "=== .env文件内容 ==="
        cat .env
        echo "==================="
        
        # 使用.env文件启动服务
        docker compose --env-file .env up -d
        
        # 显示启动的容器
        echo "=== 已启动的容器 ==="
        docker compose ps
    '''
}

def cleanupImages(String tagName) {
    sh """
        echo "=== 清理标签 ${tagName} 对应的镜像 ==="
        ${env.MODULES.split(',').collect { module ->
            if (module != 'common-logging-spring-boot-starter') {
                "docker rmi ${env.IMAGE_PREFIX}${module}:${tagName} || true"
            }
        }.join('\n')}
        echo "=== 清理完成 ==="
    """
}

// 添加自动检测模块的函数
def findModules() {
    def ignoredModules = []
    if (fileExists('.jenkinsignore')) {
        ignoredModules = readFile('.jenkinsignore').readLines()
            .findAll { it && !it.startsWith('#') }
            .collect { it.trim() }
        echo "从.jenkinsignore读取到的忽略模块: ${ignoredModules}"
    }

    def allModules = sh(
        script: """
            find . -maxdepth 1 -type d -name "*-learning" -o -name "common-*" | sed 's|^./||' | tr '\\n' ',' | sed 's/,\$//'
        """,
        returnStdout: true
    ).trim()

    echo "检测到的所有模块: ${allModules}"
    
    def filteredModules = allModules.split(',')
        .findAll { module -> 
            def shouldInclude = !ignoredModules.contains(module)
            if (!shouldInclude) {
                echo "模块 ${module} 被.jenkinsignore排除"
            }
            return shouldInclude
        }
        .join(',')
        
    echo "过滤后的模块（排除.jenkinsignore中的模块）: ${filteredModules}"
    
    return filteredModules
}

// 添加检测模块变更的函数
def detectChangedModules() {
    try {
        def changedFiles
        if (env.TAG_VERSION) {
            // 获取上一个标签
            def lastTag = sh(
                script: "git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo ''",
                returnStdout: true
            ).trim()

            if (lastTag) {
                // 如果有上一个标签，获取从上一个标签到当前标签之间的所有更改
                echo "检测从标签 ${lastTag} 到 ${env.TAG_VERSION} 之间的更改"
                changedFiles = sh(
                    script: "git diff --name-only ${lastTag} HEAD",
                    returnStdout: true
                ).trim()
            } else {
                // 如果没有上一个标签，获取所有历史更改
                echo "没有找到上一个标签，检测所有历史更改"
                changedFiles = sh(
                    script: "git log --name-only --pretty=format: | sort -u",
                    returnStdout: true
                ).trim()
            }
        } else {
            // 非标签构建，获取最近一次提交的更改
            changedFiles = sh(
                script: "git diff --name-only HEAD~1 HEAD || git diff --name-only",
                returnStdout: true
            ).trim()
        }

        echo "检测到的变更文件: ${changedFiles}"

        def allModules = env.MODULES.split(',')
        def changedModules = []

        // 如果common模块有变更，所有未被忽略的模块都需要重新构建
        if (changedFiles.contains('common-logging-spring-boot-starter')) {
            echo "common模块有变更，返回所有未被忽略的模块"
            return env.MODULES
        }

        // 检查每个未被忽略的模块的变更
        allModules.each { module ->
            if (changedFiles.contains(module)) {
                changedModules.add(module)
                echo "模块 ${module} 有变更"
            }
        }

        def result = changedModules.size() > 0 ? changedModules.join(',') : ''
        echo "最终需要构建的模块: ${result ?: '没有需要构建的模块'}"
        
        // 如果是标签构建但没有检测到变更，返回所有模块
        if (env.TAG_VERSION && result == '') {
            echo "标签构建但没有检测到变更，返回所有未被忽略的模块"
            return env.MODULES
        }

        return result
    } catch (Exception e) {
        echo "检测模块变更时发生错误: ${e.getMessage()}"
        echo "错误堆栈: ${e.printStackTrace()}"
        // 发生错误时返回所有未被忽略的模块
        return env.MODULES
    }
} 