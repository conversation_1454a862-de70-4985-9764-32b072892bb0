import PyInstaller.__main__
import os
from pathlib import Path
from PIL import Image, ImageDraw, ImageFilter

def create_icon():
    """创建现代风格的图标"""
    # 创建一个更大尺寸的图像以获得更好的质量
    size = 128  # 更大的初始尺寸
    img = Image.new('RGBA', (size, size), color=(0,0,0,0))
    draw = ImageDraw.Draw(img)
    
    # 使用更现代的配色方案
    primary_color = (52, 152, 219)     # 明亮的蓝色
    secondary_color = (46, 204, 113)   # 翠绿色
    bg_color = (41, 128, 185, 180)     # 半透明的深蓝色
    
    # 绘制主圆形背景
    padding = size // 8
    draw.ellipse([padding, padding, size-padding, size-padding], 
                 fill=bg_color)
    
    # 绘制动态效果线条
    line_width = size // 20
    for i in range(3):
        offset = i * (size//8)
        # 绘制动态曲线
        points = [
            (padding+offset, size//2+offset),
            (size//2, padding+offset),
            (size-padding-offset, size//2+offset)
        ]
        draw.line(points, fill=primary_color, width=line_width)
        
        # 绘制反向���线
        points = [
            (padding+offset, size//2-offset),
            (size//2, size-padding-offset),
            (size-padding-offset, size//2-offset)
        ]
        draw.line(points, fill=secondary_color, width=line_width)
    
    # 添加发光效果
    glow = img.filter(ImageFilter.GaussianBlur(radius=2))
    img = Image.alpha_composite(img, glow)
    
    # 缩放到所需尺寸
    img = img.resize((32, 32), Image.Resampling.LANCZOS)
    
    # 保存为 ICO 格式
    icon_sizes = [(16,16), (32,32), (48,48), (64,64), (128,128)]
    icon_images = []
    base_image = img  # 保存原始图像
    
    for size in icon_sizes:
        resized_image = base_image.resize(size, Image.Resampling.LANCZOS)
        icon_images.append(resized_image)
    
    return icon_images

def build():
    # 确保资源目录存在
    Path("dist/resources").mkdir(parents=True, exist_ok=True)
    Path("dist/bin").mkdir(parents=True, exist_ok=True)
    Path("dist/logs").mkdir(parents=True, exist_ok=True)
    
    # 创建并保存图标
    icon_images = create_icon()
    
    # 保存 PNG 格式
    png_path = Path("resources/icon.png")
    png_path.parent.mkdir(parents=True, exist_ok=True)
    icon_images[1].save(png_path, 'PNG')  # 使用 32x32 尺寸作为 PNG
    
    # 保存 ICO 格式
    ico_path = Path("resources/icon.ico")
    icon_images[0].save(ico_path, format='ICO', sizes=[(x.width, x.height) for x in icon_images])
    
    # 复制图标文件到dist目录
    import shutil
    if png_path.exists():
        shutil.copy2(png_path, "dist/resources/")
    if ico_path.exists():
        shutil.copy2(ico_path, "dist/resources/")
    
    # 复制必要的资源文件
    if Path("bin/client_windows_amd64.exe").exists():
        shutil.copy2("bin/client_windows_amd64.exe", "dist/bin/")

    # 创建干净的配置文件
    clean_config = {
        "current_profile": "default",
        "profiles": {
            "default": {
                "kcp_ip": "",
                "kcp_port": "",
                "kcp_password": "",
                "local_port": "",
                "advanced": {
                    "mtu": "1400",
                    "sndwnd": "1024",
                    "rcvwnd": "1024",
                    "mode": "normal",
                    "crypt": "aes-128",
                    "datashard": "10",
                    "parityshard": "3",
                    "dscp": "46",
                    "keepalive": "10",
                    "nocomp": True
                }
            }
        },
        "settings": {
            "enable_logging": False
        }
    }
    
    # 保存干净的配置文件
    import json
    with open("dist/resources/config.json", "w", encoding="utf-8") as f:
        json.dump(clean_config, f, indent=4, ensure_ascii=False)

    # PyInstaller 参数
    PyInstaller.__main__.run([
        'run.py',                          # 主程序文件
        '--name=Kcptun-GUI',               # 生成的exe名称
        '--windowed',                      # 无控制台窗口
        '--onefile',                       # 打包成单个文件
        '--icon=resources/icon.ico',       # 使用 ICO 格式的图标
        '--add-data=resources;resources',  # 添加资源文件
        '--add-data=bin;bin',             # 添加bin目录
        '--clean',                         # 清理临时文件
        '--noconfirm',                     # 不确认覆盖
        '--hidden-import=PIL._tkinter_finder',  # 添加隐藏导入
        # 排除不需要的文件
        '--exclude-module=test_tray',
        '--exclude-module=build',
    ])

if __name__ == "__main__":
    build() 