<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>游戏排行榜</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.24.0/axios.min.js"></script>

    <style>
        body {
            font-family: "微软雅黑", sans-serif;
            margin: 40px;
        }
        table {
            width: 500px;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 8px 12px;
            border: 1px solid #aaa;
            text-align: center;
        }
        input {
            padding: 6px;
            margin-right: 8px;
        }
        button {
            padding: 6px 12px;
            cursor: pointer;
        }
        .section {
            margin-top: 30px;
        }
    </style>
</head>
<body>

<h2>🎮 游戏排行榜</h2>

<!-- 添加玩家得分 -->
<div>
    <input type="text" id="name" placeholder="玩家姓名" />
    <input type="number" id="score" placeholder="分数" />
    <button onclick="submitScore()">提交</button>
</div>

<!-- 排行榜表格 -->
<table>
    <thead>
        <tr>
            <th>排名</th>
            <th>玩家</th>
            <th>得分</th>
            <th>操作</th>
        </tr>
    </thead>
    <tbody id="rankTable">
    </tbody>
</table>

<!-- 查询玩家得分 -->
<div class="section">
    <h3>🔍 查询玩家得分</h3>
    <input type="text" id="searchName" placeholder="输入玩家姓名" />
    <button onclick="searchScore()">查询</button>
    <div id="searchResult" style="margin-top:10px;"></div>
</div>

<script>
    function loadRank() {
        axios.get('/rank/top/10')
            .then(res => {
                const list = res.data;
                const table = document.getElementById('rankTable');
                table.innerHTML = '';
                list.forEach((item, index) => {
                    const row = `<tr>
                        <td>${index + 1}</td>
                        <td>${item.name}</td>
                        <td>${item.score}</td>
                        <td><button onclick="deletePlayer('${item.name}')">删除</button></td>
                    </tr>`;
                    table.innerHTML += row;
                });
            });
    }

    function submitScore() {
        const name = document.getElementById('name').value;
        const score = parseFloat(document.getElementById('score').value);
        if (!name || isNaN(score)) {
            alert('请输入有效姓名和分数');
            return;
        }

        axios.post('/rank/add', {
            name: name,
            score: score
        }).then(() => {
            loadRank();
            document.getElementById('name').value = '';
            document.getElementById('score').value = '';
        });
    }

    function deletePlayer(name) {
        if (!confirm(`确定要删除 ${name} 吗？`)) return;
        axios.delete(`/rank/remove/${encodeURIComponent(name)}`)
            .then(() => loadRank());
    }

    function searchScore() {
        const name = document.getElementById('searchName').value;
        if (!name) {
            alert("请输入玩家姓名");
            return;
        }
        axios.get(`/rank/score/${encodeURIComponent(name)}`)
            .then(res => {
                const score = res.data;
                if (score === null) {
                    document.getElementById('searchResult').innerText = `❌ 未找到该玩家`;
                    return;
                }

                // 获取当前排行榜，再计算名次
                axios.get('/rank/top/1000') // 简化处理：最多查1000名
                    .then(topRes => {
                        const list = topRes.data;
                        const index = list.findIndex(item => item.name === name);
                        const rank = index >= 0 ? index + 1 : '未知';
                        document.getElementById('searchResult').innerText =
                            `✅ ${name} 当前得分：${score}，排名：第 ${rank} 名`;
                    });
            });
    }

    loadRank();
</script>

</body>
</html>