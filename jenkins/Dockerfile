FROM jenkins/jenkins:lts

USER root

# 安装必要的工具
RUN apt-get update && \
    apt-get install -y \
    docker.io \
    maven \
    && rm -rf /var/lib/apt/lists/*

# 安装 Docker Compose
RUN curl -L "https://github.com/docker/compose/releases/download/v2.23.3/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose && \
    chmod +x /usr/local/bin/docker-compose

# 创建 Maven 缓存目录
RUN mkdir -p /root/.m2 && \
    chown -R jenkins:jenkins /root/.m2

# 添加 Jenkins 用户到 docker 组
RUN usermod -aG docker jenkins

USER jenkins

# 配置 Maven 镜像
COPY settings.xml /var/jenkins_home/.m2/