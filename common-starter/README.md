# Common Spring Boot Starter

## 简介
Common Spring Boot Starter 是一个统一的启动器，用于管理和配置所有common模块。它提供了一种简单的方式来选择性地启用或禁用各个功能模块。

## 功能特性
- 模块化配置：每个common模块都可以独立启用或禁用
- 自动装配：基于Spring Boot的自动配置机制
- 灵活扩展：支持在不同环境中使用不同的模块配置

## 快速开始

### 1. 添加依赖
```xml
<dependency>
    <groupId>com.example</groupId>
    <artifactId>common-starter</artifactId>
    <version>${project.version}</version>
</dependency>
```

### 2. 配置模块
在application.properties/application.yml中配置需要启用的模块：

```properties
# 启用指定模块
common.logging-enabled=true
common.utils-enabled=true
common.constants-enabled=false
# ... 其他模块配置
```

或者使用YAML格式：

```yaml
common:
  logging-enabled: true
  utils-enabled: true
  constants-enabled: false
  # ... 其他模块配置
```

## 可配置模块
- logging：日志模块
- utils：工具类模块
- constants：常量模块
- exceptions：异常处理模块
- config：配置管理模块
- db：数据库模块
- cache：缓存模块
- api：API模块
- security：安全模块
- messaging：消息模块
- scheduler：调度模块
- thirdparty：第三方集成模块

## 配置说明

### 默认配置
所有模块默认都是启用的。如果需要禁用某个模块，需要显式设置为false：

```properties
common.module-name-enabled=false
```

### 配置优先级
1. application-{profile}.properties/yml中的配置
2. application.properties/yml中的配置
3. 默认配置（所有模块启用）

## 最佳实践
1. 只启用需要的模块，减少不必要的依赖加载
2. 在开发环境可以启用更多模块便于调试
3. 在生产环境只启用必要的模块
4. 使用配置文件区分不同环境的模块需求

## 版本说明
- 1.0.0：初始版本，支持所有common模块的配置管理

## 注意事项
1. 模块之间可能存在依赖关系，确保依赖模块同时启用
2. 更改配置后需要重启应用才能生效
3. 建议在应用启动时检查所需模块是否正确启用 