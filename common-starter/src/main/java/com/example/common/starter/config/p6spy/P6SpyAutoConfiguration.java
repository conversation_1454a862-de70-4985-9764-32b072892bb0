package com.example.common.starter.config.p6spy;

import com.example.common.logging.handler.LogHandler;
import com.example.common.logging.p6spy.P6SpyLogger;
import com.p6spy.engine.spy.P6SpyOptions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import jakarta.annotation.PostConstruct;

/**
 * P6Spy自动配置
 */
@Configuration
@ConditionalOnClass(P6SpyOptions.class)
@ConditionalOnProperty(prefix = "common.p6spy", name = "enabled", havingValue = "true", matchIfMissing = true)
public class P6SpyAutoConfiguration {

    private final LogHandler logHandler;

    public P6SpyAutoConfiguration(LogHandler logHandler) {
        this.logHandler = logHandler;
    }

    @PostConstruct
    public void init() {
        P6SpyOptions.getActiveInstance().setLogMessageFormat(P6SpyLogger.class.getName());
        System.setProperty("p6spy.config.logMessageFormat", P6SpyLogger.class.getName());
    }
} 