package com.example.common.starter.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "common")
public class CommonProperties {
    private boolean loggingEnabled = true;
    private boolean utilsEnabled = true;
    private boolean constantsEnabled = true;
    private boolean exceptionsEnabled = true;
    private boolean configEnabled = true;
    private boolean dbEnabled = true;
    private boolean cacheEnabled = true;
    private boolean apiEnabled = true;
    private boolean securityEnabled = true;
    private boolean messagingEnabled = true;
    private boolean schedulerEnabled = true;
    private boolean thirdpartyEnabled = true;

    // Getters and Setters
    public boolean isLoggingEnabled() {
        return loggingEnabled;
    }

    public void setLoggingEnabled(boolean loggingEnabled) {
        this.loggingEnabled = loggingEnabled;
    }

    public boolean isUtilsEnabled() {
        return utilsEnabled;
    }

    public void setUtilsEnabled(boolean utilsEnabled) {
        this.utilsEnabled = utilsEnabled;
    }

    public boolean isConstantsEnabled() {
        return constantsEnabled;
    }

    public void setConstantsEnabled(boolean constantsEnabled) {
        this.constantsEnabled = constantsEnabled;
    }

    public boolean isExceptionsEnabled() {
        return exceptionsEnabled;
    }

    public void setExceptionsEnabled(boolean exceptionsEnabled) {
        this.exceptionsEnabled = exceptionsEnabled;
    }

    public boolean isConfigEnabled() {
        return configEnabled;
    }

    public void setConfigEnabled(boolean configEnabled) {
        this.configEnabled = configEnabled;
    }

    public boolean isDbEnabled() {
        return dbEnabled;
    }

    public void setDbEnabled(boolean dbEnabled) {
        this.dbEnabled = dbEnabled;
    }

    public boolean isCacheEnabled() {
        return cacheEnabled;
    }

    public void setCacheEnabled(boolean cacheEnabled) {
        this.cacheEnabled = cacheEnabled;
    }

    public boolean isApiEnabled() {
        return apiEnabled;
    }

    public void setApiEnabled(boolean apiEnabled) {
        this.apiEnabled = apiEnabled;
    }

    public boolean isSecurityEnabled() {
        return securityEnabled;
    }

    public void setSecurityEnabled(boolean securityEnabled) {
        this.securityEnabled = securityEnabled;
    }

    public boolean isMessagingEnabled() {
        return messagingEnabled;
    }

    public void setMessagingEnabled(boolean messagingEnabled) {
        this.messagingEnabled = messagingEnabled;
    }

    public boolean isSchedulerEnabled() {
        return schedulerEnabled;
    }

    public void setSchedulerEnabled(boolean schedulerEnabled) {
        this.schedulerEnabled = schedulerEnabled;
    }

    public boolean isThirdpartyEnabled() {
        return thirdpartyEnabled;
    }

    public void setThirdpartyEnabled(boolean thirdpartyEnabled) {
        this.thirdpartyEnabled = thirdpartyEnabled;
    }
} 