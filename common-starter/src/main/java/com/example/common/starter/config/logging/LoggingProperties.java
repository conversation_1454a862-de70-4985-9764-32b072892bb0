package com.example.common.starter.config.logging;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "common.logging")
public class LoggingProperties {
    
    private String mode = "async";
    private AsyncConfig async = new AsyncConfig();
    private FileConfig file = new FileConfig();
    private FormatConfig format = new FormatConfig();

    @Data
    public static class AsyncConfig {
        private int corePoolSize = 2;
        private int maxPoolSize = 5;
        private int queueCapacity = 100;
    }

    @Data
    public static class FileConfig {
        private String path = "logs";
        private String maxFileSize = "100MB";
        private int maxHistory = 30;
        private String totalSizeCap = "10GB";
    }

    @Data
    public static class FormatConfig {
        private String pattern = "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n";
        private boolean enableColor = true;
        private String traceIdColor = "blue";
        private String classNameColor = "cyan";
        private String methodNameColor = "green";
        private String timeColor = "yellow";
    }
} 