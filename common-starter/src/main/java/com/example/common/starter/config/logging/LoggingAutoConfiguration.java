package com.example.common.starter.config.logging;

import com.example.common.logging.handler.AsyncLogHandler;
import com.example.common.logging.handler.LogHandler;
import com.example.common.logging.handler.SyncLogHandler;
import com.example.common.logging.interceptor.LoggingInterceptor;
import com.example.common.logging.sensitive.SensitiveDataConverter;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.Filter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.filter.CommonsRequestLoggingFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.beans.factory.ObjectProvider;
import org.slf4j.MDC;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;
import org.springframework.web.filter.OncePerRequestFilter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.Map;

@Configuration
@EnableAsync
@EnableConfigurationProperties(LoggingProperties.class)
public class LoggingAutoConfiguration implements WebMvcConfigurer {

    private final ObjectProvider<LogHandler> logHandlerProvider;
    private final ObjectMapper objectMapper;

    public LoggingAutoConfiguration(ObjectProvider<LogHandler> logHandlerProvider, ObjectMapper objectMapper) {
        this.logHandlerProvider = logHandlerProvider;
        this.objectMapper = objectMapper;
    }

    @Bean
    public Filter contentCachingFilter() {
        return new OncePerRequestFilter() {
            @Override
            protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
                    throws ServletException, IOException {
                request.setCharacterEncoding(StandardCharsets.UTF_8.name());
                response.setCharacterEncoding(StandardCharsets.UTF_8.name());
                
                ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
                ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
                try {
                    filterChain.doFilter(requestWrapper, responseWrapper);
                } finally {
                    responseWrapper.copyBodyToResponse();
                }
            }
        };
    }

    @Bean
    public SensitiveDataConverter sensitiveDataConverter() {
        return new SensitiveDataConverter();
    }

    @Bean
    public ThreadPoolTaskExecutor asyncLogExecutor(LoggingProperties properties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor() {
            @Override
            public void execute(Runnable task) {
                Map<String, String> contextMap = MDC.getCopyOfContextMap();
                super.execute(() -> {
                    try {
                        if (contextMap != null) {
                            MDC.setContextMap(contextMap);
                        }
                        task.run();
                    } finally {
                        MDC.clear();
                    }
                });
            }
        };
        executor.setCorePoolSize(properties.getAsync().getCorePoolSize());
        executor.setMaxPoolSize(properties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(properties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix("AsyncLogger-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean
    @Primary
    @ConditionalOnProperty(name = "common.logging.mode", havingValue = "async", matchIfMissing = true)
    public LogHandler asyncLogHandler(ThreadPoolTaskExecutor asyncLogExecutor, SensitiveDataConverter sensitiveDataConverter) {
        return new AsyncLogHandler(asyncLogExecutor, sensitiveDataConverter);
    }

    @Bean
    @ConditionalOnProperty(name = "common.logging.mode", havingValue = "sync")
    public LogHandler syncLogHandler(SensitiveDataConverter sensitiveDataConverter) {
        return new SyncLogHandler(sensitiveDataConverter);
    }

    @Bean
    public LoggingInterceptor loggingInterceptor() {
        return new LoggingInterceptor(logHandlerProvider.getIfAvailable(), objectMapper);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(loggingInterceptor())
                .addPathPatterns("/**")
                .order(0);
    }
} 