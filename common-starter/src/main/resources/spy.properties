# 基础配置
modulelist=com.p6spy.engine.logging.P6LogFactory,com.p6spy.engine.outage.P6OutageFactory

# 使用自定义的日志格式化器
appender=com.p6spy.engine.spy.appender.Slf4JLogger
logMessageFormat=com.example.common.logging.p6spy.P6spyLogFormatter

# 设置 p6spy driver 代理
driverlist=com.mysql.cj.jdbc.Driver
deregisterdrivers=true

# 日期格式
dateformat=yyyy-MM-dd HH:mm:ss.SSS

# 是否开启慢SQL记录
outagedetection=true
# 慢SQL记录标准 2 秒
outagedetectioninterval=2

# 是否开启日志过滤
filter=true

# 包含指定的表名
include=

# 不包含指定的表名
exclude=

# 是否开启日志格式化
logMessageFormat.format=true

# 不显示的语句
exclude_pattern=(?i)select 1 