{"groups": [{"name": "common", "type": "com.example.common.starter.config.CommonProperties", "sourceType": "com.example.common.starter.config.CommonProperties"}, {"name": "common.logging", "type": "com.example.common.starter.properties.LoggingProperties", "sourceType": "com.example.common.starter.properties.LoggingProperties"}], "properties": [{"name": "common.logging-enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable the logging module.", "sourceType": "com.example.common.starter.config.CommonProperties", "defaultValue": true}, {"name": "common.logging.async", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable async logging.", "sourceType": "com.example.common.starter.properties.LoggingProperties", "defaultValue": true}, {"name": "common.logging.async-config.core-pool-size", "type": "java.lang.Integer", "description": "Core pool size for async logging thread pool.", "sourceType": "com.example.common.starter.properties.LoggingProperties$AsyncConfig", "defaultValue": 2}, {"name": "common.logging.async-config.max-pool-size", "type": "java.lang.Integer", "description": "Maximum pool size for async logging thread pool.", "sourceType": "com.example.common.starter.properties.LoggingProperties$AsyncConfig", "defaultValue": 10}, {"name": "common.logging.async-config.queue-capacity", "type": "java.lang.Integer", "description": "Queue capacity for async logging thread pool.", "sourceType": "com.example.common.starter.properties.LoggingProperties$AsyncConfig", "defaultValue": 512}, {"name": "common.logging.file-config.path", "type": "java.lang.String", "description": "Log file path.", "sourceType": "com.example.common.starter.properties.LoggingProperties$FileConfig", "defaultValue": "logs"}, {"name": "common.logging.file-config.max-file-size", "type": "java.lang.String", "description": "Maximum size of log file before rolling.", "sourceType": "com.example.common.starter.properties.LoggingProperties$FileConfig", "defaultValue": "100MB"}, {"name": "common.logging.format-config.pattern", "type": "java.lang.String", "description": "Log pattern.", "sourceType": "com.example.common.starter.properties.LoggingProperties$FormatConfig", "defaultValue": "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"}, {"name": "common.logging.sensitive-config.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable sensitive data masking.", "sourceType": "com.example.common.starter.properties.LoggingProperties$SensitiveConfig", "defaultValue": true}], "hints": []}