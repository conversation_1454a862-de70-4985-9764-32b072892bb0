package com.example.camel.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.spi.CamelEvent;
import org.apache.camel.support.EventNotifierSupport;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Configuration
public class CamelMDCConfig {

    @Autowired
    private CamelContext camelContext;

    @Value("${common.logging.trace.id-length:8}")
    private int traceIdLength;

    @Value("${common.logging.trace.id-prefix:}")
    private String traceIdPrefix;

    @Value("${common.logging.trace.include-timestamp:false}")
    private boolean includeTimestamp;

    private static final String TRACE_ID_KEY = "traceId";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("MMddHHmmss");

    @PostConstruct
    public void init() {
        try {
            EventNotifierSupport notifier = createMDCEventNotifier();
            notifier.setCamelContext(camelContext);
            camelContext.getManagementStrategy().addEventNotifier(notifier);
            notifier.start();
            log.info("MDC Event Notifier initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize MDC Event Notifier", e);
            // 记录错误但不中断应用启动
        }
    }

    private EventNotifierSupport createMDCEventNotifier() {
        return new EventNotifierSupport() {
            @Override
            public void notify(CamelEvent event) throws Exception {
                try {
                    if (event instanceof CamelEvent.ExchangeSendingEvent) {
                        handleExchangeSendingEvent((CamelEvent.ExchangeSendingEvent) event);
                    } else if (event instanceof CamelEvent.ExchangeSentEvent) {
                        cleanupMDC();
                    }
                } catch (Exception e) {
                    log.error("Error processing Camel event", e);
                    cleanupMDC(); // 确保清理MDC
                }
            }

            public boolean isEnabled() {
                return true;
            }
        };
    }

    private void handleExchangeSendingEvent(CamelEvent.ExchangeSendingEvent event) {
        try {
            String traceId = event.getExchange().getIn().getHeader("X-Trace-ID", String.class);
            if (traceId == null || traceId.isEmpty()) {
                traceId = generateTraceId();
                event.getExchange().getIn().setHeader("X-Trace-ID", traceId);
            }
            
            // 设置路由ID到MDC
            String routeId = event.getExchange().getFromRouteId();
            MDC.put("camelRouteId", routeId);
            
            // 设置到MDC
            setMDC(traceId);
            
            // 格式化日志消息
            String message = formatExchangeMessage(event.getExchange());
            if (message != null) {
                log.info(message);
            }
        } catch (Exception e) {
            log.error("Error handling exchange sending event", e);
        }
    }

    private String formatExchangeMessage(Exchange exchange) {
        String method = exchange.getIn().getHeader("CamelHttpMethod", String.class);
        String path = exchange.getIn().getHeader("CamelHttpPath", String.class);
        String routeId = exchange.getFromRouteId();
        
        // 只在请求结束时记录日志
        if (exchange.getProperties().containsKey("CamelToEndpoint")) {
            Object response = exchange.getMessage().getBody();
            return String.format("%s %s -> %s",
                method != null ? method : "-",
                path != null ? path : "-",
                response != null ? response : "-");
        }
        
        return null; // 返回 null 表示不记录日志
    }

    private void setMDC(String traceId) {
        // 清理之前的值，防止污染
        cleanupMDC();
        MDC.put(TRACE_ID_KEY, traceId);
    }

    private void cleanupMDC() {
        try {
            MDC.remove(TRACE_ID_KEY);
            MDC.remove("camelRouteId");  // 清理路由ID
        } catch (Exception e) {
            log.warn("Error cleaning up MDC", e);
        }
    }

    private String generateTraceId() {
        StringBuilder traceId = new StringBuilder();
        
        // 添加前缀（如果配置了的话）
        if (traceIdPrefix != null && !traceIdPrefix.isEmpty()) {
            traceId.append(traceIdPrefix).append("-");
        }
        
        // 添加时间戳（如果启用）
        if (includeTimestamp) {
            traceId.append(LocalDateTime.now().format(TIME_FORMATTER)).append("-");
        }
        
        // 生成UUID部分
        String uuid = UUID.randomUUID().toString().replace("-", "");
        
        // 确保总长度不超过配置的长度
        int remainingLength = traceIdLength - traceId.length();
        if (remainingLength > 0) {
            traceId.append(uuid.substring(0, Math.min(remainingLength, uuid.length())));
        }
        
        return traceId.toString();
    }
} 