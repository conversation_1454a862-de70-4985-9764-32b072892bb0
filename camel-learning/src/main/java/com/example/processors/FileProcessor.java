package com.example.processors;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class FileProcessor implements Processor {
    private static final Logger logger = LoggerFactory.getLogger(FileProcessor.class);

    @Override
    public void process(Exchange exchange) throws Exception {
        String fileName = exchange.getIn().getHeader("CamelFileName", String.class);
        String body = exchange.getIn().getBody(String.class);
        logger.info("处理文件: {}, 内容长度: {}", fileName, (body != null ? body.length() : 0));
        exchange.getMessage().setBody(body);
    }
}
