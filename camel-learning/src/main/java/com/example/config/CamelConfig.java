package com.example.config;

import org.apache.camel.LoggingLevel;
import org.apache.camel.builder.RouteBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
public class CamelConfig {

    @Bean(destroyMethod = "shutdown",name = "customThreadPool")
    public ExecutorService customThreadPool() {
        return Executors.newFixedThreadPool(10);
    }

    @Bean
    public RouteBuilder globalConfig(AppConfig appConfig) {
        return new RouteBuilder() {
            @Override
            public void configure() throws Exception {
                // 全局错误处理
                errorHandler(defaultErrorHandler()
                        .maximumRedeliveries(3)
                        .redeliveryDelay(2000)
                        .retryAttemptedLogLevel(LoggingLevel.WARN));

                // 全局选项
                getContext().setTracing(true);
                getContext().setMessageHistory(true);
                getContext().getGlobalOptions().put("CamelFaultHandlingEnabled", "true");

                // 异常处理
                onException(Exception.class)
                        .log(LoggingLevel.ERROR, "全局异常捕获: ${exception.message}")
                        .handled(true)
                        .transform().simple("{\"error\": \"${exception.message}\"}");
            }
        };
    }
}
