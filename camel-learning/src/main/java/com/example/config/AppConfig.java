package com.example.config;

import com.example.camel.config.CamelMDCConfig;
import org.apache.camel.CamelContext;
import org.apache.camel.spring.boot.CamelContextConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(CamelMDCConfig.class)
public class AppConfig {

    @Bean
    public CamelContextConfiguration camelContextConfiguration() {
        return new CamelContextConfiguration() {
            @Override
            public void beforeApplicationStart(CamelContext camelContext) {
                // 启用追踪
                camelContext.setTracing(true);
            }

            @Override
            public void afterApplicationStart(CamelContext camelContext) {
                // 应用启动后的配置
            }
        };
    }
} 