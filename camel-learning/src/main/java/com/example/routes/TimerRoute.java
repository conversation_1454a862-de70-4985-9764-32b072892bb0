package com.example.routes;

import org.apache.camel.builder.RouteBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class TimerRoute extends RouteBuilder {
    private static final Logger logger = LoggerFactory.getLogger(TimerRoute.class);

    @Value("${timer.period:60000}")
    private long period;

    @Override
    public void configure() throws Exception {
        logger.info("Configuring timer route with period: {} ms", period);

        from("timer:example?period=" + period)
            .routeId("timerRoute")
            .process(exchange -> {
                logger.debug("Timer triggered at: {}", exchange.getIn().getHeader("CamelTimerFiredTime"));
            })
            .log("Timer triggered: ${header.CamelTimerFiredTime}")
            .process(exchange -> {
                logger.info("Timer processing completed");
            });

        logger.info("Timer route configured successfully");
    }
}
