package com.example.routes;

import org.apache.camel.Exchange;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.rest.RestBindingMode;
import org.apache.camel.support.DefaultMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Component
public class RestApiRoute extends RouteBuilder {
    private static final Logger logger = LoggerFactory.getLogger(RestApiRoute.class);

    @Override
    public void configure() throws Exception {
        logger.info("Configuring REST API route");

        // 全局异常处理
        onException(Exception.class)
            .handled(true)
            .process(exchange -> {
                DefaultMessage message = new DefaultMessage(exchange.getContext());
                message.setBody(Map.of(
                    "status", "error",
                    "message", "Internal server error",
                    "code", 500
                ));
                message.setHeader(Exchange.CONTENT_TYPE, "application/json;charset=UTF-8");
                message.setHeader(Exchange.HTTP_RESPONSE_CODE, 500);
                exchange.setMessage(message);
            });

        // REST 配置
        restConfiguration()
            .component("servlet")
            .bindingMode(RestBindingMode.json)
            .dataFormatProperty("prettyPrint", "true")
            .contextPath("/")
            .port(8082)
            .enableCORS(true)
            .apiContextPath("/api-doc")
            .apiProperty("api.title", "Echo API")
            .apiProperty("api.version", "1.0.0")
            .apiProperty("cors", "true");

        // REST API 定义
        rest("/api")
            .id("api-route")
            .consumes("application/json")
            .produces("application/json")

            .get("/hello")
            .id("hello-api")
            .description("Hello endpoint")
            .outType(Map.class)
            .to("direct:hello")
            
            .get("/echo/{message}")
            .id("echo-api")
            .description("Echo endpoint")
            .outType(Map.class)
            .to("direct:echo");

        // 处理路由
        from("direct:hello")
            .routeId("hello-route")
            .process(exchange -> {
                DefaultMessage message = new DefaultMessage(exchange.getContext());
                message.setBody(Map.of(
                    "message", "您好 Achun!",
                    "status", "success",
                    "code", 200
                ));
                message.setHeader(Exchange.CONTENT_TYPE, "application/json;charset=UTF-8");
                message.setHeader(Exchange.HTTP_RESPONSE_CODE, 200);
                exchange.setMessage(message);
            });

        from("direct:echo")
            .routeId("echo-route")
            .process(exchange -> {
                try {
                    // 获取并解码消息
                    String message = exchange.getIn().getHeader("message", String.class);
                    String decodedMessage = URLDecoder.decode(message, StandardCharsets.UTF_8);
                    logger.info("Received message: {}", decodedMessage);
                    
                    // 创建新的消息对象
                    DefaultMessage responseMessage = new DefaultMessage(exchange.getContext());
                    
                    // 设置响应体
                    Map<String, Object> response = new HashMap<>();
                    response.put("message", "Echo: " + decodedMessage);
                    response.put("status", "success");
                    response.put("code", 200);
                    responseMessage.setBody(response);
                    
                    // 设置响应头
                    responseMessage.setHeader(Exchange.CONTENT_TYPE, "application/json;charset=UTF-8");
                    responseMessage.setHeader(Exchange.HTTP_RESPONSE_CODE, 200);
                    
                    // 替换原有消息
                    exchange.setMessage(responseMessage);
                    
                } catch (Exception e) {
                    logger.error("Error processing message", e);
                    DefaultMessage errorMessage = new DefaultMessage(exchange.getContext());
                    errorMessage.setBody(Map.of(
                        "message", "Error processing message",
                        "status", "error",
                        "code", 400
                    ));
                    errorMessage.setHeader(Exchange.CONTENT_TYPE, "application/json;charset=UTF-8");
                    errorMessage.setHeader(Exchange.HTTP_RESPONSE_CODE, 400);
                    exchange.setMessage(errorMessage);
                }
            });

        logger.info("REST API route configured successfully");
    }
}
