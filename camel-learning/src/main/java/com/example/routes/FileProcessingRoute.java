package com.example.routes;

import org.apache.camel.builder.RouteBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class FileProcessingRoute extends RouteBuilder {
    private static final Logger logger = LoggerFactory.getLogger(FileProcessingRoute.class);

    @Value("${app.input-directory}")
    private String inputDirectory;

    @Value("${app.output-directory}")
    private String outputDirectory;

    @Value("${app.processing-delay:1000}")
    private long processingDelay;

    @Override
    public void configure() throws Exception {
        logger.info("Configuring file processing route with input directory: {}, output directory: {}", 
                   inputDirectory, outputDirectory);

        from("file:" + inputDirectory + "?delete=true")
            .routeId("fileProcessingRoute")
            .log("Processing file: ${header.CamelFileName}")
            .process(exchange -> {
                String fileName = exchange.getIn().getHeader("CamelFileName", String.class);
                logger.debug("Started processing file: {}", fileName);
                try {
                    Thread.sleep(processingDelay);
                    logger.debug("Completed processing delay for file: {}", fileName);
                } catch (InterruptedException e) {
                    logger.error("Processing interrupted for file: {}", fileName, e);
                    Thread.currentThread().interrupt();
                }
            })
            .to("file:" + outputDirectory)
            .log("File processed successfully: ${header.CamelFileName}")
            .process(exchange -> {
                String fileName = exchange.getIn().getHeader("CamelFileName", String.class);
                logger.info("Successfully moved file {} to output directory", fileName);
            });

        logger.info("File processing route configured successfully");
    }
}
