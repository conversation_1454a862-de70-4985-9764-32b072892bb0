server:
  port: 8082
  tomcat:
    uri-encoding: UTF-8
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true

camel:
  servlet:
    mapping:
      context-path: /*
  springboot:
    name: camel-rest-service
    main-run-controller: true
  rest:
    api-context-path: /api-doc
    api-property:
      api-title: Echo API
      api-version: 1.0.0
      cors: true
    binding-mode: json
    data-format-property:
      prettyPrint: true
  component:
    servlet:
      mapping:
        enabled: true
    rest-api:
      enabled: true

spring:
  application:
    name: camel-learning
  profiles:
    active: dev
  main:
    banner-mode: off
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true

# 自定义配置
app:
  input-directory: data/input
  output-directory: data/output
  poll-delay: 5000
  processing-delay: 1000

timer:
  period: 600000

# 统一日志配置
common:
  logging:
    enabled: true
    log-path: logs
    module-name: camel-learning
    level: INFO
    pattern: "%d{HH:mm:ss.SSS} %-5level [%X{traceId}] - %msg%n"
    camel-pattern: "%d{HH:mm:ss.SSS} %-5level [%X{traceId}] - %msg%n"
    enable-console: true
    max-history: 30
    max-file-size: 100MB
    async:
      enabled: true
      queue-size: 512
    trace:
      enabled: true
      id-length: 16
      id-prefix: CAMEL
      include-timestamp: true

logging:
  config: # 置空，使用我们的日志配置
