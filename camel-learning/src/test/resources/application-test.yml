camel:
  springboot:
    name: CamelTestApplication
    main-run-controller: true

app:
  input-directory: data/test/input
  output-directory: data/test/output
  processing-delay: 0

common:
  logging:
    log-path: logs
    level: DEBUG
    module-name: camel-learning
    pattern: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    max-history: 7
    max-file-size: 10485760  # 10MB
    enable-console: true
    enable-json: false
    package-levels:
      org.apache.camel: DEBUG
      com.example.routes: DEBUG
    async:
      enabled: false  # 测试环境禁用异步，方便调试
    alert:
      enabled: false  # 测试环境禁用告警