package com.example.routes;

import org.apache.camel.EndpointInject;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.test.spring.junit5.CamelSpringBootTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
@CamelSpringBootTest
public class FileProcessingRouteTest {

    @Autowired
    private ProducerTemplate producerTemplate;

    private static final String INPUT_DIR = "data/test/input";
    private static final String OUTPUT_TEXT_DIR = "data/test/output/text";
    private static final String OUTPUT_JSON_DIR = "data/test/output/json";
    private static final String OUTPUT_UNKNOWN_DIR = "data/test/output/unknown";

    @BeforeEach
    void setUp() throws IOException {
        // 清理并创建测试目录
        cleanAndCreateDirectories();
    }

    @Test
    void testProcessTextFile() throws Exception {
        // 创建测试文本文件
        String content = "Hello, this is a test file";
        Path testFile = createTestFile("test.txt", content);

        // 等待文件处理
        Thread.sleep(2000);

        // 验证文件是否被移动到正确的目录
        assertTrue(Files.exists(Paths.get(OUTPUT_TEXT_DIR, "test.txt")));
    }

    @Test
    void testProcessJsonFile() throws Exception {
        // 创建测试JSON文件
        String content = "{\"message\": \"Hello\"}";
        Path testFile = createTestFile("test.json", content);

        // 等待文件处理
        Thread.sleep(2000);

        // 验证文件是否被移动到正确的目录
        assertTrue(Files.exists(Paths.get(OUTPUT_JSON_DIR, "test.json")));
    }

    @Test
    void testProcessUnknownFile() throws Exception {
        // 创建未知类型文件
        String content = "Some content";
        Path testFile = createTestFile("test.unknown", content);

        // 等待文件处理
        Thread.sleep(2000);

        // 验证文件是否被移动到未知类型目录
        assertTrue(Files.exists(Paths.get(OUTPUT_UNKNOWN_DIR, "test.unknown")));
    }

    private void cleanAndCreateDirectories() throws IOException {
        // 清理旧目录
        deleteDirectory(new File(INPUT_DIR));
        deleteDirectory(new File(OUTPUT_TEXT_DIR));
        deleteDirectory(new File(OUTPUT_JSON_DIR));
        deleteDirectory(new File(OUTPUT_UNKNOWN_DIR));

        // 创建新目录
        Files.createDirectories(Paths.get(INPUT_DIR));
        Files.createDirectories(Paths.get(OUTPUT_TEXT_DIR));
        Files.createDirectories(Paths.get(OUTPUT_JSON_DIR));
        Files.createDirectories(Paths.get(OUTPUT_UNKNOWN_DIR));
    }

    private Path createTestFile(String fileName, String content) throws IOException {
        Path filePath = Paths.get(INPUT_DIR, fileName);
        Files.write(filePath, content.getBytes());
        return filePath;
    }

    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
} 