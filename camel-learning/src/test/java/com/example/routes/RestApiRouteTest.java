package com.example.routes;

import org.apache.camel.EndpointInject;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.test.spring.junit5.CamelSpringBootTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@CamelSpringBootTest
public class RestApiRouteTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    public void testHelloApi() {
        String response = restTemplate.getForObject(
                "http://localhost:" + port + "/api/hello",
                String.class);
        assertNotNull(response);
        assertEquals("{\"message\": \"Hello Camel!\"}", response);
    }

    @Test
    public void testEchoApi() {
        String response = restTemplate.getForObject(
                "http://localhost:" + port + "/api/echo/test",
                String.class);
        assertNotNull(response);
        assertEquals("{\"message\": \"Echo: test\"}", response);
    }
} 