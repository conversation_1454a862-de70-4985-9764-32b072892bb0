# Camel Learning Module

## 项目简介
这是一个基于 Apache Camel 和 Spring Boot 的学习项目，用于演示和实践 Apache Camel 的各种集成模式和功能特性。

## 技术栈
- Spring Boot 3.1.0
- Apache Camel 4.0.0
- Java 17
- Maven

## 功能特性

### 1. REST API 接口
位置：`src/main/java/com/example/routes/RestApiRoute.java`

#### 接口列表：
1. Hello API
   - 路径: GET `/api/hello`
   - 功能: 返回欢迎信息
   - 返回格式: JSON
   - 示例响应: `{"message": "Hello Camel!"}`
   - 调用示例: `curl http://localhost:8080/api/hello`

2. Echo API
   - 路径: GET `/api/echo/{message}`
   - 功能: 回显输入的消息
   - 参数: message - 要回显的消息内容
   - 返回格式: JSON
   - 示例响应: `{"message": "Echo: hello-world"}`
   - 调用示例: `curl http://localhost:8080/api/echo/hello-world`

### 2. 定时任务路由
位置：`src/main/java/com/example/routes/TimerRoute.java`

功能：
- 定时触发：每10秒执行一次
- 记录触发时间：输出当前时间到日志
- 路由ID：timer-route
- 日志格式：
  ```
  Timer triggered at 2024-01-23 12:00:00
  Current time is 2024-01-23 12:00:00
  ```

### 3. 文件处理路由
位置：`src/main/java/com/example/routes/FileProcessingRoute.java`

功能：
- 监控输入目录：`data/input`
- 根据文件类型自动分类处理
- 输出到不同目录：`data/output/{type}`

支持的文件类型：
- 文本文件 (.txt) -> data/output/text/
- JSON 文件 (.json) -> data/output/json/
- 其他类型文件 -> data/output/unknown/

### 4. 配置说明
位置：`src/main/resources/application.yml`

主要配置项：
```yaml
app:
  input-directory: data/input       # 输入目录
  output-directory: data/output     # 输出目录
  poll-delay: 5000                 # 文件扫描间隔（毫秒）
  processing-delay: 1000           # 处理延迟（毫秒）
```

## 使用说明

### 1. 启动应用
```bash
mvn spring-boot:run
```

### 2. 测试功能

#### REST API 测试
```bash
# 测试 hello 接口
curl http://localhost:8080/api/hello

# 测试 echo 接口
curl http://localhost:8080/api/echo/hello-world
```

#### 文件处理测试
1. 创建必要的目录：
```bash
mkdir -p data/input data/output/text data/output/json data/output/unknown
```

2. 将文件放入 data/input 目录，系统会自动处理并分类到相应的输出目录

## 监控和管理
- 应用健康检查：访问 `/actuator/health`
- 查看日志：`logs/camel-learning.log`
