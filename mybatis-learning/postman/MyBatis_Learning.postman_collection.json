{"info": {"name": "MyBatis Learning", "description": "MyBatis学习项目的API集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "用户管理", "item": [{"name": "创建用户", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:8080/api/users", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users"]}, "body": {"mode": "raw", "raw": "{\n    \"username\": \"test_user\",\n    \"password\": \"password123\",\n    \"email\": \"<EMAIL>\"\n}"}}}, {"name": "获取用户列表", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/users", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users"]}}}, {"name": "获取用户详情", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/users/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "1"]}}}, {"name": "更新用户", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:8080/api/users/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "1"]}, "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"status\": 2\n}"}}}, {"name": "删除用户", "request": {"method": "DELETE", "url": {"raw": "http://localhost:8080/api/users/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "1"]}}}, {"name": "搜索用户", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:8080/api/users/search", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "search"]}, "body": {"mode": "raw", "raw": "{\n    \"username\": \"test\",\n    \"status\": 1\n}"}}}, {"name": "分页查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:8080/api/users/page", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "page"]}, "body": {"mode": "raw", "raw": "{\n    \"pageNum\": 1,\n    \"pageSize\": 10,\n    \"username\": \"test\",\n    \"status\": 1,\n    \"startTime\": \"2025-01-01\",\n    \"endTime\": \"2025-12-31\",\n    \"sortField\": \"createTime\",\n    \"sortOrder\": \"DESC\"\n}"}}}]}, {"name": "数据生成", "item": [{"name": "生成测试数据", "request": {"method": "POST", "url": {"raw": "http://localhost:8080/api/generator/users?total=10000&batchSize=1000", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "generator", "users"], "query": [{"key": "total", "value": "10000", "description": "需要生成的总记录数"}, {"key": "batchSize", "value": "1000", "description": "每批次插入的记录数"}]}}}]}]}