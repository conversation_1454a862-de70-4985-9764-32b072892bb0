{"info": {"_postman_id": "performance-test-collection", "name": "MyBatis性能测试", "description": "用于测试MyBatis的SQL性能的接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "测试复杂SQL查询", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/test/slow-query", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["test", "slow-query"]}, "description": "测试复杂的SQL查询性能"}}, {"name": "测试递归SQL查询", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/test/recursive-query", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["test", "recursive-query"]}, "description": "测试递归SQL查询性能"}}, {"name": "查看监控面板", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/monitor", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["monitor"]}, "description": "访问SQL性能监控面板"}}]}