<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mybatis.mapper.UserMapper">
    <!-- Cache configuration -->
    <cache
            eviction="LRU"
            flushInterval="60000"
            size="512"
            readOnly="false"/>

    <!-- Result Map -->
    <resultMap id="UserResultMap" type="com.example.mybatis.model.User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="additionalInfo" column="additional_info" typeHandler="com.example.mybatis.config.JsonTypeHandler"/>
    </resultMap>

    <!-- Insert -->
    <insert id="insert" parameterType="com.example.mybatis.model.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (
            username, password, email, status, create_time, update_time, additional_info
        ) VALUES (
            #{username}, #{password}, #{email}, #{status}, #{createTime}, #{updateTime},
            #{additionalInfo,typeHandler=com.example.mybatis.config.JsonTypeHandler}
        )
    </insert>

    <!-- Select by ID -->
    <select id="selectById" resultMap="UserResultMap">
        SELECT * FROM user WHERE id = #{id}
    </select>

    <!-- Select all users -->
    <select id="selectAll" resultMap="UserResultMap">
        SELECT * FROM user ORDER BY id DESC
    </select>

    <!-- Select by condition -->
    <select id="selectByCondition" parameterType="com.example.mybatis.model.User" resultMap="UserResultMap">
        SELECT * FROM user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE #{username}
            </if>
            <if test="email != null and email != ''">
                AND email = #{email}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- Update -->
    <update id="update" parameterType="com.example.mybatis.model.User">
        UPDATE user
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="email != null">email = #{email},</if>
            <if test="status != null">status = #{status},</if>
            <if test="additionalInfo != null">
                additional_info = #{additionalInfo,typeHandler=com.example.mybatis.config.JsonTypeHandler},
            </if>
            update_time = #{updateTime}
        </set>
        WHERE id = #{id}
    </update>

    <!-- Delete -->
    <delete id="deleteById">
        DELETE FROM user WHERE id = #{id}
    </delete>

    <!-- 测试慢SQL：复杂条件查询 -->
    <select id="findUsersWithComplexConditions" resultMap="UserResultMap">
        WITH RECURSIVE 
        number_sequence AS (
            SELECT 1 as n
            UNION ALL
            SELECT n + 1 FROM number_sequence WHERE n &lt; 1000
        ),
        user_stats AS (
            SELECT 
                u.*,
                COUNT(*) OVER (PARTITION BY u.status) as users_in_status,
                ROW_NUMBER() OVER (PARTITION BY u.status ORDER BY u.create_time) as rank_in_status,
                CASE 
                    WHEN u.status = #{status} THEN 1
                    ELSE 0
                END as status_match
            FROM user u
            CROSS JOIN number_sequence ns
            WHERE ns.n &lt;= 10
            GROUP BY u.id, u.status, u.create_time
        )
        SELECT DISTINCT 
            us.id,
            us.username,
            us.email,
            us.status,
            us.create_time,
            us.update_time,
            us.additional_info,
            us.rank_in_status
        FROM user_stats us
        WHERE us.status = #{status}
        AND us.users_in_status >= #{minLoginCount}
        ORDER BY us.rank_in_status DESC
    </select>

    <!-- 测试慢SQL：递归查询用户层级 -->
    <select id="findUserHierarchy" resultMap="UserResultMap">
        WITH RECURSIVE 
        user_tree AS (
            SELECT 
                u.*,
                0 as level,
                CAST(u.id as CHAR(200)) as path
            FROM user u
            WHERE u.id = #{rootUserId}
            
            UNION ALL
            
            SELECT 
                u.*,
                ut.level + 1,
                CONCAT(ut.path, ',', u.id)
            FROM user u
            INNER JOIN user_tree ut ON u.parent_id = ut.id
            CROSS JOIN (
                SELECT 1 as n FROM user LIMIT 100
            ) as extra
            WHERE ut.level &lt; 10
        ),
        user_metrics AS (
            SELECT 
                ut.*,
                COUNT(*) OVER (PARTITION BY ut.level) as users_at_level,
                ROW_NUMBER() OVER (PARTITION BY ut.level ORDER BY ut.create_time) as rank_at_level
            FROM user_tree ut
        )
        SELECT DISTINCT
            um.id,
            um.username,
            um.email,
            um.status,
            um.create_time,
            um.update_time,
            um.additional_info,
            um.path,
            um.level,
            um.rank_at_level
        FROM user_metrics um
        ORDER BY um.path, um.level, um.rank_at_level
    </select>

    <!-- Count for pagination -->
    <select id="countByCondition" parameterType="com.example.mybatis.model.UserQueryRequest" resultType="long">
        SELECT COUNT(*) FROM user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE #{username}
            </if>
            <if test="email != null and email != ''">
                AND email = #{email}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="startTime != null and startTime != ''">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- Page query with dynamic sorting -->
    <select id="selectPage" parameterType="com.example.mybatis.model.UserQueryRequest" resultMap="UserResultMap">
        SELECT * FROM user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE #{username}
            </if>
            <if test="email != null and email != ''">
                AND email = #{email}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="startTime != null and startTime != ''">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        <if test="sortField != null and sortField != ''">
            ORDER BY 
            <choose>
                <when test="sortField == 'username'">username</when>
                <when test="sortField == 'email'">email</when>
                <when test="sortField == 'status'">status</when>
                <when test="sortField == 'createTime'">create_time</when>
                <otherwise>id</otherwise>
            </choose>
            <if test="sortOrder != null and sortOrder.toUpperCase() == 'DESC'">
                DESC
            </if>
            <if test="sortOrder == null or sortOrder.toUpperCase() != 'DESC'">
                ASC
            </if>
        </if>
        <if test="sortField == null or sortField == ''">
            ORDER BY id DESC
        </if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- Batch Insert -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user (
            username, password, email, status, create_time, update_time, additional_info
        ) VALUES 
        <foreach collection="list" item="user" separator=",">
            (
                #{user.username}, 
                #{user.password}, 
                #{user.email}, 
                #{user.status}, 
                #{user.createTime}, 
                #{user.updateTime},
                #{user.additionalInfo,typeHandler=com.example.mybatis.config.JsonTypeHandler}
            )
        </foreach>
    </insert>
</mapper> 