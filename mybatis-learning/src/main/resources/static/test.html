<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>同步与异步请求测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .container {
            display: flex;
            gap: 20px;
            justify-content: center;
        }
        .panel {
            width: 380px;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
            flex: none;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 3px;
            min-height: 100px;
            max-height: 400px;
            overflow-y: auto;
            word-break: break-all;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        #counter {
            font-size: 24px;
            margin: 20px 0;
            text-align: center;
        }
        .explanation {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <h1>同步与异步请求测试页面</h1>
    
    <div class="explanation">
        <h3>说明：</h3>
        <p>1. 下面有一个计数器会每秒递增，用来演示页面是否被阻塞</p>
        <p>2. 同步请求会阻塞页面，计数器会停止增加</p>
        <p>3. 异步请求不会阻塞页面，计数器会继续增加</p>
        .
        <p>4. 每个请求都会在服务器端sleep 5秒，以模拟耗时操作</p>
    </div>

    <div id="counter">计数器：0</div>

    <div class="container">
        <div class="panel">
            <h2>同步请求</h2>
            <div class="status" id="syncStatus">当前状态：准备就绪</div>
            <p>这个请求会阻塞页面，计数器会停止</p>
            <button onclick="testSync()" id="syncButton">发送同步请求</button>
            <div id="syncResult" class="result"></div>
        </div>
        <div class="panel">
            <h2>异步请求</h2>
            <div class="status" id="asyncStatus">当前状态：准备就绪</div>
            <p>这个请求不会阻塞页面，计数器会继续运行</p>
            <button onclick="testAsync()" id="asyncButton">发送异步请求</button>
            <div id="asyncResult" class="result"></div>
        </div>
    </div>

    <script>
        // 计数器
        let count = 0;
        setInterval(() => {
            count++;
            document.getElementById('counter').textContent = `计数器：${count}`;
        }, 1000);

        // 更新状态显示
        function updateStatus(type, status) {
            document.getElementById(type + 'Status').textContent = `当前状态：${status}`;
        }

        // 同步请求测试
        function testSync() {
            const syncButton = document.getElementById('syncButton');
            const syncResult = document.getElementById('syncResult');
            
            // 禁用按钮
            syncButton.disabled = true;
            updateStatus('sync', '请求中...');
            
            syncResult.innerHTML = `
                <div class="loading">
                    <p>正在发送同步请求...</p>
                    <p>服务器将sleep 5秒，观察：</p>
                    <ul>
                        <li>计数器是否停止增加？</li>
                        <li>页面是否完全无法操作？</li>
                        <li>另一个按钮是否可以点击？</li>
                    </ul>
                </div>
            `;
            
            const startTime = new Date().getTime();
            const startCount = count;  // 记录开始时的计数

            try {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', '/api/users/1', false);  // 同步请求
                xhr.send();
                
                const endTime = new Date().getTime();
                const duration = endTime - startTime;
                const countDiff = count - startCount;  // 计算期间计数器增加了多少
                
                if (xhr.status === 200) {
                    const data = JSON.parse(xhr.responseText);
                    updateStatus('sync', '请求完成');
                    syncResult.innerHTML = `
                        <div class="success">
                            <p>同步请求完成！</p>
                            <p>耗时：${duration}ms</p>
                            <p>计数器增加了：${countDiff}</p>
                            <p>观察结果：</p>
                            <ul>
                                <li>请求过程中计数器应该停止增加</li>
                                <li>整个页面应该完全无法操作</li>
                                <li>这就是同步请求的特点：会阻塞整个页面</li>
                            </ul>
                            <p>服务器响应数据：</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(xhr.responseText);
                }
            } catch (error) {
                updateStatus('sync', '请求失败');
                syncResult.innerHTML = `
                    <div class="error">
                        <p>请求失败</p>
                        <p>错误信息：${error.message}</p>
                    </div>
                `;
            } finally {
                syncButton.disabled = false;
            }
        }

        // 异步请求测试
        function testAsync() {
            const asyncButton = document.getElementById('asyncButton');
            const asyncResult = document.getElementById('asyncResult');
            
            // 禁用按钮
            asyncButton.disabled = true;
            updateStatus('async', '请求中...');
            
            asyncResult.innerHTML = `
                <div class="loading">
                    <p>正在发送异步请求...</p>
                    <p>观察：</p>
                    <ul>
                        <li>计数器是否继续增加？</li>
                        <li>页面是否可以正常操作？</li>
                        <li>另一个按钮是否可以点击？</li>
                    </ul>
                </div>
            `;

            const startTime = new Date().getTime();
            const startCount = count;  // 记录开始时的计数

            // 使用fetch代替XMLHttpRequest，因为它更适合处理Promise
            fetch('/api/users/page', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    pageNum: 1,
                    pageSize: 10,
                    status: 1
                })
            })
            .then(response => response.json())
            .then(data => {
                const endTime = new Date().getTime();
                const duration = endTime - startTime;
                const countDiff = count - startCount;

                updateStatus('async', '请求完成');
                asyncResult.innerHTML = `
                    <div class="success">
                        <p>异步请求完成！</p>
                        <p>耗时：${duration}ms</p>
                        <p>计数器增加了：${countDiff}</p>
                        <p>观察结果：</p>
                        <ul>
                            <li>请求过程中计数器继续增加</li>
                            <li>整个页面可以正常操作</li>
                            <li>这就是异步请求的特点：不会阻塞页面</li>
                        </ul>
                        <p>服务器响应数据：</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            })
            .catch(error => {
                updateStatus('async', '请求失败');
                asyncResult.innerHTML = `
                    <div class="error">
                        <p>请求失败</p>
                        <p>错误信息：${error.message}</p>
                    </div>
                `;
            })
            .finally(() => {
                asyncButton.disabled = false;
            });
        }
    </script>
</body>
</html> 