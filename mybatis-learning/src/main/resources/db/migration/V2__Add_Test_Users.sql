-- 插入测试用户数据
INSERT INTO user (username, password, email, status, create_time, update_time, additional_info)
VALUES
    ('admin', '$2a$10$rDmFN6ZJvwFqMz2Y7HCC4O8KKwNZnTqvQV.YyxVYd.hHwsLvXjUu2', '<EMAIL>', 1, NOW(), NOW(), '{"role": "admin", "department": "IT"}'),
    ('user1', '$2a$10$rDmFN6ZJvwFqMz2Y7HCC4O8KKwNZnTqvQV.YyxVYd.hHwsLvXjUu2', '<EMAIL>', 1, NOW(), NOW(), '{"role": "user", "department": "Sales"}'),
    ('user2', '$2a$10$rDmFN6ZJvwFqMz2Y7HCC4O8KKwNZnTqvQV.YyxVYd.hHwsLvXjUu2', '<EMAIL>', 1, NOW(), NOW(), '{"role": "user", "department": "Marketing"}'),
    ('user3', '$2a$10$rDmFN6ZJvwFqMz2Y7HCC4O8KKwNZnTqvQV.YyxVYd.hHwsLvXjUu2', '<EMAIL>', 0, NOW(), NOW(), '{"role": "user", "department": "HR"}'),
    ('manager1', '$2a$10$rDmFN6ZJvwFqMz2Y7HCC4O8KKwNZnTqvQV.YyxVYd.hHwsLvXjUu2', '<EMAIL>', 1, NOW(), NOW(), '{"role": "manager", "department": "Sales"}'),
    ('test_user1', '$2a$10$NlBC3KQHKVXsC5sVyxRxYOYgQzWGBCX8o.vK8Juh/0KuU6Qj78Z2a', '<EMAIL>', 1, NOW(), NOW(), '{"role": "user", "description": "测试用户1"}'),
    ('test_user2', '$2a$10$NlBC3KQHKVXsC5sVyxRxYOYgQzWGBCX8o.vK8Juh/0KuU6Qj78Z2a', '<EMAIL>', 1, NOW(), NOW(), '{"role": "user", "description": "测试用户2"}'),
    ('test_user3', '$2a$10$NlBC3KQHKVXsC5sVyxRxYOYgQzWGBCX8o.vK8Juh/0KuU6Qj78Z2a', '<EMAIL>', 1, NOW(), NOW(), '{"role": "user", "description": "测试用户3"}'),
    ('test_user4', '$2a$10$NlBC3KQHKVXsC5sVyxRxYOYgQzWGBCX8o.vK8Juh/0KuU6Qj78Z2a', '<EMAIL>', 1, NOW(), NOW(), '{"role": "user", "description": "测试用户4"}'),
    ('test_user5', '$2a$10$NlBC3KQHKVXsC5sVyxRxYOYgQzWGBCX8o.vK8Juh/0KuU6Qj78Z2a', '<EMAIL>', 1, NOW(), NOW(), '{"role": "user", "description": "测试用户5"}'); 