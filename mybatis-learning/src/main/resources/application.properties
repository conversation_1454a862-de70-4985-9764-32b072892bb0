# ========================
# = 环境配置
# ========================
# 激活开发环境配置文件（application-dev.properties）
spring.profiles.active=dev
# 引入数据库连接池配置（application-hikari.properties）
spring.profiles.include=hikari

# ========================
# = 服务器配置
# ========================
# 应用服务器端口号
server.port=8080

# 异步请求配置
spring.mvc.async.request-timeout=30000
spring.task.execution.pool.core-size=10
spring.task.execution.pool.max-size=50
spring.task.execution.pool.queue-capacity=500
spring.task.execution.thread-name-prefix=AsyncThread-

# ========================
# = 数据库基础配置
# ========================
# 数据库连接URL，包含以下参数：
# - useUnicode=true：使用Unicode字符编码
# - characterEncoding=utf-8：使用UTF-8字符编码
# - useSSL=false：不使用SSL连接
# - serverTimezone=UTC：服务器时区设置为UTC
# - allowPublicKeyRetrieval=true：允许客户端从服务器获取公钥
# 使用P6Spy代理
spring.datasource.driver-class-name=com.p6spy.engine.spy.P6SpyDriver
# 数据源类型
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.url=****************************************************************************************************************************************************************
# 数据库用户名
spring.datasource.username=root
# 数据库密码
spring.datasource.password=123456

# ========================
# = SQL初始化配置
# ========================
# SQL初始化模式：never表示不执行初始化
spring.sql.init.mode=never
# 禁用Spring Boot的SQL初始化功能
spring.sql.init.enabled=false
# 设置SQL平台为MySQL
spring.sql.init.platform=mysql

# ========================
# = MyBatis配置
# ========================
# 指定Mapper XML文件位置
mybatis.mapper-locations=classpath:mapper/*.xml
# 指定实体类包路径，用于类型别名
mybatis.type-aliases-package=com.example.mybatis.model
# 开启驼峰命名转换，如：数据库字段user_name自动映射到实体类userName属性
mybatis.configuration.map-underscore-to-camel-case=true
# 开启MyBatis的二级缓存
mybatis.configuration.cache-enabled=true
# 开启延迟加载功能
mybatis.configuration.lazy-loading-enabled=true
# 关闭积极的延迟加载，即按需加载
mybatis.configuration.aggressive-lazy-loading=false
# 设置自动映射级别为FULL，会自动映射所有属性
mybatis.configuration.auto-mapping-behavior=full

# ========================
# = Common模块配置
# ========================
# 模块启用配置
common.logging-enabled=true
common.utils-ena
common.constants-enabled=false
common.exceptions-enabled=false
common.config-enabled=false
common.db-enabled=false
common.cache-enabled=false
common.api-enabled=false
common.security-enabled=false
common.messaging-enabled=false
common.scheduler-enabled=false
common.thirdparty-enabled=false

# 日志配置
common.logging.mode=async
common.logging.async.core-pool-size=2
common.logging.async.max-pool-size=5
common.logging.async.queue-capacity=100

# 日志文件配置
common.logging.file.path=logs
common.logging.file.max-file-size=100MB
common.logging.file.max-history=30
common.logging.file.total-size-cap=10GB

# 日志格式配置
common.logging.format.pattern=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n
common.logging.format.show-mdc=true
common.logging.format.show-thread=true
common.logging.format.show-class-and-method=true

# 敏感信息配置
common.logging.sensitive.enabled=true
common.logging.sensitive.mask-char=*
common.logging.sensitive.fields=password,pwd,secret,credential,token,cardNo,idCard,phone,mobile,email,username,realName,address
common.logging.sensitive.mask-rules.phone=$1****$2
common.logging.sensitive.mask-rules.email=$1****@$2
common.logging.sensitive.mask-rules.idCard=$1****$2
common.logging.sensitive.mask-rules.username=$1**$2
common.logging.sensitive.mask-rules.realName=$1*$2

# P6Spy配置
common.p6spy.enabled=true
decorator.datasource.p6spy.enable-logging=true
decorator.datasource.p6spy.multiline=true
decorator.datasource.p6spy.logging=slf4j

# 关闭MyBatis的SQL日志，因为已经有P6Spy了
logging.level.com.example.mybatis.mapper=OFF

# Spring框架的日志级别设置为INFO
logging.level.org.springframework=INFO
# 启用控制台彩色输出
spring.output.ansi.enabled=ALWAYS

# 基础日志配置
logging.charset.console=UTF-8
logging.charset.file=UTF-8

# ========================
# = Flyway数据库版本管理配置
# ========================
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.validate-on-migrate=true
spring.flyway.url=**********************************************************************************************************************************************************
spring.flyway.user=root
spring.flyway.password=123456

# ========================
# = Actuator监控配置
# ========================
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.prometheus.enabled=true
management.metrics.enable.all=true

