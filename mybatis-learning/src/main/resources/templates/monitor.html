<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>SQL性能监控面板</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            margin: 8px;
            background-color: #fafafa;
            font-size: 12px;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: white;
            padding: 12px;
            border-radius: 3px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        .chart-container {
            margin-bottom: 12px;
            background-color: white;
            padding: 8px;
            border-radius: 3px;
            border: 1px solid #e8e8e8;
        }
        .chart-title {
            font-size: 13px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #1976d2;
            padding-left: 4px;
            border-left: 2px solid #1976d2;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        canvas {
            width: 100% !important;
            height: 160px !important;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 8px;
            font-size: 11px;
        }
        th, td {
            padding: 4px 6px;
            text-align: left;
            border-bottom: 1px solid #eee;
            white-space: nowrap;
        }
        th {
            background-color: #f5f7fa;
            font-weight: 500;
            font-size: 11px;
            color: #666;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .info-panel {
            background-color: #f3f8ff;
            padding: 6px 10px;
            border-radius: 3px;
            margin-bottom: 12px;
            font-size: 11px;
            border: 1px solid #e3ecfd;
        }
        .info-panel p {
            margin: 0;
            color: #2962ff;
        }
        .chart-wrapper {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
        }
        .chart-box {
            flex: 1;
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 3px;
            padding: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="info-panel">
            <p>此面板显示系统中SQL查询和HTTP请求的性能指标，每5秒自动更新一次。</p>
        </div>
        
        <div class="chart-wrapper">
            <div class="chart-box">
                <div class="chart-title">SQL执行统计</div>
                <canvas id="sqlChart"></canvas>
            </div>
            
            <div class="chart-box">
                <div class="chart-title">HTTP请求统计</div>
                <canvas id="httpChart"></canvas>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">
                <span>详细统计数据</span>
            </div>
            <table id="metricsTable">
                <thead>
                    <tr>
                        <th>接口</th>
                        <th>指标类型</th>
                        <th>值</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 格式化时间显示
        function formatTime(ms) {
            if (ms === undefined || ms === null) return "0ms";
            if (ms < 1) return "< 1ms";
            if (ms < 1000) return ms.toFixed(2) + "ms";
            return (ms / 1000).toFixed(2) + "s";
        }

        // 格式化接口名称
        function formatEndpoint(endpoint) {
            if (!endpoint) return "";
            const parts = endpoint.split('{');
            if (parts.length > 1) {
                const details = parts[1].split('}')[0].split(',');
                const methodName = details.find(d => d.includes('method='))?.split('=')[1] || '';
                const className = details.find(d => d.includes('class='))?.split('=')[1] || '';
                return `${methodName} (${className})`;
            }
            return endpoint;
        }

        // 图表配置
        const chartConfig = {
            sql: {
                type: 'bar',
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            min: 0,  // 设置最小值
                            suggestedMax: 100,  // 建议最大值
                            title: {
                                display: true,
                                text: '执行时间 (ms)',
                                font: { size: 10 }
                            },
                            ticks: {
                                font: { size: 9 },
                                stepSize: 20,  // 设置刻度步长
                                callback: function(value) {
                                    return value + 'ms';
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false  // 隐藏网格线
                            },
                            ticks: {
                                maxRotation: 0,  // 不旋转标签
                                minRotation: 0,
                                font: { size: 9 }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                font: { size: 10 },
                                boxWidth: 20,
                                padding: 8
                            }
                        }
                    },
                    animation: false  // 禁用动画
                }
            },
            http: {
                type: 'bar',
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            min: 0,  // 设置最小值
                            suggestedMax: 100,  // 建议最大值
                            title: {
                                display: true,
                                text: '响应时间 (ms)',
                                font: { size: 10 }
                            },
                            ticks: {
                                font: { size: 9 },
                                stepSize: 20,  // 设置刻度步长
                                callback: function(value) {
                                    return value + 'ms';
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false  // 隐藏网格线
                            },
                            ticks: {
                                maxRotation: 0,  // 不旋转标签
                                minRotation: 0,
                                font: { size: 9 }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                font: { size: 10 },
                                boxWidth: 20,
                                padding: 8
                            }
                        }
                    },
                    animation: false  // 禁用动画
                }
            }
        };

        let sqlChart = null;
        let httpChart = null;

        function updateCharts() {
            $.get('/monitor/metrics', function(data) {
                updateSqlChart(data);
                updateHttpChart(data);
                updateTable(data);
            });
        }

        function updateSqlChart(metrics) {
            // 只获取SQL执行相关的指标
            const sqlData = Object.entries(metrics)
                .filter(([key]) => key.startsWith('sql_execution'))
                .map(([key, value]) => {
                    const tags = key.split('{')[1]?.split('}')[0].split(',');
                    const method = tags.find(t => t.startsWith('method='))?.split('=')[1] || '';
                    const type = tags.find(t => t.startsWith('type='))?.split('=')[1] || '';
                    return {
                        name: `${method}`,  // 只显示方法名
                        type: type,  // SQL类型
                        mean: Math.max(0.1, Math.round(value.mean || 0)),  // 确保最小值为0.1ms
                        max: Math.max(0.1, Math.round(value.max || 0))  // 确保最小值为0.1ms
                    };
                })
                .sort((a, b) => b.mean - a.mean)
                .slice(0, 5);

            // 动态计算合适的最大值
            const maxValue = Math.max(...sqlData.flatMap(d => [d.mean, d.max]));
            const suggestedMax = Math.ceil(maxValue / 20) * 20;  // 向上取整到最近的20的倍数

            const chartData = {
                labels: sqlData.map(d => d.name),
                datasets: [
                    {
                        label: '平均执行时间(ms)',
                        data: sqlData.map(d => d.mean),
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1,
                        barPercentage: 0.7,
                        categoryPercentage: 0.9
                    },
                    {
                        label: '最大执行时间(ms)',
                        data: sqlData.map(d => d.max),
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1,
                        barPercentage: 0.7,
                        categoryPercentage: 0.9
                    }
                ]
            };

            const options = {
                ...chartConfig.sql.options,
                scales: {
                    ...chartConfig.sql.options.scales,
                    y: {
                        ...chartConfig.sql.options.scales.y,
                        suggestedMax: suggestedMax,
                        ticks: {
                            font: { size: 9 },
                            stepSize: Math.max(1, Math.floor(suggestedMax / 5)),  // 动态计算步长
                            callback: function(value) {
                                return value + 'ms';
                            }
                        }
                    }
                },
                plugins: {
                    ...chartConfig.sql.options.plugins,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const dataIndex = context.dataIndex;
                                const data = sqlData[dataIndex];
                                return [
                                    `${context.dataset.label}: ${context.parsed.y}ms`,
                                    `SQL类型: ${data.type}`
                                ];
                            }
                        }
                    }
                }
            };

            if (!sqlChart) {
                const ctx = document.getElementById('sqlChart');
                sqlChart = new Chart(ctx, {
                    type: chartConfig.sql.type,
                    data: chartData,
                    options: options
                });
            } else {
                sqlChart.data = chartData;
                sqlChart.options = options;
                sqlChart.update('none');
            }
        }

        function updateHttpChart(metrics) {
            // 只获取Controller相关的指标
            const httpData = Object.entries(metrics)
                .filter(([key]) => key.includes('Controller'))
                .map(([key, value]) => {
                    const tags = key.split('{')[1]?.split('}')[0].split(',');
                    const method = tags.find(t => t.startsWith('method='))?.split('=')[1] || '';
                    const className = tags.find(t => t.startsWith('class='))?.split('=')[1] || '';
                    return {
                        name: `${method}`,
                        className: className,
                        mean: Math.max(0.1, Math.round(value.mean || 0)),  // 确保最小值为0.1ms
                        count: value.count || 0
                    };
                })
                .sort((a, b) => b.mean - a.mean)
                .slice(0, 5);

            // 动态计算合适的最大值
            const maxValue = Math.max(...httpData.map(d => d.mean));
            const suggestedMax = Math.ceil(maxValue / 20) * 20;  // 向上取整到最近的20的倍数

            const chartData = {
                labels: httpData.map(d => d.name),
                datasets: [{
                    label: '平均响应时间(ms)',
                    data: httpData.map(d => d.mean),
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1,
                    barPercentage: 0.7,
                    categoryPercentage: 0.9
                }]
            };

            const options = {
                ...chartConfig.http.options,
                scales: {
                    ...chartConfig.http.options.scales,
                    y: {
                        ...chartConfig.http.options.scales.y,
                        suggestedMax: suggestedMax,
                        ticks: {
                            font: { size: 9 },
                            stepSize: Math.max(1, Math.floor(suggestedMax / 5)),  // 动态计算步长
                            callback: function(value) {
                                return value + 'ms';
                            }
                        }
                    }
                }
            };

            if (!httpChart) {
                const ctx = document.getElementById('httpChart');
                httpChart = new Chart(ctx, {
                    type: chartConfig.http.type,
                    data: chartData,
                    options: options
                });
            } else {
                httpChart.data = chartData;
                httpChart.options = options;
                httpChart.update('none');
            }
        }

        function updateTable(data) {
            const tbody = $('#metricsTable tbody');
            tbody.empty();
            
            Object.entries(data)
                .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
                .forEach(([key, metrics]) => {
                    Object.entries(metrics).forEach(([metric, value]) => {
                        tbody.append(`
                            <tr>
                                <td>${key}</td>
                                <td>${metric}</td>
                                <td>${metric === 'count' ? value : formatTime(value)}</td>
                            </tr>
                        `);
                    });
                });
        }

        // 初始化
        $(document).ready(function() {
            updateCharts();
            setInterval(updateCharts, 5000);
        });
    </script>
</body>
</html> 