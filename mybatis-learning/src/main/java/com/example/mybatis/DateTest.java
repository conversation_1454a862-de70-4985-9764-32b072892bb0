package com.example.mybatis;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateTest {
    public static void main(String[] args) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(2019, Calendar.DECEMBER, 31);  // 使用12月31日的日期
        Date strDate = calendar.getTime();
        
        // 正常的公历年份格式
        DateFormat formatCalendarYear = new SimpleDateFormat("yyyy-MM-dd");
        System.out.println("2019-12-31 to yyyy-MM-dd: " + formatCalendarYear.format(strDate));

        // 错误的 ISO 周年度格式
        DateFormat formatIsoWeekYear = new SimpleDateFormat("YYYY-MM-dd");
        System.out.println("2019-12-31 to YYYY-MM-dd: " + formatIsoWeekYear.format(strDate));
    }
}
