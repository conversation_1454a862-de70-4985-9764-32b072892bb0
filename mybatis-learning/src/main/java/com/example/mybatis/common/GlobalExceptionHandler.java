package com.example.mybatis.common;

import com.example.common.logging.handler.LogHandler;
import com.example.common.logging.model.LogLevel;
import com.example.common.logging.model.LogMessage;
import com.example.mybatis.common.exception.AsyncTimeoutException;
import com.example.mybatis.common.exception.UserNotFoundException;
import com.example.mybatis.common.exception.BusinessException;
import org.slf4j.MDC;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.UUID;

@Component
@RestControllerAdvice
public class GlobalExceptionHandler {

    private final LogHandler logHandler;

    @Autowired
    public GlobalExceptionHandler(LogHandler logHandler) {
        this.logHandler = logHandler;
    }

    @ExceptionHandler(UserNotFoundException.class)
    public ApiResponse<Void> handleUserNotFoundException(UserNotFoundException e) {
        return ApiResponse.error(404, e.getMessage());
    }

    @ExceptionHandler(DataAccessException.class)
    public ApiResponse<Void> handleDataAccessException(DataAccessException e) {
        LogMessage logMessage = buildErrorLogMessage("数据访问异常", e);
        logHandler.handle(logMessage);
        return ApiResponse.error(500, "数据库操作失败");
    }

    @ExceptionHandler(AsyncTimeoutException.class)
    public ApiResponse<Void> handleAsyncTimeoutException(AsyncTimeoutException e) {
        LogMessage logMessage = buildErrorLogMessage("异步操作超时", e);
        logHandler.handle(logMessage);
        return ApiResponse.error(408, e.getMessage());
    }

    @ExceptionHandler(BusinessException.class)
    public ApiResponse<Void> handleBusinessException(BusinessException e) {
        return ApiResponse.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public ApiResponse<Void> handleException(Exception e) {
        LogMessage logMessage = buildErrorLogMessage("未知异常", e);
        logHandler.handle(logMessage);
        return ApiResponse.error(500, "服务器内部错误");
    }

    private LogMessage buildErrorLogMessage(String message, Exception e) {
        String traceId = MDC.get("traceId");
        if (traceId == null) {
            traceId = UUID.randomUUID().toString().replace("-", "").substring(0, 6);
            MDC.put("traceId", traceId);
        }

        StackTraceElement[] stackTrace = e.getStackTrace();
        String className = stackTrace.length > 0 ? stackTrace[0].getClassName() : "";
        String methodName = stackTrace.length > 0 ? stackTrace[0].getMethodName() : "";
        int lineNumber = stackTrace.length > 0 ? stackTrace[0].getLineNumber() : 0;

        LogMessage logMessage = LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.ERROR)
                .message(message)
                .throwable(e)
                .timestamp(LocalDateTime.now())
                .className(className)
                .methodName(methodName)
                .lineNumber(lineNumber)
                .threadName(Thread.currentThread().getName())
                .tags(new HashMap<>())
                .build();

        // 添加额外的上下文信息
        logMessage.getTags().put("exceptionClass", e.getClass().getName());
        logMessage.getTags().put("exceptionMessage", e.getMessage());
        logMessage.getTags().put("traceId", traceId);

        return logMessage;
    }
} 