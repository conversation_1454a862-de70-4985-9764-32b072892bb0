package com.example.mybatis.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * User entity to demonstrate various MyBatis features
 * Including:
 * 1. Basic CRUD operations
 * 2. Type handlers (for handling LocalDateTime)
 * 3. Result mapping
 * 4. Dynamic SQL
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * User ID - Primary Key
     */
    private Long id;

    /**
     * Username - Unique
     */
    private String username;

    /**
     * Password - Encrypted
     */
    private String password;

    /**
     * Email address
     */
    private String email;

    /**
     * User status (0: inactive, 1: active)
     */
    private Integer status;

    /**
     * Creation time
     */
    private LocalDateTime createTime;

    /**
     * Last update time
     */
    private LocalDateTime updateTime;

    /**
     * Additional user information in JSON format
     * Demonstrates TypeHandler usage
     */
    private Object additionalInfo;
} 