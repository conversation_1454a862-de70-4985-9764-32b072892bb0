package com.example.mybatis.model;

import lombok.Data;

@Data
public class PageRequest {
    private int pageNum = 1;  // 当前页码，默认第1页
    private int pageSize = 10;  // 每页大小，默认10条
    private String sortField;  // 排序字段
    private String sortOrder;  // 排序方向（ASC/DESC）
    
    // 验证并修正分页参数
    public void validate() {
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 100) {  // 限制最大每页数量
            pageSize = 100;
        }
    }
    
    // 获取偏移量
    public int getOffset() {
        return (pageNum - 1) * pageSize;
    }
} 