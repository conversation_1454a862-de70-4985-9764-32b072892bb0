package com.example.mybatis.model;

import lombok.Data;
import java.util.List;

@Data
public class PageResult<T> {
    private List<T> list;        // 当前页数据列表
    private long total;          // 总记录数
    private int pageNum;         // 当前页码
    private int pageSize;        // 每页大小
    private int totalPages;      // 总页数
    private boolean hasNext;     // 是否有下一页
    private boolean hasPrevious; // 是否有上一页

    public PageResult(List<T> list, long total, PageRequest pageRequest) {
        this.list = list;
        this.total = total;
        this.pageNum = pageRequest.getPageNum();
        this.pageSize = pageRequest.getPageSize();
        this.totalPages = (int) Math.ceil((double) total / pageSize);
        this.hasNext = pageNum < totalPages;
        this.hasPrevious = pageNum > 1;
    }
} 