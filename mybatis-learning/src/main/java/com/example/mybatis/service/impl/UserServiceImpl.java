package com.example.mybatis.service.impl;

import com.example.mybatis.mapper.UserMapper;
import com.example.mybatis.model.User;
import com.example.mybatis.model.UserQueryRequest;
import com.example.mybatis.model.PageResult;
import com.example.mybatis.service.UserService;
import com.example.common.logging.annotation.Logging;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;

    public UserServiceImpl(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @Override
    @Logging
    public User createUser(User user) {
        userMapper.insert(user);
        return user;
    }

    @Override
    @Logging
    public User getUserById(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    @Logging
    public List<User> getAllUsers() {
        return userMapper.selectAll();
    }

    @Override
    @Logging
    public List<User> searchUsers(User criteria) {
        return userMapper.selectByCondition(criteria);
    }

    @Override
    @Logging
    public User updateUser(Long id, User user) {
        user.setId(id);
        userMapper.update(user);
        return user;
    }

    @Override
    @Logging
    public boolean deleteUser(Long id) {
        userMapper.deleteById(id);
        return true;
    }

    @Override
    @Logging
    public PageResult<User> getPageList(UserQueryRequest request) {
        List<User> list = userMapper.selectPage(request);
        long total = userMapper.countByCondition(request);
        return new PageResult<>(list, total, request);
    }
} 