package com.example.mybatis.service;

import com.example.mybatis.common.exception.AsyncTimeoutException;
import com.example.mybatis.model.User;
import com.example.mybatis.model.PageResult;
import com.example.mybatis.model.UserQueryRequest;
import java.util.List;

/**
 * 可中断的查询服务
 * 支持在执行过程中检查中断状态
 */
public interface InterruptibleQueryService {
    
    /**
     * 可中断的搜索用户操作
     * @param criteria 搜索条件
     * @return 用户列表
     * @throws InterruptedException 如果操作被中断
     */
    List<User> searchUsersInterruptibly(User criteria) throws InterruptedException;
    
    /**
     * 可中断的获取所有用户操作
     * @return 用户列表
     * @throws InterruptedException 如果操作被中断
     */
    List<User> getAllUsersInterruptibly() throws InterruptedException;
    
    /**
     * 可中断的分页查询操作
     * @param request 分页请求
     * @return 分页结果
     * @throws InterruptedException 如果操作被中断
     */
    PageResult<User> getPageListInterruptibly(UserQueryRequest request) throws InterruptedException;

    /**
     * 带超时的用户查询操作
     * @param timeoutMillis 超时时间（毫秒）
     * @return 用户列表
     * @throws AsyncTimeoutException 如果操作超时
     */
    List<User> queryUsersWithTimeout(long timeoutMillis) throws AsyncTimeoutException;
} 