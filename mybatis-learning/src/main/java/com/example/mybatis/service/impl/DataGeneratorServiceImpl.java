package com.example.mybatis.service.impl;

import com.example.mybatis.mapper.UserMapper;
import com.example.mybatis.model.User;
import com.example.mybatis.service.DataGeneratorService;
import com.github.javafaker.Faker;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@Service
public class DataGeneratorServiceImpl implements DataGeneratorService {
    
    private final UserMapper userMapper;
    private final Faker faker;
    
    public DataGeneratorServiceImpl(UserMapper userMapper) {
        this.userMapper = userMapper;
        this.faker = new Faker(new Locale("zh-CN"));
    }
    
    @Override
    @Transactional
    public void generateTestData(int batchSize, int totalCount) {
        int batches = (totalCount + batchSize - 1) / batchSize;
        
        for (int i = 0; i < batches; i++) {
            int currentBatchSize = Math.min(batchSize, totalCount - i * batchSize);
            List<User> users = generateUsers(currentBatchSize);
            userMapper.batchInsert(users);
        }
    }
    
    private List<User> generateUsers(int count) {
        List<User> users = new ArrayList<>(count);
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 0; i < count; i++) {
            User user = User.builder()
                    .username(faker.name().username())
                    .password(faker.internet().password())
                    .email(faker.internet().emailAddress())
                    .status(1)
                    .createTime(now)
                    .updateTime(now)
                    .build();
            users.add(user);
        }
        return users;
    }
} 