package com.example.mybatis.service;

import com.example.mybatis.model.User;
import com.example.mybatis.model.UserQueryRequest;
import com.example.mybatis.model.PageResult;
import java.util.List;

public interface UserService {
    User createUser(User user);
    User getUserById(Long id);
    List<User> getAllUsers();
    List<User> searchUsers(User criteria);
    User updateUser(Long id, User user);
    boolean deleteUser(Long id);
    
    // 新增分页查询方法
    PageResult<User> getPageList(UserQueryRequest request);
} 