package com.example.mybatis.service.impl;

import com.example.common.logging.handler.LogHandler;
import com.example.common.logging.model.LogLevel;
import com.example.common.logging.model.LogMessage;
import com.example.mybatis.common.exception.AsyncTimeoutException;
import com.example.mybatis.mapper.UserMapper;
import com.example.mybatis.model.User;
import com.example.mybatis.model.PageResult;
import com.example.mybatis.model.UserQueryRequest;
import com.example.mybatis.service.InterruptibleQueryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;

@Service
public class InterruptibleQueryServiceImpl implements InterruptibleQueryService {
    
    private final UserMapper userMapper;
    private final LogHandler logHandler;
    private final ExecutorService executorService;
    
    public InterruptibleQueryServiceImpl(UserMapper userMapper, LogHandler logHandler) {
        this.userMapper = userMapper;
        this.logHandler = logHandler;
        this.executorService = Executors.newCachedThreadPool();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> searchUsersInterruptibly(User criteria) throws InterruptedException {
        // 检查中断状态
        if (Thread.interrupted()) {
            throw new InterruptedException("搜索操作被中断");
        }
        
        logHandler.handle(LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message("开始可中断搜索，条件: " + criteria)
                .build());
        List<User> users = userMapper.selectByCondition(criteria);
        
        // 再次检查中断状态
        if (Thread.interrupted()) {
            throw new InterruptedException("搜索操作被中断");
        }
        
        logHandler.handle(LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message("可中断搜索完成，找到 " + users.size() + " 条记录")
                .build());
        return users;
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> getAllUsersInterruptibly() throws InterruptedException {
        if (Thread.interrupted()) {
            throw new InterruptedException("查询操作被中断");
        }
        
        logHandler.handle(LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message("开始可中断查询所有用户")
                .build());
        List<User> users = userMapper.selectAll();
        
        if (Thread.interrupted()) {
            throw new InterruptedException("查询操作被中断");
        }
        
        logHandler.handle(LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message("可中断查询完成，找到 " + users.size() + " 条记录")
                .build());
        return users;
    }
    
    @Override
    @Transactional(readOnly = true)
    public PageResult<User> getPageListInterruptibly(UserQueryRequest request) throws InterruptedException {
        if (Thread.interrupted()) {
            throw new InterruptedException("分页查询被中断");
        }
        
        logHandler.handle(LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message("开始可中断分页查询: " + request)
                .build());
        
        // 查询总数
        long total = userMapper.countByCondition(request);
        
        if (Thread.interrupted()) {
            throw new InterruptedException("分页查询被中断");
        }
        
        // 如果没有数据，直接返回空结果
        if (total == 0) {
            return new PageResult<>(List.of(), 0, request);
        }
        
        // 查询数据
        List<User> users = userMapper.selectPage(request);
        
        if (Thread.interrupted()) {
            throw new InterruptedException("分页查询被中断");
        }
        
        logHandler.handle(LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message("可中断分页查询完成，总数: " + total)
                .build());
        return new PageResult<>(users, total, request);
    }
    
    @Override
    public List<User> queryUsersWithTimeout(long timeoutMillis) throws AsyncTimeoutException {
        try {
            Future<List<User>> future = executorService.submit(() -> {
                logHandler.handle(LogMessage.builder()
                        .id(UUID.randomUUID().toString())
                        .level(LogLevel.INFO)
                        .message("开始查询用户列表")
                        .build());
                return userMapper.selectAll();
            });

            try {
                List<User> users = future.get(timeoutMillis, TimeUnit.MILLISECONDS);
                logHandler.handle(LogMessage.builder()
                        .id(UUID.randomUUID().toString())
                        .level(LogLevel.INFO)
                        .message("查询用户列表完成")
                        .build());
                return users;
            } catch (TimeoutException e) {
                future.cancel(true);
                logHandler.handle(LogMessage.builder()
                        .id(UUID.randomUUID().toString())
                        .level(LogLevel.ERROR)
                        .message("查询用户列表超时")
                        .throwable(e)
                        .build());
                throw new AsyncTimeoutException("查询超时");
            }
        } catch (InterruptedException | ExecutionException e) {
            logHandler.handle(LogMessage.builder()
                    .id(UUID.randomUUID().toString())
                    .level(LogLevel.ERROR)
                    .message("查询用户列表异常")
                    .throwable(e)
                    .build());
            Thread.currentThread().interrupt();
            throw new RuntimeException("查询异常", e);
        }
    }
} 