package com.example.mybatis;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * MyBatis学习项目的主应用类
 * 
 * @SpringBootApplication: Spring Boot的核心注解，它是一个组合注解，包含：
 *   - @SpringBootConfiguration: 标识这是一个Spring Boot的配置类
 *   - @EnableAutoConfiguration: 启用Spring Boot的自动配置机制
 *   - @ComponentScan: 启用组件扫描，自动扫描当前包及其子包下的Spring组件
 *
 * @MapperScan: MyBatis提供的注解，用于自动扫描指定包下的Mapper接口
 *   - 值"com.example.mybatis.mapper"指定了Mapper接口所在的包路径
 *   - 它会自动为包下的所有接口创建代理对象并注册到Spring容器中
 *
 * @EnableTransactionManagement: 启用Spring的声明式事务管理
 *   - 允许使用@Transactional注解进行事务管理
 *   - 提供了事务相关的功能，如事务的传播行为、隔离级别等配置
 */
@SpringBootApplication
@EnableAutoConfiguration
@ComponentScan(basePackages = {"com.example.mybatis", "com.example.common.starter"})
@MapperScan("com.example.mybatis.mapper")
@EnableTransactionManagement
public class MybatisApplication {
    
    /**
     * 应用程序的入口方法
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 启动Spring Boot应用
        SpringApplication.run(MybatisApplication.class, args);
    }
} 