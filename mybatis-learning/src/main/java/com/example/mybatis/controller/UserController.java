package com.example.mybatis.controller;

import com.example.mybatis.model.User;
import com.example.mybatis.model.UserQueryRequest;
import com.example.mybatis.model.PageResult;
import com.example.mybatis.service.UserService;
import com.example.mybatis.common.exception.UserNotFoundException;
import com.example.mybatis.common.exception.AsyncTimeoutException;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.scheduling.annotation.Async;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;
import java.util.concurrent.*;

/**
 * User REST Controller
 */
@RestController
@RequestMapping(value = "/api/users", produces = "application/json")
public class UserController {

    private final UserService userService;
    private final Executor asyncExecutor;

    public UserController(UserService userService, 
                         @Qualifier("asyncExecutor") Executor asyncExecutor) {
        this.userService = userService;
        this.asyncExecutor = asyncExecutor;
    }

    /**
     * Create a new user
     */
    @PostMapping
    @Transactional(timeout = 60)
    public User createUser(@RequestBody User user) {
        return userService.createUser(user);
    }

    /**
     * Get user by ID
     */
    @GetMapping("/{id}")
    public User getUserById(@PathVariable Long id) {
        User user = userService.getUserById(id);
        if (user == null) {
            throw new UserNotFoundException(id);
        }
        return user;
    }

    /**
     * Get all users
     */
    @GetMapping
    public CompletableFuture<List<User>> getAllUsers() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return userService.getAllUsers();
            } catch (Exception e) {
                throw new CompletionException(e);
            }
        }, asyncExecutor).orTimeout(30, TimeUnit.SECONDS)
          .exceptionally(throwable -> {
              if (throwable instanceof TimeoutException) {
                  throw new AsyncTimeoutException("查询所有用户超时");
              }
              throw new CompletionException(throwable);
          });
    }

    /**
     * Search users
     */
    @PostMapping("/search")
    public CompletableFuture<List<User>> searchUsers(@RequestBody User criteria) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return userService.searchUsers(criteria);
            } catch (Exception e) {
                throw new CompletionException(e);
            }
        }, asyncExecutor).orTimeout(10, TimeUnit.MILLISECONDS)
          .exceptionally(throwable -> {
              if (throwable instanceof TimeoutException) {
                  throw new AsyncTimeoutException("搜索用户超时");
              }
              throw new CompletionException(throwable);
          });
    }

    /**
     * Update user
     */
    @PutMapping("/{id}")
    @Transactional(timeout = 10)
    public User updateUser(@PathVariable Long id, @RequestBody User user) {
        User updatedUser = userService.updateUser(id, user);
        if (updatedUser == null) {
            throw new UserNotFoundException(id);
        }
        return updatedUser;
    }

    /**
     * Delete user
     */
    @DeleteMapping("/{id}")
    @Transactional(timeout = 30)
    public void deleteUser(@PathVariable Long id) {
        boolean deleted = userService.deleteUser(id);
        if (!deleted) {
            throw new UserNotFoundException(id);
        }
    }

    /**
     * Get page list
     */
    @PostMapping("/page")
    public CompletableFuture<PageResult<User>> getPageList(@RequestBody UserQueryRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return userService.getPageList(request);
            } catch (Exception e) {
                throw new CompletionException(e);
            }
        }, asyncExecutor).orTimeout(15, TimeUnit.SECONDS)
          .exceptionally(throwable -> {
              if (throwable instanceof TimeoutException) {
                  throw new AsyncTimeoutException("分页查询超时");
              }
              throw new CompletionException(throwable);
          });
    }
} 