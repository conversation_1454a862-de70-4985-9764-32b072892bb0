package com.example.mybatis.controller;

import com.example.mybatis.service.DataGeneratorService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/generator")
public class DataGeneratorController {
    private final DataGeneratorService dataGeneratorService;

    public DataGeneratorController(DataGeneratorService dataGeneratorService) {
        this.dataGeneratorService = dataGeneratorService;
    }

    @PostMapping("/users")
    public String generateUsers(
            @RequestParam(defaultValue = "10000") int total,
            @RequestParam(defaultValue = "1000") int batchSize) {
        dataGeneratorService.generateTestData(total, batchSize);
        return "Started generating " + total + " test users";
    }
} 