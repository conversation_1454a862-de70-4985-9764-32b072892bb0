package com.example.mybatis.controller;

import com.example.mybatis.mapper.UserMapper;
import com.example.mybatis.model.User;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/test")
public class SlowSqlTestController {

    private final UserMapper userMapper;

    public SlowSqlTestController(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @GetMapping("/slow-query")
    public List<User> testSlowQuery() {
        // 这个查询会执行复杂的条件和连接操作
        return userMapper.findUsersWithComplexConditions(1, 0);
    }

    @GetMapping("/recursive-query")
    public List<User> testRecursiveQuery() {
        // 这个查询会执行递归查询操作
        return userMapper.findUserHierarchy(1L);
    }
} 