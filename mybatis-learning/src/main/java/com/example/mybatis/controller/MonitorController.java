package com.example.mybatis.controller;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Controller
public class MonitorController {

    private final MeterRegistry meterRegistry;

    public MonitorController(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    @GetMapping("/monitor")
    public String monitorPage() {
        return "monitor";
    }

    @GetMapping("/monitor/metrics")
    @ResponseBody
    public Map<String, Object> getMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        // 收集所有Timer类型的指标
        meterRegistry.getMeters().stream()
                .filter(meter -> meter instanceof Timer)
                .forEach(meter -> {
                    Timer timer = (Timer) meter;
                    String name = timer.getId().getName();
                    
                    // 添加标签信息
                    String tags = timer.getId().getTags().stream()
                            .map(tag -> tag.getKey() + "=" + tag.getValue())
                            .collect(Collectors.joining(","));
                    
                    if (!tags.isEmpty()) {
                        name = name + "{" + tags + "}";
                    }
                    
                    Map<String, Object> timerMetrics = new HashMap<>();
                    timerMetrics.put("count", timer.count());
                    timerMetrics.put("mean", timer.mean(TimeUnit.MILLISECONDS));
                    timerMetrics.put("max", timer.max(TimeUnit.MILLISECONDS));
                    timerMetrics.put("total", timer.totalTime(TimeUnit.MILLISECONDS));
                    
                    metrics.put(name, timerMetrics);
                });
        
        return metrics;
    }
} 