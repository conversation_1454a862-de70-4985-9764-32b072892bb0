package com.example.mybatis.config;

import io.micrometer.core.instrument.MeterRegistry;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * SQL执行性能监控配置类
 *
 * @Aspect: 声明这是一个切面类，用于实现AOP（面向切面编程）
 * @Component: 将该类注册为Spring容器管理的组件
 */
@Aspect
@Component
public class SqlMetricsConfig {

    /**
     * Micrometer的度量注册器，用于记录和管理各种指标
     */
    private final MeterRegistry meterRegistry;

    /**
     * 构造函数，通过依赖注入获取MeterRegistry实例
     *
     * @param meterRegistry Spring自动注入的度量注册器
     */
    public SqlMetricsConfig(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    /**
     * 环绕通知，用于监控Mapper接口方法的执行时间
     *
     * @Around: 环绕通知注解，指定切点表达式
     * execution(* com.example.mybatis.mapper.*.*(..)):
     *   - 匹配mapper包下所有类的所有方法
     *   - 第一个*表示任意返回类型
     *   - 第二个*表示任意类
     *   - 第三个*表示任意方法
     *   - (..)表示任意参数
     *
     * @param joinPoint 切点对象，包含被拦截方法的信息
     * @return 被拦截方法的执行结果
     * @throws Throwable 执行过程中可能抛出的异常
     */
    @Around("execution(* com.example.mybatis.mapper.*.*(..))")
    public Object recordSqlMetrics(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取被拦截方法的名称
        String methodName = joinPoint.getSignature().getName();
        // 获取被拦截类的简单名称
        String className = joinPoint.getTarget().getClass().getSimpleName();
        // 定义度量指标名称
        String metricName = "sql_execution";

        // 记录方法执行开始时间
        long startTime = System.currentTimeMillis();
        try {
            // 执行原方法
            return joinPoint.proceed();
        } finally {
            // 计算方法执行时间
            long duration = System.currentTimeMillis() - startTime;
            // 记录执行时间指标
            meterRegistry.timer(metricName,
                "class", className,     // 记录执行类
                "method", methodName,   // 记录执行方法
                "type", getSqlType(methodName))  // 记录SQL类型
                .record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 根据方法名称推断SQL操作类型
     *
     * @param methodName Mapper方法名称
     * @return SQL操作类型：query(查询)、insert(插入)、update(更新)、delete(删除)或other(其他)
     */
    private String getSqlType(String methodName) {
        if (methodName.startsWith("find") || methodName.startsWith("select") || methodName.startsWith("get")) {
            return "query";
        } else if (methodName.startsWith("insert") || methodName.startsWith("add")) {
            return "insert";
        } else if (methodName.startsWith("update")) {
            return "update";
        } else if (methodName.startsWith("delete")) {
            return "delete";
        }
        return "other";
    }
}