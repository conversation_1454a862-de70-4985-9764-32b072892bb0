package com.example.mybatis.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * JSON类型处理器
 * 用于在MyBatis中处理JSON类型的数据与Java对象之间的转换
 * 
 * @MappedTypes: 指定该处理器可以处理的Java类型
 * Object.class表示可以处理任何类型的对象
 */
@MappedTypes(Object.class)
public class JsonTypeHandler extends BaseTypeHandler<Object> {
    
    /**
     * Jackson的ObjectMapper实例，用于JSON序列化和反序列化
     * 使用static final确保线程安全和单例模式
     */
    private static final ObjectMapper MAPPER = new ObjectMapper();

    /**
     * 设置非空参数值到PreparedStatement中
     * 将Java对象转换为JSON字符串，并设置到SQL语句中
     *
     * @param ps PreparedStatement实例
     * @param i 参数位置
     * @param parameter 要设置的参数值
     * @param jdbcType JDBC类型
     * @throws SQLException 如果设置参数时发生错误
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        try {
            ps.setString(i, MAPPER.writeValueAsString(parameter));
        } catch (JsonProcessingException e) {
            throw new SQLException("Error converting JSON to String", e);
        }
    }

    /**
     * 通过列名从ResultSet中获取可为空的结果
     * 将JSON字符串转换回Java对象
     *
     * @param rs ResultSet实例
     * @param columnName 列名
     * @return 转换后的Java对象，如果原值为null则返回null
     * @throws SQLException 如果获取或转换数据时发生错误
     */
    @Override
    public Object getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        if (json == null) return null;
        try {
            return MAPPER.readValue(json, Object.class);
        } catch (JsonProcessingException e) {
            throw new SQLException("Error converting String to JSON", e);
        }
    }

    /**
     * 通过列索引从ResultSet中获取可为空的结果
     * 将JSON字符串转换回Java对象
     *
     * @param rs ResultSet实例
     * @param columnIndex 列索引
     * @return 转换后的Java对象，如果原值为null则返回null
     * @throws SQLException 如果获取或转换数据时发生错误
     */
    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        if (json == null) return null;
        try {
            return MAPPER.readValue(json, Object.class);
        } catch (JsonProcessingException e) {
            throw new SQLException("Error converting String to JSON", e);
        }
    }

    /**
     * 从CallableStatement中获取可为空的结果
     * 用于处理存储过程的输出参数
     *
     * @param cs CallableStatement实例
     * @param columnIndex 列索引
     * @return 转换后的Java对象，如果原值为null则返回null
     * @throws SQLException 如果获取或转换数据时发生错误
     */
    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        if (json == null) return null;
        try {
            return MAPPER.readValue(json, Object.class);
        } catch (JsonProcessingException e) {
            throw new SQLException("Error converting String to JSON", e);
        }
    }
} 