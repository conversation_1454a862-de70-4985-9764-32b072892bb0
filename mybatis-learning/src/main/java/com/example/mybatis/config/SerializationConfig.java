package com.example.mybatis.config;

import org.springframework.context.annotation.Configuration;
import jakarta.annotation.PostConstruct;
import java.io.ObjectInputFilter;

/**
 * 序列化过滤器配置
 * 用于增强应用程序的反序列化安全性
 */
@Configuration
public class SerializationConfig {

    @PostConstruct
    public void configureSerializationFilter() {
        // 创建一个允许列表模式的过滤器
        String filterPattern = 
            "com.example.mybatis.**;" +          // 允许项目自身的类
            "java.util.*;" +                     // 允许Java工具类
            "java.lang.*;" +                     // 允许Java基础类
            "org.apache.ibatis.**;" +            // 允许MyBatis相关类
            "com.example.mybatis.model.*;" +     // 允许模型类
            "java.sql.*;" +                      // 允许JDBC相关类
            "java.time.*;" +                     // 允许日期时间类
            "java.math.*;" +                     // 允许数学计算类
            "[Ljava.lang.Object;" +              // 允许对象数组
            "[Ljava.util.HashMap;" +             // 允许HashMap数组
            "java.util.ArrayList;" +             // 允许ArrayList
            "java.util.LinkedHashMap;" +         // 允许LinkedHashMap
            "!*";                                // 拒绝其他所有类

        // 设置全局序列化过滤器
        ObjectInputFilter filter = ObjectInputFilter.Config.createFilter(filterPattern);
        ObjectInputFilter.Config.setSerialFilter(filter);
    }
}   