package com.example.mybatis.config;

import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.concurrent.Executor;
import java.util.Map;

/**
 * 异步执行配置
 * 配置异步执行的线程池、超时和事务支持
 */
@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean("asyncExecutor")
    public Executor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor() {
            @Override
            public void execute(Runnable task) {
                Map<String, String> contextMap = MDC.getCopyOfContextMap();
                super.execute(() -> {
                    try {
                        if (contextMap != null) {
                            MDC.setContextMap(contextMap);
                        }
                        task.run();
                    } finally {
                        MDC.clear();
                    }
                });
            }
        };
        
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(25);
        executor.setThreadNamePrefix("Async-");
        executor.initialize();
        return executor;
    }

    @Bean
    public TransactionTemplate transactionTemplate(PlatformTransactionManager transactionManager) {
        TransactionTemplate template = new TransactionTemplate(transactionManager);
        // 设置事务超时时间（30秒）
        template.setTimeout(30);
        return template;
    }
} 