package com.example.mybatis.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.SqlSession;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * SQL性能监控配置类
 * 用于配置MyBatis的SQL执行性能监控
 * 
 * @Configuration: 标识这是一个Spring配置类
 */
@Configuration
public class SqlPerformanceMonitorConfig {

    /**
     * 创建SQL性能监控拦截器的Bean
     * 
     * @param meterRegistry Spring自动注入的度量注册器
     * @return SQL性能监控拦截器实例
     */
    @Bean
    public SqlPerformanceInterceptor sqlPerformanceInterceptor(MeterRegistry meterRegistry) {
        return new SqlPerformanceInterceptor(meterRegistry);
    }

    /**
     * SQL性能监控拦截器内部类
     * 
     * @Intercepts: MyBatis拦截器注解，指定要拦截的方法
     * @Signature: 指定拦截的具体方法签名
     *   - type: 指定要拦截的类型（SqlSession.class）
     *   - method: 指定要拦截的方法名（select）
     *   - args: 指定方法参数类型
     */
    @Intercepts({
            @Signature(type = SqlSession.class, method = "select", 
                      args = {String.class, Object.class, ResultHandler.class})
    })
    public static class SqlPerformanceInterceptor implements Interceptor {
        
        /**
         * Micrometer的度量注册器
         * 用于记录SQL执行性能指标
         */
        private final MeterRegistry meterRegistry;

        /**
         * 构造函数
         * 
         * @param meterRegistry Spring自动注入的度量注册器
         */
        public SqlPerformanceInterceptor(MeterRegistry meterRegistry) {
            this.meterRegistry = meterRegistry;
        }

        /**
         * 拦截方法，用于在SQL执行前后记录时间
         * 
         * @param invocation 包含被拦截方法信息的对象
         * @return 原方法的执行结果
         * @throws Throwable 执行过程中可能抛出的异常
         */
        @Override
        public Object intercept(Invocation invocation) throws Throwable {
            // 记录开始时间（使用纳秒级精度）
            long startTime = System.nanoTime();
            try {
                // 执行原方法
                return invocation.proceed();
            } finally {
                // 记录结束时间并计算执行时间
                long endTime = System.nanoTime();
                recordMetrics(invocation.getMethod().getName(), endTime - startTime);
            }
        }

        /**
         * 记录SQL执行性能指标
         * 
         * @param operation SQL操作类型
         * @param duration 执行时间（纳秒）
         */
        private void recordMetrics(String operation, long duration) {
            Timer.builder("mybatis.sql.execution")  // 创建计时器，指定名称
                    .tag("operation", operation)    // 添加操作类型标签
                    .description("SQL execution time")  // 添加描述
                    .register(meterRegistry)        // 注册到度量注册器
                    .record(duration, TimeUnit.NANOSECONDS);  // 记录执行时间
        }

        /**
         * 创建代理对象
         * 
         * @param target 目标对象
         * @return 代理后的对象
         */
        @Override
        public Object plugin(Object target) {
            return Interceptor.super.plugin(target);
        }

        /**
         * 设置拦截器属性
         * 
         * @param properties 配置属性
         */
        @Override
        public void setProperties(Properties properties) {
            Interceptor.super.setProperties(properties);
        }
    }
} 