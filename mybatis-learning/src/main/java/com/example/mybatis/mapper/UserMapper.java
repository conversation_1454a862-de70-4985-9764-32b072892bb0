package com.example.mybatis.mapper;

import com.example.mybatis.model.User;
import com.example.mybatis.model.UserQueryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserMapper {
    void insert(User user);
    User selectById(@Param("id") Long id);
    List<User> selectAll();
    List<User> selectByCondition(User criteria);
    void update(User user);
    void deleteById(@Param("id") Long id);
    
    // 测试慢SQL：复杂条件查询
    List<User> findUsersWithComplexConditions(@Param("status") Integer status, 
                                            @Param("minLoginCount") Integer minLoginCount);
    
    // 测试慢SQL：递归查询用户层级
    List<User> findUserHierarchy(@Param("rootUserId") Long rootUserId);

    // 新增分页查询方法
    long countByCondition(UserQueryRequest request);
    List<User> selectPage(UserQueryRequest request);

    // 批量插入用户数据
    void batchInsert(List<User> users);
} 