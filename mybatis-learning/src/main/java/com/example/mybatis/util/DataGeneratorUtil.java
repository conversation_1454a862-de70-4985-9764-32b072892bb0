package com.example.mybatis.util;

import com.example.mybatis.model.User;
import com.github.javafaker.Faker;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 测试数据生成工具类
 */
public class DataGeneratorUtil {
    private static final Faker faker = new Faker(new Locale("zh_CN"));
    private static final Random random = new Random();
    private static final Set<String> usedNames = new HashSet<>();
    
    // 常用中文姓氏（按使用频率排序）
    private static final String[] SURNAMES = {
        "李", "王", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
        "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
        "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧",
        "程", "曹", "袁", "邓", "许", "傅", "沈", "曾", "彭", "吕",
        "苏", "卢", "蒋", "蔡", "贾", "丁", "魏", "薛", "叶", "阎",
        "余", "潘", "杜", "戴", "夏", "锺", "汪", "田", "任", "姜",
        "范", "方", "石", "姚", "谭", "廖", "邹", "熊", "金", "陆",
        "郝", "孔", "白", "崔", "康", "毛", "邱", "秦", "江", "史",
        "顾", "侯", "邵", "孟", "龙", "万", "段", "雷", "钱", "汤",
        "尹", "黎", "易", "常", "武", "乔", "贺", "赖", "龚", "文"
    };
    
    // 常用名字字符（按使用频率排序）
    private static final String[] NAME_CHARS = {
        "伟", "芳", "娜", "秀", "敏", "静", "丽", "强", "磊", "军",
        "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "兰", "霞",
        "平", "刚", "博", "志", "建", "国", "栋", "雪", "琳", "晨",
        "阳", "文", "华", "晶", "玉", "俊", "慧", "敬", "宇", "峰",
        "旭", "凯", "嘉", "瑞", "飞", "彬", "鑫", "欣", "琪", "悦",
        "昊", "天", "思", "远", "翔", "宁", "浩", "迪", "海", "荣",
        "岩", "松", "佳", "琴", "成", "健", "亮", "帆", "云", "龙",
        "辉", "斌", "春", "江", "宏", "声", "世", "昌", "庆", "欢",
        "光", "达", "安", "福", "生", "珍", "发", "仁", "义", "才",
        "凤", "元", "正", "良", "宝", "玲", "庭", "诚", "星", "兵"
    };

    // 复姓列表
    private static final String[] DOUBLE_SURNAMES = {
        "欧阳", "司马", "上官", "端木", "诸葛", "东方", "独孤", "南宫", "万俟", "闻人",
        "夏侯", "皇甫", "尉迟", "公羊", "澹台", "公冶", "宗政", "濮阳", "淳于", "单于",
        "太叔", "申屠", "公孙", "仲孙", "轩辕", "令狐", "钟离", "宇文", "长孙", "慕容"
    };

    /**
     * 生成不重复的中文姓名
     */
    private static String generateUniqueName() {
        String name;
        do {
            StringBuilder fullName = new StringBuilder();
            
            // 生成姓氏（90%概率使用单姓，10%概率使用复姓）
            if (random.nextInt(100) < 90) {
                fullName.append(SURNAMES[random.nextInt(SURNAMES.length)]);
            } else {
                fullName.append(DOUBLE_SURNAMES[random.nextInt(DOUBLE_SURNAMES.length)]);
            }
            
            // 决定名字长度（80%概率两字名，15%概率一字名，5%概率三字名）
            int nameLength = random.nextInt(100);
            Set<Integer> usedIndices = new HashSet<>();
            
            if (nameLength < 15) { // 15% 一字名
                fullName.append(NAME_CHARS[random.nextInt(NAME_CHARS.length)]);
            } else if (nameLength < 95) { // 80% 两字名
                // 生成第一个字
                int firstIndex = random.nextInt(NAME_CHARS.length);
                usedIndices.add(firstIndex);
                fullName.append(NAME_CHARS[firstIndex]);
                
                // 生成第二个字（确保不重复）
                int secondIndex;
                do {
                    secondIndex = random.nextInt(NAME_CHARS.length);
                } while (usedIndices.contains(secondIndex));
                fullName.append(NAME_CHARS[secondIndex]);
            } else { // 5% 三字名
                // 生成三个不重复的字
                for (int i = 0; i < 3; i++) {
                    int index;
                    do {
                        index = random.nextInt(NAME_CHARS.length);
                    } while (usedIndices.contains(index));
                    usedIndices.add(index);
                    fullName.append(NAME_CHARS[index]);
                }
            }
            
            name = fullName.toString();
        } while (usedNames.contains(name));
        
        usedNames.add(name);
        return name;
    }

    /**
     * 生成随机用户数据
     */
    public static User generateUser() {
        User user = new User();
        String name = generateUniqueName();
        
        user.setUsername(name);
        user.setPassword(faker.crypto().md5());
        user.setEmail(name + "@example.com");
        user.setStatus(random.nextInt(2));
        
        LocalDateTime createTime = LocalDateTime.ofInstant(
            faker.date().past(30, TimeUnit.DAYS).toInstant(),
            ZoneId.systemDefault()
        );
        user.setCreateTime(createTime);
        user.setUpdateTime(createTime);
        
        user.setAdditionalInfo("{" +
            "\"phone\":\"" + faker.phoneNumber().cellPhone() + "\"," +
            "\"address\":\"" + faker.address().fullAddress() + "\"," +
            "\"company\":\"" + faker.company().name() + "\"," +
            "\"realName\":\"" + name + "\"" +
            "}");
        
        return user;
    }

    /**
     * 批量生成用户数据
     */
    public static List<User> generateUsers(int count) {
        // 计算可能的组合数（考虑单姓和复姓）
        long singleSurnameCount = SURNAMES.length;
        long doubleSurnameCount = DOUBLE_SURNAMES.length;
        long nameCharsCount = NAME_CHARS.length;
        
        // 计算各种长度名字的组合数
        long oneCharCombinations = (singleSurnameCount + doubleSurnameCount) * nameCharsCount;
        long twoCharCombinations = (singleSurnameCount + doubleSurnameCount) * nameCharsCount * (nameCharsCount - 1);
        long threeCharCombinations = (singleSurnameCount + doubleSurnameCount) * nameCharsCount * (nameCharsCount - 1) * (nameCharsCount - 2);
        
        long maxPossibleUsers = oneCharCombinations + twoCharCombinations + threeCharCombinations;
            
        if (count > maxPossibleUsers) {
            throw new IllegalArgumentException(
                "Cannot generate more than " + maxPossibleUsers + 
                " unique users with current name combinations."
            );
        }
        
        List<User> users = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            users.add(generateUser());
        }
        return users;
    }

    /**
     * 清除已使用的姓名（用于测试）
     */
    public static void reset() {
        usedNames.clear();
    }
}
