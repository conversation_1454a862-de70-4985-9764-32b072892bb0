package com.example.mybatis.service;

import com.example.mybatis.model.User;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class UserServiceTest {

    @Autowired
    private UserService userService;

    @Test
    @Transactional
    void testCreateUser() {
        User user = User.builder()
                .username("testuser")
                .password("password")
                .email("<EMAIL>")
                .build();

        User created = userService.createUser(user);
        assertNotNull(created.getId());
        assertEquals("testuser", created.getUsername());
        assertEquals(1, created.getStatus());
    }

    @Test
    @Transactional
    void testUpdateUser() {
        // First create a user
        User user = User.builder()
                .username("updatetest")
                .password("password")
                .email("<EMAIL>")
                .build();
        User created = userService.createUser(user);

        // Update the user
        created.setEmail("<EMAIL>");
        User updated = userService.updateUser(created.getId(), created);
        assertNotNull(updated);

        // Verify the update
        User found = userService.getUserById(created.getId());
        assertEquals("<EMAIL>", found.getEmail());
    }

    @Test
    @Transactional
    void testDeleteUser() {
        // First create a user
        User user = User.builder()
                .username("deletetest")
                .password("password")
                .email("<EMAIL>")
                .build();
        User created = userService.createUser(user);

        // Delete the user
        boolean deleted = userService.deleteUser(created.getId());
        assertTrue(deleted);

        // Verify the deletion
        User found = userService.getUserById(created.getId());
        assertNull(found);
    }

    @Test
    void testGetAllUsers() {
        List<User> users = userService.getAllUsers();
        assertNotNull(users);
        assertFalse(users.isEmpty());
    }
}