# MyBatis 学习指南

## 1. 环境准备
1. 确保已安装：
   - JDK 17
   - Maven 3.6+
   - MySQL 8.0+
   - IDE（推荐使用IDEA）

2. 数据库准备：
   ```sql
   CREATE DATABASE mybatis_learning;
   USE mybatis_learning;
   ```

## 2. 快速开始
1. 克隆项目到本地
2. 修改 `application.properties` 中的数据库配置（用户名和密码）
3. 运行 `MybatisApplication.java` 启动项目
4. 访问 `http://localhost:8080/api/users` 测试基础功能

## 3. 学习路径

### 第一阶段：MyBatis基础（2-3天）
1. **基础概念**
   - 了解ORM框架的作用
   - MyBatis的核心组件
   - 配置文件结构
   
2. **实践任务**
   - 运行并测试 `UserController` 中的CRUD接口
   - 查看 `UserMapper.xml` 学习基础SQL映射
   - 尝试自己编写一个新的实体类和对应的Mapper

### 第二阶段：进阶特性（3-4天）
1. **动态SQL**
   - if条件
   - choose/when/otherwise
   - trim/where/set
   - foreach循环
   
2. **实践任务**
   - 学习 `UserMapper.xml` 中的动态SQL示例
   - 修改 `searchUsers` 方法，添加新的搜索条件
   - 实现批量插入和更新功能

### 第三阶段：性能优化（2-3天）
1. **缓存机制**
   - 一级缓存（默认开启）
   - 二级缓存配置和使用
   - 自定义缓存实现

2. **实践任务**
   - 观察一级缓存的生效场景
   - 配置并测试二级缓存
   - 使用Redis实现自定义缓存

### 第四阶段：高级特性（3-4天）
1. **高级映射**
   - 一对一关系
   - 一对多关系
   - 多对多关系
   
2. **实践任务**
   - 创建角色表（Role）和用户角色关联表
   - 实现用户和角色的关联查询
   - 处理复杂的嵌套查询场景

## 4. 测试方法

### 4.1 单元测试
1. 运行 `UserServiceTest` 类中的测试用例
2. 学习测试用例的编写方法
3. 为新功能编写对应的测试用例

### 4.2 接口测试
使用Postman或curl测试以下接口：

1. 创建用户
```bash
curl -X POST http://localhost:8080/api/users \
-H "Content-Type: application/json" \
-d '{
    "username": "test_user",
    "password": "123456",
    "email": "<EMAIL>"
}'
```

2. 查询用户
```bash
curl http://localhost:8080/api/users/1
```

3. 更新用户
```bash
curl -X PUT http://localhost:8080/api/users/1 \
-H "Content-Type: application/json" \
-d '{
    "email": "<EMAIL>"
}'
```

4. 删除用户
```bash
curl -X DELETE http://localhost:8080/api/users/1
```

### 4.3 性能测试
1. 使用JMeter进行压力测试
2. 观察Druid监控页面的SQL统计
3. 分析并优化慢SQL查询

## 5. 常见问题和解决方案

### 5.1 连接问题
1. 确保MySQL服务已启动
2. 检查用户名密码是否正确
3. 确认数据库名称是否正确
4. 检查防火墙设置

### 5.2 映射问题
1. 检查XML文件是否在正确的位置
2. 确认namespace是否正确
3. 验证resultType/resultMap的配置
4. 检查字段名和属性名的映射关系

### 5.3 缓存问题
1. 确认缓存配置是否正确
2. 了解缓存失效的场景
3. 观察缓存命中率

## 6. 扩展学习资源

### 6.1 官方文档
- [MyBatis官方文档](https://mybatis.org/mybatis-3/zh/index.html)
- [MyBatis-Spring-Boot-Starter文档](http://mybatis.org/spring-boot-starter/mybatis-spring-boot-autoconfigure/)

### 6.2 推荐书籍
- 《MyBatis从入门到精通》
- 《深入浅出MyBatis技术原理与实战》

### 6.3 在线教程
- MyBatis官方示例
- GitHub优秀开源项目
- 技术博客文章

## 7. 进阶主题

### 7.1 插件开发
- 分页插件
- 审计插件
- 加密插件

### 7.2 源码分析
- 配置解析过程
- SQL执行流程
- 插件机制实现

### 7.3 实际应用
- 分库分表
- 读写分离
- 分布式事务

## 8. 面试题整理

### 8.1 基础概念
1. MyBatis与Hibernate的区别
2. #{}和${}的区别
3. MyBatis的工作原理
4. XML映射文件中标签的使用

### 8.2 进阶特性
1. 动态SQL的实现原理
2. 缓存机制的工作原理
3. 插件机制的实现原理
4. 延迟加载的实现方式

### 8.3 性能调优
1. 如何处理千万级数据的查询
2. 如何优化MyBatis的性能
3. 如何处理复杂的多表关联查询
4. 分页查询的优化方案

## 9. 项目练习

### 9.1 基础项目
- 实现一个简单的用户管理系统
- 包含基础的CRUD操作
- 使用动态SQL实现复杂查询

### 9.2 进阶项目
- 实现一个博客系统
- 包含用户、文章、评论等多个实体
- 使用复杂的表关联
- 实现缓存和性能优化

### 9.3 高级项目
- 实现一个电商系统
- 包含商品、订单、库存等模块
- 处理高并发场景
- 实现分库分表方案

## 10. 代码规范

### 10.1 命名规范
- 表名使用下划线命名法
- Java类使用驼峰命名法
- XML文件要有清晰的注释

### 10.2 SQL规范
- 使用预编译防止SQL注入
- 合理使用动态SQL
- 避免使用*号查询
- 建立合适的索引

### 10.3 代码组织
- 遵循单一职责原则
- 保持代码简洁清晰
- 编写完整的注释
- 做好异常处理

## 11. 如何获取帮助
1. 查阅官方文档
2. Stack Overflow提问
3. GitHub Issues
4. 技术社区交流 