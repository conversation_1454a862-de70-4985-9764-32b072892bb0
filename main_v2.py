# -*- coding: utf-8 -*-
"""
Kcptun GUI v2.0 主程序入口
重构后的现代化版本
"""

import os
import sys
import traceback
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Windows单实例控制
if sys.platform.startswith('win'):
    import ctypes
    from ctypes import wintypes

class SingleInstance:
    """单实例控制类"""

    def __init__(self, app_name="KcptunGUI_v2.0"):
        self.app_name = app_name
        self.mutex_name = f"Global\\{app_name}_Mutex"
        self.mutex = None
        self.is_running = False

    def check_single_instance(self):
        """检查是否已有实例在运行"""
        if not sys.platform.startswith('win'):
            return True  # 非Windows系统暂不限制

        try:
            # 创建或打开互斥锁
            kernel32 = ctypes.windll.kernel32

            # 尝试创建互斥锁
            self.mutex = kernel32.CreateMutexW(
                None,  # 安全属性
                True,  # 初始拥有者
                self.mutex_name  # 互斥锁名称
            )

            if not self.mutex:
                return False

            # 检查是否已存在
            last_error = kernel32.GetLastError()
            if last_error == 183:  # ERROR_ALREADY_EXISTS
                self.is_running = True
                return False

            return True

        except Exception as e:
            print(f"单实例检查失败: {e}")
            return True  # 出错时允许运行

    def try_activate_existing(self):
        """尝试激活已存在的实例"""
        if not sys.platform.startswith('win'):
            return False

        try:
            # 查找已存在的窗口
            user32 = ctypes.windll.user32

            # 定义窗口查找回调函数
            def enum_windows_proc(hwnd, lParam):
                # 获取窗口标题
                length = user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value

                    # 检查是否是我们的程序窗口
                    if "Kcptun客户端 v2.0" in window_title:
                        # 显示窗口
                        user32.ShowWindow(hwnd, 9)  # SW_RESTORE
                        user32.SetForegroundWindow(hwnd)
                        return False  # 停止枚举
                return True

            # 枚举所有窗口
            EnumWindowsProc = ctypes.WINFUNCTYPE(wintypes.BOOL, wintypes.HWND, wintypes.LPARAM)
            user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)

            return True

        except Exception as e:
            print(f"激活现有实例失败: {e}")
            return False

    def release(self):
        """释放互斥锁"""
        if self.mutex and sys.platform.startswith('win'):
            try:
                kernel32 = ctypes.windll.kernel32
                kernel32.ReleaseMutex(self.mutex)
                kernel32.CloseHandle(self.mutex)
                self.mutex = None
            except Exception as e:
                print(f"释放互斥锁失败: {e}")

def setup_environment():
    """设置运行环境"""
    # Windows系统设置
    if sys.platform.startswith('win'):
        try:
            # 设置控制台代码页
            import ctypes
            kernel32 = ctypes.windll.kernel32
            kernel32.SetConsoleOutputCP(65001)  # 设置控制台代码页为UTF-8
        except Exception:
            pass
    
    # 设置环境变量
    os.environ['PYSTRAY_DEBUG'] = '1'
    os.environ['PYTHONIOENCODING'] = 'utf-8'

def check_dependencies():
    """检查依赖项"""
    required_modules = [
        'tkinter',
        'pystray', 
        'PIL',
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"缺少必要的依赖模块: {', '.join(missing_modules)}")
        print("请运行以下命令安装依赖:")
        print("pip install pystray Pillow")
        return False
    
    return True

def check_kcptun_client():
    """检查kcptun客户端程序"""
    client_path = Path("bin/client_windows_amd64.exe")
    if not client_path.exists():
        print(f"错误: 找不到Kcptun客户端程序: {client_path}")
        print("请将Kcptun客户端程序放置在bin目录下")
        return False
    return True

def main():
    """主函数"""
    single_instance = None

    try:
        print("Kcptun GUI v2.0 启动中...")

        # 单实例检查
        single_instance = SingleInstance()
        if not single_instance.check_single_instance():
            print("检测到程序已在运行，尝试激活现有窗口...")
            single_instance.try_activate_existing()
            print("程序已在运行，退出当前启动。")
            return 0

        print("单实例检查通过，继续启动...")

        # 设置环境
        setup_environment()
        
        # 检查依赖
        if not check_dependencies():
            try:
                input("按回车键退出...")
            except:
                import time
                time.sleep(3)
            return 1

        # 检查kcptun客户端（测试时跳过）
        if not check_kcptun_client():
            print("警告: 未找到kcptun客户端，但继续启动用于测试...")
            # try:
            #     input("按回车键退出...")
            # except:
            #     import time
            #     time.sleep(3)
            # return 1
        
        # 导入并启动主窗口
        from gui.main_window import MainWindow

        print("正在初始化界面...")
        # 检查是否有kcptun客户端，没有则以测试模式启动
        test_mode = not check_kcptun_client()
        app = MainWindow(test_mode=test_mode)
        
        print("Kcptun GUI v2.0 启动完成")
        app.run()

        return 0

    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"程序启动失败: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        try:
            input("按回车键退出...")
        except:
            import time
            time.sleep(5)
        return 1
    finally:
        # 清理单实例锁
        if single_instance:
            single_instance.release()
            print("单实例锁已释放")

if __name__ == "__main__":
    sys.exit(main())
