#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

# Windows系统设置
if sys.platform.startswith('win'):
    try:
        # 设置控制台代码页
        import ctypes
        kernel32 = ctypes.windll.kernel32
        kernel32.SetConsoleOutputCP(65001)  # 设置控制台代码页为UTF-8
    except:
        pass

import tkinter as tk
from src.gui import KcptunGUI
import sys
import traceback
from pathlib import Path
import win32event
import win32api
import winerror

def setup_environment():
    """确保必要的目录结构存在"""
    directories = ['bin', 'resources']
    for dir_name in directories:
        Path(dir_name).mkdir(parents=True, exist_ok=True)

def main():
    mutex = None
    try:
        # 创建互斥锁
        mutex_name = "Global\\KcptunGUI_SingleInstance_Mutex"
        mutex = win32event.CreateMutex(None, 1, mutex_name)
        if win32api.GetLastError() == winerror.ERROR_ALREADY_EXISTS:
            # 如果互斥锁已存在，说明程序已经在运行
            ctypes.windll.user32.MessageBoxW(
                0, 
                "Kcptun客户端已经在运行中。\n请检查系统托盘或任务栏。", 
                "提示", 
                0x40  # MB_ICONINFORMATION
            )
            return

        # 确保目录结构存在
        setup_environment()
        
        # 创建主窗口
        root = tk.Tk()
        
        # 设置窗口图标（如果存在）
        icon_path = Path("resources/icon.png")
        if icon_path.exists():
            try:
                root.iconphoto(True, tk.PhotoImage(file=str(icon_path)))
            except Exception:
                pass
        
        # 创建应用实例
        app = KcptunGUI(root)
        
        # 进入主循环
        root.mainloop()
        
    except Exception as e:
        error_msg = f"Fatal error: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        sys.exit(1)
    finally:
        # 释放互斥锁
        if mutex:
            win32api.CloseHandle(mutex)

if __name__ == "__main__":
    main() 