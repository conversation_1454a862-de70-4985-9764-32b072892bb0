# Redis Learning Module

这个模块用于学习和实践Redis相关的功能和特性。

## 功能特性

1. Redis基础操作
   - 字符串(String)操作
   - 列表(List)操作
   - 集合(Set)操作
   - 有序集合(Sorted Set)操作
   - 哈希(Hash)操作

2. Redis进阶特性
   - 发布订阅
   - 事务操作
   - 缓存策略
   - 分布式锁

## 如何使用

1. 确保本地安装了Redis服务器
2. 在application.yml中配置Redis连接信息
3. 运行示例代码学习不同的Redis特性

## 示例代码结构

- `com.example.config`: Redis配置类
- `com.example.service`: Redis操作服务类
- `com.example.controller`: REST API示例
- `com.example.demo`: 各种Redis特性的示例代码

## 学习路径

1. 从基础的CRUD操作开始
2. 学习不同数据类型的使用场景
3. 实践缓存策略
4. 探索分布式特性 