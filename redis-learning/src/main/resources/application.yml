spring:
  profiles:
    active: dev
  application:
    name: redis-learning
  data:
    redis:
      host: *************
      port: 6379
      database: 0
      timeout: 60000
      client-type: lettuce
      lettuce:
        pool:
          max-active: 8
          max-wait: -1
          max-idle: 8
          min-idle: 0

server:
  port: 8081

# 统一日志配置
common:
  logging:
    enabled: true
    log-path: logs
    level: INFO
    module-name: ${spring.application.name}
    max-history: 30
    max-file-size: 100MB
    enable-console: true
    pattern: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([%thread]){cyan} %clr(%-5level){red} %clr(%logger{36}){yellow} %clr([%X{traceId}]){green} - %msg%n"

    # 包级别日志配置
    package-levels:
      com.example: DEBUG
      org.springframework: INFO

    # 异步日志配置
    async:
      enabled: true
      queue-size: 512

    # MDC配置
    mdc:
      id-generator: snowflake
      max-length: 32
      enable-prefix: true
      prefix: "redis-"
      include-timestamp: true
      timestamp-format: "MMddHHmmss"
      # snowflake配置
      snowflake:
        worker-id: 1
        data-center-id: 1
        sequence: 0

# 只保留日志级别配置
logging:
  level:
    root: INFO
    com.example: DEBUG
    org.springframework.data.redis: INFO
