# -*- coding: utf-8 -*-
"""
PyInstaller 构建配置
"""

# 应用信息
APP_NAME = "KcptunGUI_v2.0"
APP_VERSION = "2.0.0"
APP_DESCRIPTION = "Kcptun 图形化客户端 v2.0"
APP_AUTHOR = "Kcptun GUI Team"

# 构建配置
BUILD_CONFIG = {
    # 基本设置
    'onefile': True,                    # 打包成单个文件
    'windowed': True,                   # 无控制台窗口
    'name': APP_NAME,                   # 输出文件名
    'distpath': 'dist',                 # 输出目录
    'workpath': 'build',                # 工作目录
    'specpath': '.',                    # spec文件位置
    
    # 数据文件
    'datas': [
        ('src', 'src'),                 # 源代码目录
        ('resources', 'resources'),      # 资源目录
    ],
    
    # 隐藏导入
    'hiddenimports': [
        'pystray._win32',
        'PIL._tkinter_finder',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'asyncio',
        'concurrent.futures',
        'threading',
        'json',
        'pathlib',
        'datetime',
        'tempfile',
        'shutil',
        'subprocess',
    ],
    
    # 排除模块
    'excludes': [
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'IPython',
        'jupyter',
        'notebook',
        'qtconsole',
        'spyder',
        'pytest',
        'setuptools',
        'distutils',
    ],
    
    # 优化选项
    'optimize': 2,                      # 字节码优化级别
    'strip': True,                      # 去除调试信息
    'upx': False,                       # 不使用UPX压缩（可能导致杀毒软件误报）
}

# 版本信息
VERSION_INFO = f"""
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2, 0, 0, 0),
    prodvers=(2, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [
            StringStruct(u'CompanyName', u'{APP_AUTHOR}'),
            StringStruct(u'FileDescription', u'{APP_DESCRIPTION}'),
            StringStruct(u'FileVersion', u'{APP_VERSION}'),
            StringStruct(u'InternalName', u'{APP_NAME}'),
            StringStruct(u'LegalCopyright', u'Copyright © 2024'),
            StringStruct(u'OriginalFilename', u'{APP_NAME}.exe'),
            StringStruct(u'ProductName', u'{APP_DESCRIPTION}'),
            StringStruct(u'ProductVersion', u'{APP_VERSION}')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
"""
