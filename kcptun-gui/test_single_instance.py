# -*- coding: utf-8 -*-
"""
测试单实例功能
"""

import sys
import time
import subprocess
from pathlib import Path

def test_single_instance():
    """测试单实例功能"""
    print("🧪 测试单实例功能...")
    
    # 启动第一个实例
    print("\n1️⃣ 启动第一个实例...")
    process1 = subprocess.Popen(
        [sys.executable, "main_v2.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0
    )
    
    # 等待第一个实例启动
    time.sleep(3)
    
    # 检查第一个实例是否还在运行
    if process1.poll() is None:
        print("✅ 第一个实例启动成功")
    else:
        print("❌ 第一个实例启动失败")
        stdout, stderr = process1.communicate()
        print(f"输出: {stdout}")
        print(f"错误: {stderr}")
        return False
    
    # 尝试启动第二个实例
    print("\n2️⃣ 尝试启动第二个实例...")
    process2 = subprocess.Popen(
        [sys.executable, "main_v2.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0
    )
    
    # 等待第二个实例处理
    time.sleep(2)
    
    # 检查第二个实例的输出
    stdout2, stderr2 = process2.communicate()
    
    if "程序已在运行" in stdout2:
        print("✅ 单实例检查正常工作 - 第二个实例被阻止")
        print(f"第二个实例输出: {stdout2.strip()}")
    else:
        print("❌ 单实例检查失败 - 第二个实例可能启动了")
        print(f"第二个实例输出: {stdout2}")
        print(f"第二个实例错误: {stderr2}")
    
    # 清理第一个实例
    print("\n3️⃣ 清理测试进程...")
    try:
        process1.terminate()
        process1.wait(timeout=5)
        print("✅ 第一个实例已终止")
    except subprocess.TimeoutExpired:
        process1.kill()
        print("⚠️ 第一个实例被强制终止")
    except Exception as e:
        print(f"❌ 终止第一个实例时出错: {e}")
    
    print("\n🎉 单实例功能测试完成！")
    return True

if __name__ == "__main__":
    test_single_instance()
