# -*- coding: utf-8 -*-
"""
配置管理模块
负责配置的加载、保存、验证、导入导出等功能
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
import tempfile

try:
    from ..logger import KcptunLogger
except ImportError:
    from logger import KcptunLogger


class ConfigManager:
    """配置管理器"""
    
    DEFAULT_CONFIG = {
        'current_profile': 'default',
        'profiles': {
            'default': {
                'kcp_ip': '',
                'kcp_port': '',
                'kcp_password': '',
                'local_port': '',
                'advanced': {
                    'mtu': '1400',
                    'sndwnd': '1024',
                    'rcvwnd': '1024',
                    'mode': 'normal',
                    'crypt': 'aes-128',
                    'datashard': '10',
                    'parityshard': '3',
                    'dscp': '46',
                    'keepalive': '10',
                    'nocomp': True
                }
            }
        },
        'settings': {
            'enable_logging': True,
            'auto_start': False,
            'minimize_to_tray': True,
            'theme': 'light'
        }
    }
    
    def __init__(self, config_path: str = "resources/config.json", logger: Optional[KcptunLogger] = None):
        self.config_path = Path(config_path)
        self.logger = logger or KcptunLogger()
        self.backup_dir = self.config_path.parent / "backups"

        # 确保目录存在
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)

        # 当前配置缓存
        self._config_cache: Optional[Dict[str, Any]] = None
        self._config_dirty = False
        self._last_imported_profiles: List[str] = []  # 最后导入的配置名称
    
    def load(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 验证和修复配置
                config = self._validate_and_fix_config(config)
                self._config_cache = config
                self.logger.info("Configuration loaded successfully")
                return config
            else:
                # 创建默认配置
                self.logger.info("Creating default configuration")
                return self._create_default_config()
                
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            self.logger.info("Using default configuration")
            return self._create_default_config()
    
    def save(self, config: Dict[str, Any]) -> bool:
        """保存配置文件"""
        try:
            # 创建备份
            if self.config_path.exists():
                self._create_backup()
            
            # 验证配置
            config = self._validate_and_fix_config(config)
            
            # 保存到临时文件，然后原子性替换
            temp_path = self.config_path.with_suffix('.tmp')
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            # 原子性替换
            temp_path.replace(self.config_path)
            
            # 更新缓存
            self._config_cache = config
            self._config_dirty = False
            
            self.logger.info("Configuration saved successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
            return False
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        config = self.DEFAULT_CONFIG.copy()
        self.save(config)
        return config
    
    def _validate_and_fix_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证和修复配置"""
        # 深拷贝以避免修改原配置
        import copy
        fixed_config = copy.deepcopy(config)
        
        # 确保必要的键存在
        if 'profiles' not in fixed_config:
            fixed_config['profiles'] = {}
        
        if 'settings' not in fixed_config:
            fixed_config['settings'] = self.DEFAULT_CONFIG['settings'].copy()
        
        # 确保至少有一个默认配置
        if 'default' not in fixed_config['profiles']:
            fixed_config['profiles']['default'] = self.DEFAULT_CONFIG['profiles']['default'].copy()
        
        # 验证当前配置
        current_profile = fixed_config.get('current_profile', 'default')
        if current_profile not in fixed_config['profiles']:
            fixed_config['current_profile'] = 'default'
        
        # 验证每个配置的完整性
        for profile_name, profile_data in fixed_config['profiles'].items():
            fixed_config['profiles'][profile_name] = self._fix_profile_data(profile_data)
        
        return fixed_config
    
    def _fix_profile_data(self, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """修复单个配置数据"""
        default_profile = self.DEFAULT_CONFIG['profiles']['default']
        
        # 确保基本字段存在
        for key in ['kcp_ip', 'kcp_port', 'kcp_password', 'local_port']:
            if key not in profile_data:
                profile_data[key] = ''
        
        # 确保高级设置存在
        if 'advanced' not in profile_data:
            profile_data['advanced'] = {}
        
        # 修复高级设置
        default_advanced = default_profile['advanced']
        for key, default_value in default_advanced.items():
            if key not in profile_data['advanced']:
                profile_data['advanced'][key] = default_value
        
        return profile_data
    
    def _create_backup(self):
        """创建配置备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"config_backup_{timestamp}.json"
            backup_path = self.backup_dir / backup_name
            
            shutil.copy2(self.config_path, backup_path)
            
            # 清理旧备份（保留最近10个）
            self._cleanup_old_backups()
            
            self.logger.debug(f"Config backup created: {backup_name}")
            
        except Exception as e:
            self.logger.warning(f"Failed to create backup: {e}")
    
    def _cleanup_old_backups(self, keep_count: int = 10):
        """清理旧备份文件"""
        try:
            backup_files = list(self.backup_dir.glob("config_backup_*.json"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 删除多余的备份
            for backup_file in backup_files[keep_count:]:
                backup_file.unlink()
                self.logger.debug(f"Deleted old backup: {backup_file.name}")
                
        except Exception as e:
            self.logger.warning(f"Failed to cleanup old backups: {e}")
    
    def get_profile_names(self) -> List[str]:
        """获取所有配置名称"""
        config = self.get_current_config()
        return list(config.get('profiles', {}).keys())
    
    def get_current_profile_name(self) -> str:
        """获取当前配置名称"""
        config = self.get_current_config()
        return config.get('current_profile', 'default')
    
    def get_profile(self, profile_name: str) -> Optional[Dict[str, Any]]:
        """获取指定配置"""
        config = self.get_current_config()
        return config.get('profiles', {}).get(profile_name)
    
    def get_current_profile(self) -> Dict[str, Any]:
        """获取当前配置"""
        current_name = self.get_current_profile_name()
        profile = self.get_profile(current_name)
        if profile is None:
            # 返回默认配置
            return self.DEFAULT_CONFIG['profiles']['default'].copy()
        return profile
    
    def get_current_config(self) -> Dict[str, Any]:
        """获取当前完整配置"""
        if self._config_cache is None:
            self._config_cache = self.load()
        return self._config_cache
    
    def set_current_profile(self, profile_name: str) -> bool:
        """设置当前配置"""
        config = self.get_current_config()
        if profile_name not in config.get('profiles', {}):
            self.logger.error(f"Profile not found: {profile_name}")
            return False
        
        config['current_profile'] = profile_name
        return self.save(config)
    
    def add_profile(self, profile_name: str, profile_data: Dict[str, Any]) -> bool:
        """添加新配置"""
        if not profile_name or not profile_name.strip():
            self.logger.error("Profile name cannot be empty")
            return False
        
        config = self.get_current_config()
        
        # 修复配置数据
        profile_data = self._fix_profile_data(profile_data)
        
        config['profiles'][profile_name] = profile_data
        return self.save(config)
    
    def update_profile(self, profile_name: str, profile_data: Dict[str, Any]) -> bool:
        """更新配置"""
        config = self.get_current_config()
        if profile_name not in config.get('profiles', {}):
            self.logger.error(f"Profile not found: {profile_name}")
            return False
        
        # 修复配置数据
        profile_data = self._fix_profile_data(profile_data)
        
        config['profiles'][profile_name] = profile_data
        return self.save(config)
    
    def delete_profile(self, profile_name: str) -> bool:
        """删除配置"""
        if profile_name == 'default':
            self.logger.error("Cannot delete default profile")
            return False
        
        config = self.get_current_config()
        if profile_name not in config.get('profiles', {}):
            self.logger.error(f"Profile not found: {profile_name}")
            return False
        
        del config['profiles'][profile_name]
        
        # 如果删除的是当前配置，切换到默认配置
        if config.get('current_profile') == profile_name:
            config['current_profile'] = 'default'
        
        return self.save(config)
    
    def export_config(self, export_path: str, profile_name: Optional[str] = None) -> bool:
        """导出配置"""
        try:
            config = self.get_current_config()
            
            if profile_name:
                # 导出单个配置
                if profile_name not in config.get('profiles', {}):
                    self.logger.error(f"Profile not found: {profile_name}")
                    return False
                
                export_data = {
                    'profile_name': profile_name,
                    'profile_data': config['profiles'][profile_name],
                    'export_time': datetime.now().isoformat()
                }
            else:
                # 导出所有配置
                export_data = {
                    'all_profiles': config['profiles'],
                    'export_time': datetime.now().isoformat()
                }
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"Config exported to: {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export config: {e}")
            return False
    
    def import_config(self, import_path: str) -> bool:
        """导入配置"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            config = self.get_current_config()
            imported_profile_names = []  # 记录导入的配置名称

            if 'profile_name' in import_data and 'profile_data' in import_data:
                # 导入单个配置（导出格式）
                profile_name = import_data['profile_name']
                profile_data = import_data['profile_data']

                # 如果配置已存在，添加后缀
                original_name = profile_name
                counter = 1
                while profile_name in config['profiles']:
                    profile_name = f"{original_name}_{counter}"
                    counter += 1

                config['profiles'][profile_name] = self._fix_profile_data(profile_data)
                imported_profile_names.append(profile_name)

            elif 'all_profiles' in import_data:
                # 导入所有配置（批量导出格式）
                for name, data in import_data['all_profiles'].items():
                    # 避免覆盖现有配置
                    original_name = name
                    counter = 1
                    while name in config['profiles']:
                        name = f"{original_name}_{counter}"
                        counter += 1

                    config['profiles'][name] = self._fix_profile_data(data)
                    imported_profile_names.append(name)

            elif 'profiles' in import_data:
                # 导入完整配置文件格式
                for name, data in import_data['profiles'].items():
                    # 跳过默认配置，避免覆盖
                    if name == 'default':
                        continue

                    # 避免覆盖现有配置
                    original_name = name
                    counter = 1
                    while name in config['profiles']:
                        name = f"{original_name}_{counter}"
                        counter += 1

                    config['profiles'][name] = self._fix_profile_data(data)
                    imported_profile_names.append(name)

                # 如果导入的配置文件有设置，也导入设置
                if 'settings' in import_data:
                    config['settings'].update(import_data['settings'])

            else:
                self.logger.error("Invalid import file format")
                return False

            # 记录导入的配置名称，供界面使用
            self._last_imported_profiles = imported_profile_names
            
            success = self.save(config)
            if success:
                self.logger.info(f"Config imported from: {import_path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to import config: {e}")
            return False
    
    def get_settings(self) -> Dict[str, Any]:
        """获取设置"""
        config = self.get_current_config()
        return config.get('settings', self.DEFAULT_CONFIG['settings'].copy())
    
    def update_settings(self, settings: Dict[str, Any]) -> bool:
        """更新设置"""
        config = self.get_current_config()
        config['settings'].update(settings)
        return self.save(config)

    def get_last_imported_profiles(self) -> List[str]:
        """获取最后导入的配置名称列表"""
        return self._last_imported_profiles.copy()

    def clear_last_imported_profiles(self):
        """清空最后导入的配置记录"""
        self._last_imported_profiles = []
