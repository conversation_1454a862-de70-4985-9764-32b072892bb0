# -*- coding: utf-8 -*-
"""
Kcptun进程管理模块
负责kcptun进程的启动、停止、监控等功能
"""

import os
import sys
import time
import subprocess
import threading
import asyncio
from pathlib import Path
from typing import Optional, Dict, Any, Callable
from concurrent.futures import ThreadPoolExecutor

try:
    from ..logger import KcptunLogger
except ImportError:
    from logger import KcptunLogger


class ProcessManager:
    """Kcptun进程管理器"""
    
    def __init__(self, bin_dir: Path, logger: Optional[KcptunLogger] = None, test_mode: bool = False):
        self.bin_dir = Path(bin_dir)
        self.client_exe = self.bin_dir / "client_windows_amd64.exe"
        self.logger = logger or KcptunLogger()
        self.test_mode = test_mode

        # 进程相关
        self.process: Optional[subprocess.Popen] = None
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.executor = ThreadPoolExecutor(max_workers=2)

        # 回调函数
        self.status_callbacks: list[Callable[[bool], None]] = []

        # 验证客户端程序是否存在（测试模式下跳过）
        if not test_mode and not self.client_exe.exists():
            raise FileNotFoundError(f"Kcptun客户端程序不存在: {self.client_exe}")
        elif test_mode:
            self.logger.warning("运行在测试模式，跳过客户端程序检查")
    
    def add_status_callback(self, callback: Callable[[bool], None]):
        """添加状态变化回调函数"""
        self.status_callbacks.append(callback)
    
    def _notify_status_change(self, running: bool):
        """通知状态变化"""
        for callback in self.status_callbacks:
            try:
                callback(running)
            except Exception as e:
                self.logger.error(f"Status callback error: {e}")
    
    def build_command(self, config: Dict[str, Any]) -> list[str]:
        """构建kcptun启动命令"""
        cmd = [
            str(self.client_exe),
            "-r", f"{config['kcp_ip']}:{config['kcp_port']}",
            "-l", f":{config['local_port']}",
            "-key", config['kcp_password'],
        ]
        
        # 添加高级参数
        advanced = config.get('advanced', {})
        if advanced:
            cmd.extend([
                "-mode", advanced.get('mode', 'normal'),
                "-mtu", str(advanced.get('mtu', 1400)),
                "-sndwnd", str(advanced.get('sndwnd', 1024)),
                "-rcvwnd", str(advanced.get('rcvwnd', 1024)),
                "-crypt", advanced.get('crypt', 'aes-128'),
                "-datashard", str(advanced.get('datashard', 10)),
                "-parityshard", str(advanced.get('parityshard', 3)),
                "-dscp", str(advanced.get('dscp', 46)),
                "-keepalive", str(advanced.get('keepalive', 10))
            ])
            
            if advanced.get('nocomp', True):
                cmd.append("-nocomp")
        
        return cmd
    
    async def start_async(self, config: Dict[str, Any]) -> bool:
        """异步启动kcptun进程"""
        if self.running:
            self.logger.warning("Process is already running")
            return False
        
        try:
            # 确保之前的进程已完全停止
            if self.process:
                await self.stop_async()
                await asyncio.sleep(1)
            
            cmd = self.build_command(config)
            self.logger.info(f"Starting kcptun with command: {' '.join(cmd)}")
            
            # 在线程池中启动进程
            loop = asyncio.get_event_loop()
            self.process = await loop.run_in_executor(
                self.executor, self._start_process, cmd
            )
            
            # 等待短暂时间确认进程启动
            await asyncio.sleep(0.5)
            if self.process.poll() is not None:
                raise Exception("进程启动失败")
            
            self.running = True
            self.logger.info("Kcptun started successfully")
            
            # 启动异步监控
            asyncio.create_task(self._monitor_process_async())
            
            # 通知状态变化
            self._notify_status_change(True)
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start kcptun: {str(e)}")
            if self.process:
                try:
                    self.process.terminate()
                except:
                    pass
                self.process = None
            self.running = False
            self._notify_status_change(False)
            return False
    
    def _start_process(self, cmd: list[str]) -> subprocess.Popen:
        """在线程池中启动进程"""
        return subprocess.Popen(
            cmd,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
            cwd=str(self.bin_dir),
            env=os.environ.copy()
        )
    
    async def stop_async(self) -> bool:
        """异步停止kcptun进程"""
        if not self.process:
            return True
        
        try:
            self.logger.info("Stopping kcptun...")
            self.running = False
            
            # 在线程池中停止进程
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self.executor, self._stop_process)
            
            self.logger.info("Kcptun stopped")
            self._notify_status_change(False)
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping kcptun: {str(e)}")
            return False
        finally:
            self.process = None
            self.running = False
    
    def _stop_process(self):
        """在线程池中停止进程"""
        if not self.process:
            return
        
        try:
            # Windows下使用taskkill强制结束进程树
            if os.name == 'nt':
                try:
                    subprocess.run(
                        ['taskkill', '/F', '/T', '/PID', str(self.process.pid)], 
                        capture_output=True,
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        timeout=5
                    )
                except subprocess.TimeoutExpired:
                    self.process.kill()
            else:
                self.process.terminate()
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
            
            # 清理资源
            self._cleanup_process_resources()
            
        except Exception as e:
            self.logger.error(f"Error in _stop_process: {e}")
    
    def _cleanup_process_resources(self):
        """清理进程资源"""
        if not self.process:
            return
        
        try:
            if self.process.stdout:
                self.process.stdout.close()
        except Exception as e:
            self.logger.debug(f"Error closing stdout: {e}")
        
        try:
            if self.process.stderr:
                self.process.stderr.close()
        except Exception as e:
            self.logger.debug(f"Error closing stderr: {e}")
    
    async def _monitor_process_async(self):
        """异步监控进程状态"""
        try:
            while self.process and self.running:
                await asyncio.sleep(1)
                
                if self.process.poll() is not None:
                    self.running = False
                    self.logger.warning("Kcptun process terminated unexpectedly")
                    self._notify_status_change(False)
                    break
                    
        except Exception as e:
            self.logger.error(f"Monitor error: {str(e)}")
        finally:
            if self.running:
                self.running = False
                self._notify_status_change(False)
    
    def start_sync(self, config: Dict[str, Any]) -> bool:
        """同步启动（兼容旧接口）"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.start_async(config))
        except Exception as e:
            self.logger.error(f"Sync start error: {e}")
            return False
        finally:
            loop.close()
    
    def stop_sync(self) -> bool:
        """同步停止（兼容旧接口）"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.stop_async())
        except Exception as e:
            self.logger.error(f"Sync stop error: {e}")
            return False
        finally:
            loop.close()
    
    def is_running(self) -> bool:
        """检查进程是否运行中"""
        return self.running and self.process and self.process.poll() is None
    
    def get_process_info(self) -> Dict[str, Any]:
        """获取进程信息"""
        if not self.process:
            return {"running": False}
        
        return {
            "running": self.running,
            "pid": self.process.pid if self.process else None,
            "poll": self.process.poll() if self.process else None
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.running:
                self.stop_sync()
            
            if self.executor:
                self.executor.shutdown(wait=True)
                
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")
