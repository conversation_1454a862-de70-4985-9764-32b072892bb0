# -*- coding: utf-8 -*-
"""
输入验证模块
负责各种输入参数的验证
"""

import re
import socket
from typing import Dict, Any, List, Tuple, Optional
from urllib.parse import urlparse


class ValidationError(Exception):
    """验证错误异常"""
    pass


class Validator:
    """输入验证器"""
    
    # 支持的加密方式
    SUPPORTED_CRYPT = [
        "aes", "aes-128", "aes-192", "salsa20", "blowfish", 
        "twofish", "cast5", "3des", "tea", "xtea", "xor", "none"
    ]
    
    # 支持的传输模式
    SUPPORTED_MODES = ["normal", "fast", "fast2", "fast3"]
    
    @staticmethod
    def validate_ip(ip: str) -> bool:
        """验证IP地址"""
        if not ip or not isinstance(ip, str):
            return False
        
        # IPv4正则表达式
        ipv4_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if re.match(ipv4_pattern, ip):
            # 验证每个数字是否在0-255之间
            return all(0 <= int(num) <= 255 for num in ip.split('.'))
        
        # IPv6验证
        try:
            socket.inet_pton(socket.AF_INET6, ip)
            return True
        except socket.error:
            pass
        
        # 域名验证
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return bool(re.match(domain_pattern, ip))
    
    @staticmethod
    def validate_port(port: str) -> bool:
        """验证端口号"""
        try:
            port_num = int(port)
            return 1 <= port_num <= 65535
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_password(password: str) -> bool:
        """验证密码"""
        if not password or not isinstance(password, str):
            return False
        
        # 密码长度检查
        if len(password) < 1 or len(password) > 256:
            return False
        
        # 检查是否包含不可打印字符
        return password.isprintable()
    
    @staticmethod
    def validate_mtu(mtu: str) -> bool:
        """验证MTU值"""
        try:
            mtu_num = int(mtu)
            # MTU通常在576-9000之间
            return 576 <= mtu_num <= 9000
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_window_size(size: str) -> bool:
        """验证窗口大小"""
        try:
            size_num = int(size)
            # 窗口大小通常在1-65535之间
            return 1 <= size_num <= 65535
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_mode(mode: str) -> bool:
        """验证传输模式"""
        return mode in Validator.SUPPORTED_MODES
    
    @staticmethod
    def validate_crypt(crypt: str) -> bool:
        """验证加密方式"""
        return crypt in Validator.SUPPORTED_CRYPT
    
    @staticmethod
    def validate_shard(shard: str) -> bool:
        """验证数据片段数"""
        try:
            shard_num = int(shard)
            # 片段数通常在0-255之间
            return 0 <= shard_num <= 255
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_dscp(dscp: str) -> bool:
        """验证DSCP值"""
        try:
            dscp_num = int(dscp)
            # DSCP值在0-63之间
            return 0 <= dscp_num <= 63
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_keepalive(keepalive: str) -> bool:
        """验证心跳包时间"""
        try:
            keepalive_num = int(keepalive)
            # 心跳包时间通常在1-3600秒之间
            return 1 <= keepalive_num <= 3600
        except (ValueError, TypeError):
            return False
    
    @classmethod
    def validate_basic_config(cls, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证基本配置"""
        errors = []
        
        # 验证必填字段
        required_fields = ['kcp_ip', 'kcp_port', 'kcp_password', 'local_port']
        for field in required_fields:
            if not config.get(field):
                errors.append(f"字段 {field} 不能为空")
        
        # 验证IP地址
        if config.get('kcp_ip') and not cls.validate_ip(config['kcp_ip']):
            errors.append("无效的服务器IP地址")
        
        # 验证端口
        if config.get('kcp_port') and not cls.validate_port(config['kcp_port']):
            errors.append("无效的服务器端口")
        
        if config.get('local_port') and not cls.validate_port(config['local_port']):
            errors.append("无效的本地端口")
        
        # 验证密码
        if config.get('kcp_password') and not cls.validate_password(config['kcp_password']):
            errors.append("无效的连接密码")
        
        return len(errors) == 0, errors
    
    @classmethod
    def validate_advanced_config(cls, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证高级配置"""
        errors = []
        advanced = config.get('advanced', {})
        
        # 验证MTU
        if advanced.get('mtu') and not cls.validate_mtu(advanced['mtu']):
            errors.append("MTU值必须在576-9000之间")
        
        # 验证窗口大小
        if advanced.get('sndwnd') and not cls.validate_window_size(advanced['sndwnd']):
            errors.append("发送窗口大小必须在1-65535之间")
        
        if advanced.get('rcvwnd') and not cls.validate_window_size(advanced['rcvwnd']):
            errors.append("接收窗口大小必须在1-65535之间")
        
        # 验证传输模式
        if advanced.get('mode') and not cls.validate_mode(advanced['mode']):
            errors.append(f"不支持的传输模式: {advanced['mode']}")
        
        # 验证加密方式
        if advanced.get('crypt') and not cls.validate_crypt(advanced['crypt']):
            errors.append(f"不支持的加密方式: {advanced['crypt']}")
        
        # 验证数据片段
        if advanced.get('datashard') and not cls.validate_shard(advanced['datashard']):
            errors.append("数据片段数必须在0-255之间")
        
        if advanced.get('parityshard') and not cls.validate_shard(advanced['parityshard']):
            errors.append("校验片段数必须在0-255之间")
        
        # 验证DSCP
        if advanced.get('dscp') and not cls.validate_dscp(advanced['dscp']):
            errors.append("DSCP值必须在0-63之间")
        
        # 验证心跳包时间
        if advanced.get('keepalive') and not cls.validate_keepalive(advanced['keepalive']):
            errors.append("心跳包时间必须在1-3600秒之间")
        
        return len(errors) == 0, errors
    
    @classmethod
    def validate_full_config(cls, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证完整配置"""
        all_errors = []
        
        # 验证基本配置
        basic_valid, basic_errors = cls.validate_basic_config(config)
        all_errors.extend(basic_errors)
        
        # 验证高级配置
        advanced_valid, advanced_errors = cls.validate_advanced_config(config)
        all_errors.extend(advanced_errors)
        
        return len(all_errors) == 0, all_errors
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """验证URL格式"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    @staticmethod
    def validate_profile_name(name: str) -> Tuple[bool, str]:
        """验证配置名称"""
        if not name or not isinstance(name, str):
            return False, "配置名称不能为空"
        
        name = name.strip()
        if not name:
            return False, "配置名称不能为空"
        
        if len(name) > 50:
            return False, "配置名称不能超过50个字符"
        
        # 检查特殊字符
        invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in invalid_chars:
            if char in name:
                return False, f"配置名称不能包含字符: {char}"
        
        return True, ""
    
    @staticmethod
    def sanitize_input(text: str, max_length: int = 256) -> str:
        """清理输入文本"""
        if not isinstance(text, str):
            return ""
        
        # 移除首尾空白
        text = text.strip()
        
        # 限制长度
        if len(text) > max_length:
            text = text[:max_length]
        
        # 移除控制字符
        text = ''.join(char for char in text if char.isprintable())
        
        return text
    
    @classmethod
    def get_validation_suggestions(cls, config: Dict[str, Any]) -> List[str]:
        """获取配置优化建议"""
        suggestions = []
        advanced = config.get('advanced', {})
        
        # MTU建议
        mtu = advanced.get('mtu', '1400')
        try:
            mtu_num = int(mtu)
            if mtu_num > 1400:
                suggestions.append("MTU值较大可能导致分片，建议设置为1400或更小")
        except ValueError:
            pass
        
        # 窗口大小建议
        try:
            sndwnd = int(advanced.get('sndwnd', '1024'))
            rcvwnd = int(advanced.get('rcvwnd', '1024'))
            
            if sndwnd < 512 or rcvwnd < 512:
                suggestions.append("窗口大小较小可能影响性能，建议设置为1024或更大")
            elif sndwnd > 4096 or rcvwnd > 4096:
                suggestions.append("窗口大小较大可能占用更多内存")
        except ValueError:
            pass
        
        # 模式建议
        mode = advanced.get('mode', 'normal')
        if mode == 'normal':
            suggestions.append("normal模式较为保守，网络条件良好时可尝试fast模式")
        elif mode == 'fast3':
            suggestions.append("fast3模式较为激进，网络不稳定时可能导致丢包")
        
        return suggestions
