# -*- coding: utf-8 -*-
"""
系统托盘管理模块
"""

import os
import sys
import time
import threading
from pathlib import Path
from typing import Optional, TYPE_CHECKING

import pystray
from PIL import Image, ImageDraw, ImageFilter

if TYPE_CHECKING:
    from .main_window import MainWindow

try:
    from ..core.process_manager import ProcessManager
    from ..core.config_manager import ConfigManager
    from ..logger import KcptunLogger
except ImportError:
    from core.process_manager import ProcessManager
    from core.config_manager import ConfigManager
    from logger import KcptunLogger


class TrayManager:
    """系统托盘管理器"""
    
    def __init__(self, main_window: 'MainWindow', process_manager: ProcessManager,
                 config_manager: ConfigManager, logger: KcptunLogger):
        self.main_window = main_window
        self.process_manager = process_manager
        self.config_manager = config_manager
        self.logger = logger
        
        self.tray_icon: Optional[pystray.Icon] = None
        self.icon_path = Path("resources/icon.png")
        self.last_click_time = 0
        self.double_click_interval = 300  # 毫秒
        
        # 确保图标存在
        if not self.icon_path.exists():
            self._create_default_icon()
    
    def setup(self):
        """设置系统托盘"""
        try:
            image = Image.open(self.icon_path)
            
            # 创建自定义托盘图标类
            class CustomIcon(pystray._win32.Icon if os.name == 'nt' else pystray.Icon):
                def __init__(self, *args, **kwargs):
                    self.tray_manager = kwargs.pop('tray_manager')
                    super().__init__(*args, **kwargs)
                
                def _on_notify(self, wparam, lparam):
                    """处理托盘图标的通知事件"""
                    try:
                        # 处理左键点击
                        if lparam == 0x0201:  # WM_LBUTTONDOWN
                            current_time = int(time.time() * 1000)
                            time_diff = current_time - self.tray_manager.last_click_time
                            
                            if time_diff < self.tray_manager.double_click_interval:
                                # 双击 - 显示窗口
                                self.tray_manager.main_window.root.after(0, self.tray_manager.main_window.show_window)
                                self.tray_manager.last_click_time = 0
                            else:
                                # 单击 - 记录时间
                                self.tray_manager.last_click_time = current_time
                            return True
                        elif lparam == 0x0203:  # WM_LBUTTONDBLCLK
                            # 直接处理双击消息
                            self.tray_manager.main_window.root.after(0, self.tray_manager.main_window.show_window)
                            self.tray_manager.last_click_time = 0
                            return True
                            
                        return super()._on_notify(wparam, lparam) if hasattr(super(), '_on_notify') else False
                        
                    except Exception as e:
                        self.tray_manager.logger.error(f"Tray notify error: {e}")
                        return False
            
            # 创建菜单
            menu = self._create_menu()
            
            # 创建托盘图标
            self.tray_icon = CustomIcon(
                "Kcptun客户端",
                image,
                "Kcptun客户端\n双击显示主窗口\n右键显示菜单",
                menu=menu,
                tray_manager=self
            )
            
            # 在新线程中启动托盘图标
            threading.Thread(target=self.tray_icon.run, daemon=True).start()
            
            self.logger.info("System tray setup completed")
            
        except Exception as e:
            self.logger.error(f"Failed to setup tray: {e}")
            raise
    
    def _create_menu(self) -> pystray.Menu:
        """创建托盘菜单"""
        def create_profile_menu():
            """创建配置子菜单"""
            try:
                config = self.config_manager.get_current_config()
                profiles = list(config.get('profiles', {}).keys())
                current_profile = config.get('current_profile', 'default')
                
                def switch_profile(profile_name):
                    def handler(icon, item):
                        try:
                            self.config_manager.set_current_profile(profile_name)
                            # 通知主窗口更新
                            self.main_window.root.after(0, lambda: self.main_window.update_config(
                                self.config_manager.get_current_config()
                            ))
                            self.show_notification("配置切换", f"已切换到配置: {profile_name}")
                        except Exception as e:
                            self.logger.error(f"Switch profile error: {e}")
                    return handler
                
                return pystray.Menu(
                    *[pystray.MenuItem(
                        f"{'✓ ' if name == current_profile else '  '}{name}",
                        switch_profile(name)
                    ) for name in profiles]
                )
            except Exception as e:
                self.logger.error(f"Create profile menu error: {e}")
                return pystray.Menu()
        
        return pystray.Menu(
            pystray.MenuItem("显示主窗口", self._show_window),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("配置", create_profile_menu()),
            pystray.MenuItem(
                "启动服务", 
                self._start_service,
                enabled=not self.process_manager.is_running()
            ),
            pystray.MenuItem(
                "停止服务", 
                self._stop_service,
                enabled=self.process_manager.is_running()
            ),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("退出", self._quit_application)
        )
    
    def _show_window(self, icon=None, item=None):
        """显示主窗口"""
        self.main_window.show_window()
    
    def _start_service(self, icon=None, item=None):
        """从托盘启动服务"""
        try:
            current_profile = self.config_manager.get_current_profile()
            
            def start_async():
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    success = loop.run_until_complete(self.process_manager.start_async(current_profile))
                    if success:
                        self.show_notification("服务启动", "Kcptun服务已启动")
                    else:
                        self.show_notification("启动失败", "Kcptun服务启动失败")
                except Exception as e:
                    self.logger.error(f"Tray start service error: {e}")
                    self.show_notification("启动失败", f"启动失败: {e}")
                finally:
                    loop.close()
            
            threading.Thread(target=start_async, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"Tray start service error: {e}")
            self.show_notification("启动失败", f"启动失败: {e}")
    
    def _stop_service(self, icon=None, item=None):
        """从托盘停止服务"""
        try:
            def stop_async():
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    success = loop.run_until_complete(self.process_manager.stop_async())
                    if success:
                        self.show_notification("服务停止", "Kcptun服务已停止")
                    else:
                        self.show_notification("停止失败", "Kcptun服务停止失败")
                except Exception as e:
                    self.logger.error(f"Tray stop service error: {e}")
                    self.show_notification("停止失败", f"停止失败: {e}")
                finally:
                    loop.close()
            
            threading.Thread(target=stop_async, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"Tray stop service error: {e}")
            self.show_notification("停止失败", f"停止失败: {e}")
    
    def _quit_application(self, icon=None, item=None):
        """退出应用程序"""
        self.main_window.quit_application()
    
    def update_status(self, running: bool):
        """更新托盘菜单状态"""
        try:
            if self.tray_icon:
                # 更新菜单
                menu = self._create_menu()
                self.tray_icon.menu = menu
        except Exception as e:
            self.logger.error(f"Update tray status error: {e}")
    
    def show_notification(self, title: str, message: str):
        """显示托盘通知"""
        try:
            if self.tray_icon:
                self.tray_icon.notify(title, message)
        except Exception as e:
            self.logger.error(f"Show notification error: {e}")
    
    def stop(self):
        """停止托盘图标"""
        try:
            if self.tray_icon:
                self.tray_icon.stop()
                self.tray_icon = None
        except Exception as e:
            self.logger.error(f"Stop tray error: {e}")
    
    def _create_default_icon(self):
        """创建默认图标"""
        try:
            # 创建现代风格的图标
            size = 128
            img = Image.new('RGBA', (size, size), color=(0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # 使用现代配色方案
            primary_color = (52, 152, 219)     # 明亮的蓝色
            secondary_color = (46, 204, 113)   # 翠绿色
            bg_color = (41, 128, 185, 180)     # 半透明的深蓝色
            
            # 绘制主圆形背景
            padding = size // 8
            draw.ellipse([padding, padding, size-padding, size-padding], fill=bg_color)
            
            # 绘制动态效果线条
            line_width = size // 20
            for i in range(3):
                offset = i * (size//8)
                # 绘制动态曲线
                points = [
                    (padding+offset, size//2+offset),
                    (size//2, padding+offset),
                    (size-padding-offset, size//2+offset)
                ]
                draw.line(points, fill=primary_color, width=line_width)
                
                # 绘制反向曲线
                points = [
                    (padding+offset, size//2-offset),
                    (size//2, size-padding-offset),
                    (size-padding-offset, size//2-offset)
                ]
                draw.line(points, fill=secondary_color, width=line_width)
            
            # 添加发光效果
            glow = img.filter(ImageFilter.GaussianBlur(radius=2))
            img = Image.alpha_composite(img, glow)
            
            # 缩放到所需尺寸
            img = img.resize((32, 32), Image.Resampling.LANCZOS)
            
            # 确保目录存在并保存
            self.icon_path.parent.mkdir(parents=True, exist_ok=True)
            img.save(self.icon_path, 'PNG')
            
            self.logger.debug("Default icon created")
            
        except Exception as e:
            self.logger.error(f"Create default icon error: {e}")
            # 如果创建失败，创建一个简单的图标
            self._create_simple_icon()
    
    def _create_simple_icon(self):
        """创建简单图标（备用方案）"""
        try:
            size = 32
            img = Image.new('RGBA', (size, size), color=(52, 152, 219, 255))
            draw = ImageDraw.Draw(img)
            
            # 绘制简单的K字母
            draw.text((8, 8), "K", fill=(255, 255, 255, 255))
            
            self.icon_path.parent.mkdir(parents=True, exist_ok=True)
            img.save(self.icon_path, 'PNG')
            
        except Exception as e:
            self.logger.error(f"Create simple icon error: {e}")
