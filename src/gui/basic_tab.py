# -*- coding: utf-8 -*-
"""
基本设置标签页模块
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import Optional, Callable, Dict, Any
import asyncio
import threading

try:
    from ..core.config_manager import ConfigManager
    from ..core.process_manager import ProcessManager
    from ..core.validator import Validator
    from ..logger import Kc<PERSON>un<PERSON>ogger
except ImportError:
    from core.config_manager import ConfigManager
    from core.process_manager import ProcessManager
    from core.validator import Validator
    from logger import KcptunLogger


class BasicTab:
    """基本设置标签页"""
    
    def __init__(self, parent: ttk.Notebook, config_manager: ConfigManager, 
                 process_manager: ProcessManager, validator: Validator, logger: KcptunLogger):
        self.parent = parent
        self.config_manager = config_manager
        self.process_manager = process_manager
        self.validator = validator
        self.logger = logger
        
        # 创建主框架
        self.frame = ttk.Frame(parent)
        
        # 界面变量
        self.kcp_ip_var = tk.StringVar()
        self.kcp_port_var = tk.StringVar()
        self.kcp_password_var = tk.StringVar()
        self.local_port_var = tk.StringVar()
        self.profile_var = tk.StringVar()
        self.show_password_var = tk.BooleanVar(value=False)
        
        # 界面组件
        self.status_label: Optional[ttk.Label] = None
        self.profile_combo: Optional[ttk.Combobox] = None
        self.start_button: Optional[ttk.Button] = None
        self.stop_button: Optional[ttk.Button] = None
        self.password_entry: Optional[ttk.Entry] = None
        self.toggle_password_btn: Optional[ttk.Button] = None
        
        # 回调函数
        self.config_change_callback: Optional[Callable[[dict], None]] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_bindings()
        
        self.logger.debug("BasicTab initialized")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 状态显示区域
        self._create_status_section(main_container)
        
        # 基本连接设置
        self._create_connection_section(main_container)
        
        # 配置管理区域
        self._create_profile_section(main_container)
        
        # 操作按钮区域
        self._create_action_section(main_container)
    
    def _create_status_section(self, parent):
        """创建状态显示区域"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        status_left = ttk.Frame(status_frame)
        status_left.pack(side=tk.LEFT)
        
        ttk.Label(status_left, text="当前运行状态:").pack(side=tk.LEFT, padx=(0, 5))
        self.status_label = ttk.Label(status_left, text="未运行", style='Status.Stopped.TLabel')
        self.status_label.pack(side=tk.LEFT)
    
    def _create_connection_section(self, parent):
        """创建连接设置区域"""
        conn_frame = ttk.LabelFrame(parent, text="基本连接设置")
        conn_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        conn_frame.grid_columnconfigure(1, weight=1)
        
        # KCP IP
        ttk.Label(conn_frame, text="服务器IP:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ip_entry = ttk.Entry(conn_frame, textvariable=self.kcp_ip_var)
        ip_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)

        # KCP Port
        ttk.Label(conn_frame, text="服务器端口:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        port_entry = ttk.Entry(conn_frame, textvariable=self.kcp_port_var)
        port_entry.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)

        # KCP Password
        ttk.Label(conn_frame, text="连接密码:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        password_frame = ttk.Frame(conn_frame)
        password_frame.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        password_frame.grid_columnconfigure(0, weight=1)

        self.password_entry = ttk.Entry(password_frame, textvariable=self.kcp_password_var, show="*")
        self.password_entry.grid(row=0, column=0, sticky=tk.EW)

        self.toggle_password_btn = ttk.Button(
            password_frame,
            text="👀",
            command=self._toggle_password_visibility,
            width=3
        )
        self.toggle_password_btn.grid(row=0, column=1, padx=(5, 0))

        # Local Port
        ttk.Label(conn_frame, text="本地端口:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        local_port_entry = ttk.Entry(conn_frame, textvariable=self.local_port_var)
        local_port_entry.grid(row=3, column=1, sticky=tk.EW, padx=5, pady=5)
    
    def _create_profile_section(self, parent):
        """创建配置管理区域"""
        profile_frame = ttk.LabelFrame(parent, text="配置管理")
        profile_frame.pack(fill=tk.X, pady=10)
        
        # 配置选择
        select_frame = ttk.Frame(profile_frame)
        select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(select_frame, text="当前配置:").pack(side=tk.LEFT)
        self.profile_combo = ttk.Combobox(select_frame, textvariable=self.profile_var, state="readonly")
        self.profile_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # 配置管理按钮
        button_frame = ttk.Frame(profile_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="新建配置", command=self._new_profile).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存为新配置", command=self._save_as_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除配置", command=self._delete_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导入配置", command=self._import_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导出配置", command=self._export_config).pack(side=tk.LEFT, padx=5)
    
    def _create_action_section(self, parent):
        """创建操作按钮区域"""
        action_frame = ttk.Frame(parent)
        action_frame.pack(fill=tk.X, pady=10)
        
        # 左侧操作按钮
        left_frame = ttk.Frame(action_frame)
        left_frame.pack(side=tk.LEFT)
        
        self.start_button = ttk.Button(left_frame, text="启动", command=self._start_service, width=10)
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_button = ttk.Button(left_frame, text="停止", command=self._stop_service, width=10)
        self.stop_button.pack(side=tk.LEFT)
        self.stop_button.state(['disabled'])
        
        # 右侧配置按钮
        right_frame = ttk.Frame(action_frame)
        right_frame.pack(side=tk.RIGHT)
        
        ttk.Button(right_frame, text="保存配置", command=self._save_config, width=10).pack(side=tk.RIGHT)
        ttk.Button(right_frame, text="测试连接", command=self._test_connection, width=10).pack(side=tk.RIGHT, padx=(0, 5))
    
    def _setup_bindings(self):
        """设置事件绑定"""
        # 配置选择变化
        self.profile_combo.bind('<<ComboboxSelected>>', self._on_profile_selected)
        
        # 输入验证
        self.kcp_ip_var.trace('w', self._on_input_changed)
        self.kcp_port_var.trace('w', self._on_input_changed)
        self.kcp_password_var.trace('w', self._on_input_changed)
        self.local_port_var.trace('w', self._on_input_changed)
    
    def _toggle_password_visibility(self):
        """切换密码显示/隐藏"""
        if self.show_password_var.get():
            self.password_entry.configure(show='*')
            self.show_password_var.set(False)
        else:
            self.password_entry.configure(show='')
            self.show_password_var.set(True)
    
    def _on_input_changed(self, *args):
        """输入变化事件"""
        # 实时验证输入
        self._validate_inputs()
    
    def _validate_inputs(self) -> bool:
        """验证输入"""
        config = self._get_current_input()
        valid, errors = self.validator.validate_basic_config(config)
        
        # 更新启动按钮状态
        if valid and not self.process_manager.is_running():
            self.start_button.state(['!disabled'])
        else:
            self.start_button.state(['disabled'])
        
        return valid
    
    def _get_current_input(self) -> dict:
        """获取当前输入的配置"""
        return {
            'kcp_ip': self.kcp_ip_var.get().strip(),
            'kcp_port': self.kcp_port_var.get().strip(),
            'kcp_password': self.kcp_password_var.get(),
            'local_port': self.local_port_var.get().strip()
        }
    
    def _start_service(self):
        """启动服务"""
        if not self._validate_inputs():
            messagebox.showerror("错误", "请检查输入参数")
            return
        
        # 保存当前配置
        self._save_current_config()
        
        # 获取完整配置
        current_profile = self.config_manager.get_current_profile()
        
        # 异步启动
        def start_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success = loop.run_until_complete(self.process_manager.start_async(current_profile))
                if not success:
                    self.frame.after(0, lambda: messagebox.showerror("错误", "启动失败，请检查日志"))
            except Exception as e:
                self.logger.error(f"Start service error: {e}")
                self.frame.after(0, lambda: messagebox.showerror("错误", f"启动失败: {e}"))
            finally:
                loop.close()
        
        threading.Thread(target=start_async, daemon=True).start()
    
    def _stop_service(self):
        """停止服务"""
        def stop_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success = loop.run_until_complete(self.process_manager.stop_async())
                if not success:
                    self.frame.after(0, lambda: messagebox.showerror("错误", "停止失败"))
            except Exception as e:
                self.logger.error(f"Stop service error: {e}")
                self.frame.after(0, lambda: messagebox.showerror("错误", f"停止失败: {e}"))
            finally:
                loop.close()
        
        threading.Thread(target=stop_async, daemon=True).start()
    
    def _test_connection(self):
        """测试连接"""
        if not self._validate_inputs():
            messagebox.showerror("错误", "请检查输入参数")
            return
        
        # TODO: 实现连接测试功能
        messagebox.showinfo("提示", "连接测试功能开发中...")
    
    def _save_config(self):
        """保存配置"""
        try:
            self._save_current_config()
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            self.logger.error(f"Save config error: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def _save_current_config(self):
        """保存当前配置到配置管理器"""
        current_input = self._get_current_input()
        current_profile_name = self.profile_var.get()
        
        # 获取当前完整配置
        full_config = self.config_manager.get_current_config()
        current_profile = full_config['profiles'].get(current_profile_name, {})
        
        # 更新基本设置
        current_profile.update(current_input)
        
        # 保存配置
        self.config_manager.update_profile(current_profile_name, current_profile)
        
        # 触发配置变化回调
        if self.config_change_callback:
            self.config_change_callback(full_config)

    def _new_profile(self):
        """新建配置"""
        name = simpledialog.askstring("新建配置", "请输入配置名称:")
        if not name:
            return

        name = name.strip()
        valid, error_msg = self.validator.validate_profile_name(name)
        if not valid:
            messagebox.showerror("错误", error_msg)
            return

        # 检查是否已存在
        if name in self.config_manager.get_profile_names():
            messagebox.showerror("错误", "配置名称已存在")
            return

        # 创建默认配置
        default_profile = self.config_manager.DEFAULT_CONFIG['profiles']['default'].copy()

        if self.config_manager.add_profile(name, default_profile):
            self._refresh_profile_list()
            self.profile_var.set(name)
            self._load_profile(name)
            messagebox.showinfo("成功", f"配置 '{name}' 创建成功")
        else:
            messagebox.showerror("错误", "创建配置失败")

    def _save_as_profile(self):
        """保存为新配置"""
        name = simpledialog.askstring("保存新配置", "请输入配置名称:")
        if not name:
            return

        name = name.strip()
        valid, error_msg = self.validator.validate_profile_name(name)
        if not valid:
            messagebox.showerror("错误", error_msg)
            return

        # 检查是否已存在
        if name in self.config_manager.get_profile_names():
            if not messagebox.askyesno("确认", f"配置 '{name}' 已存在，是否覆盖？"):
                return

        # 获取当前输入作为新配置
        current_input = self._get_current_input()
        current_profile = self.config_manager.get_current_profile()

        # 合并配置
        new_profile = current_profile.copy()
        new_profile.update(current_input)

        if self.config_manager.add_profile(name, new_profile):
            self._refresh_profile_list()
            self.profile_var.set(name)
            messagebox.showinfo("成功", f"配置 '{name}' 保存成功")
        else:
            messagebox.showerror("错误", "保存配置失败")

    def _delete_profile(self):
        """删除配置"""
        current_profile = self.profile_var.get()
        if current_profile == 'default':
            messagebox.showerror("错误", "默认配置不能删除")
            return

        if not messagebox.askyesno("确认", f"是否删除配置 '{current_profile}'？"):
            return

        if self.config_manager.delete_profile(current_profile):
            self._refresh_profile_list()
            self.profile_var.set('default')
            self._load_profile('default')
            messagebox.showinfo("成功", f"配置 '{current_profile}' 删除成功")
        else:
            messagebox.showerror("错误", "删除配置失败")

    def _import_config(self):
        """导入配置"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if not file_path:
            return

        try:
            if self.config_manager.import_config(file_path):
                # 刷新配置列表
                self._refresh_profile_list()

                # 获取导入的配置名称
                imported_profiles = self.config_manager.get_last_imported_profiles()

                if imported_profiles:
                    # 切换到第一个导入的配置
                    target_profile = imported_profiles[0]
                    self.profile_var.set(target_profile)
                    self._load_profile(target_profile)
                    self.config_manager.set_current_profile(target_profile)

                    # 获取更新后的配置
                    updated_config = self.config_manager.get_current_config()

                    # 通知其他标签页更新
                    if self.config_change_callback:
                        self.config_change_callback(updated_config)

                    # 显示导入成功信息
                    if len(imported_profiles) == 1:
                        messagebox.showinfo("成功", f"配置导入成功！已切换到配置: {target_profile}")
                    else:
                        profile_list = "、".join(imported_profiles)
                        messagebox.showinfo("成功", f"成功导入 {len(imported_profiles)} 个配置: {profile_list}\n已切换到配置: {target_profile}")
                else:
                    messagebox.showinfo("提示", "配置文件导入成功，但没有新的配置被添加")

                # 清空导入记录
                self.config_manager.clear_last_imported_profiles()
            else:
                messagebox.showerror("错误", "配置导入失败")
        except Exception as e:
            self.logger.error(f"Import config error: {e}")
            messagebox.showerror("错误", f"导入配置失败: {e}")

    def _export_config(self):
        """导出配置"""
        from tkinter import filedialog

        current_profile = self.profile_var.get()

        file_path = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
            initialfile=f"kcptun_config_{current_profile}.json"
        )

        if not file_path:
            return

        try:
            if self.config_manager.export_config(file_path, current_profile):
                messagebox.showinfo("成功", f"配置已导出到: {file_path}")
            else:
                messagebox.showerror("错误", "配置导出失败")
        except Exception as e:
            self.logger.error(f"Export config error: {e}")
            messagebox.showerror("错误", f"导出配置失败: {e}")

    def _on_profile_selected(self, event):
        """配置选择变化事件"""
        profile_name = self.profile_var.get()
        if not profile_name:
            return

        # 保存当前配置
        try:
            self._save_current_config()
        except Exception as e:
            self.logger.warning(f"Failed to save current config: {e}")

        # 加载新配置
        self._load_profile(profile_name)

        # 设置为当前配置
        self.config_manager.set_current_profile(profile_name)

    def _load_profile(self, profile_name: str):
        """加载指定配置"""
        profile = self.config_manager.get_profile(profile_name)
        if not profile:
            self.logger.error(f"Profile not found: {profile_name}")
            return

        # 更新界面
        self.kcp_ip_var.set(profile.get('kcp_ip', ''))
        self.kcp_port_var.set(profile.get('kcp_port', ''))
        self.kcp_password_var.set(profile.get('kcp_password', ''))
        self.local_port_var.set(profile.get('local_port', ''))

    def _refresh_profile_list(self):
        """刷新配置列表"""
        profile_names = self.config_manager.get_profile_names()
        self.profile_combo['values'] = profile_names

    def load_config(self, config: dict):
        """加载配置到界面"""
        try:
            # 更新配置列表
            self._refresh_profile_list()

            # 设置当前配置
            current_profile = config.get('current_profile', 'default')
            self.profile_var.set(current_profile)

            # 加载配置内容
            self._load_profile(current_profile)

        except Exception as e:
            self.logger.error(f"Load config error: {e}")

    def sync_config(self, config: dict):
        """同步配置（从其他标签页）"""
        # 只更新配置列表，不改变当前输入
        self._refresh_profile_list()

    def update_status(self, running: bool):
        """更新状态显示"""
        if running:
            self.status_label.config(text="运行中", style='Status.Running.TLabel')
            self.start_button.state(['disabled'])
            self.stop_button.state(['!disabled'])
        else:
            self.status_label.config(text="未运行", style='Status.Stopped.TLabel')
            if self._validate_inputs():
                self.start_button.state(['!disabled'])
            else:
                self.start_button.state(['disabled'])
            self.stop_button.state(['disabled'])

    def set_config_change_callback(self, callback: Callable[[dict], None]):
        """设置配置变化回调"""
        self.config_change_callback = callback
