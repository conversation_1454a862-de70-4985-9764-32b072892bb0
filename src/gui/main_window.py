# -*- coding: utf-8 -*-
"""
主窗口模块
负责主窗口的创建和管理
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import asyncio
from pathlib import Path
from typing import Optional

try:
    from .basic_tab import BasicTab
    from .advanced_tab import AdvancedTab
    from .tray_manager import TrayManager
    from ..core.process_manager import ProcessManager
    from ..core.config_manager import ConfigManager
    from ..core.validator import Validator
    from ..logger import KcptunLogger
except ImportError:
    from gui.basic_tab import BasicTab
    from gui.advanced_tab import AdvancedTab
    from gui.tray_manager import TrayManager
    from core.process_manager import ProcessManager
    from core.config_manager import ConfigManager
    from core.validator import Validator
    from logger import KcptunLogger


class MainWindow:
    """主窗口类"""

    def __init__(self, test_mode: bool = False):
        self.root = tk.Tk()
        self.root.title("Kcptun客户端 v2.0")
        self.root.geometry("650x600")
        self.root.minsize(650, 600)

        # 核心组件
        self.logger = KcptunLogger()
        self.config_manager = ConfigManager(logger=self.logger)
        self.process_manager = ProcessManager(
            bin_dir=Path("bin"),
            logger=self.logger,
            test_mode=test_mode
        )
        self.validator = Validator()
        
        # GUI组件
        self.basic_tab: Optional[BasicTab] = None
        self.advanced_tab: Optional[AdvancedTab] = None
        self.tray_manager: Optional[TrayManager] = None
        self.notebook: Optional[ttk.Notebook] = None
        
        # 状态
        self.current_config = {}
        
        # 初始化
        self._setup_window()
        self._setup_styles()
        self._create_widgets()
        self._setup_callbacks()
        self._load_initial_config()
        self._setup_tray()
        
        # 绑定窗口事件
        self.root.protocol('WM_DELETE_WINDOW', self._on_window_close)
        
        self.logger.info("Main window initialized")
    
    def _setup_window(self):
        """设置窗口属性"""
        # 设置窗口图标
        self._set_window_icon()

        # 设置DPI感知
        if os.name == 'nt':
            try:
                from ctypes import windll
                windll.shcore.SetProcessDpiAwareness(1)
            except Exception:
                pass

    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            # 首先尝试使用ICO文件
            ico_path = Path("resources/icon.ico")
            if ico_path.exists():
                self.root.iconbitmap(str(ico_path))
                return

            # 如果没有ICO文件，尝试从PNG创建
            png_path = Path("resources/icon.png")
            if png_path.exists():
                try:
                    from PIL import Image
                    # 加载PNG图像
                    img = Image.open(png_path)
                    # 转换为ICO格式并保存
                    ico_path = Path("resources/icon.ico")
                    img.save(ico_path, format='ICO', sizes=[(32, 32)])
                    self.root.iconbitmap(str(ico_path))
                    return
                except ImportError:
                    # 如果没有PIL，使用PhotoImage作为备选
                    try:
                        icon = tk.PhotoImage(file=str(png_path))
                        self.root.iconphoto(True, icon)
                        return
                    except Exception:
                        pass
                except Exception as e:
                    self.logger.debug(f"Failed to convert PNG to ICO: {e}")

            # 如果都失败了，创建一个简单的默认图标
            self._create_default_icon()

        except Exception as e:
            self.logger.debug(f"Failed to set window icon: {e}")

    def _create_default_icon(self):
        """创建默认窗口图标"""
        try:
            # 创建一个简单的默认图标
            import tempfile
            import os

            # 创建一个简单的ICO文件内容（16x16像素）
            ico_data = (
                b'\x00\x00\x01\x00\x01\x00\x10\x10\x00\x00\x01\x00\x08\x00h\x05\x00\x00'
                b'\x16\x00\x00\x00(\x00\x00\x00\x10\x00\x00\x00 \x00\x00\x00\x01\x00\x08\x00'
                b'\x00\x00\x00\x00@\x05\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00'
                b'\x00\x01\x00\x00'
            )

            # 创建临时ICO文件
            with tempfile.NamedTemporaryFile(suffix='.ico', delete=False) as f:
                f.write(ico_data)
                temp_ico_path = f.name

            try:
                self.root.iconbitmap(temp_ico_path)
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_ico_path)
                except Exception:
                    pass

        except Exception as e:
            self.logger.debug(f"Failed to create default icon: {e}")
    
    def _setup_styles(self):
        """设置全局样式"""
        style = ttk.Style()
        
        # 使用系统默认主题
        if sys.platform.startswith('win'):
            try:
                style.theme_use('vista')
            except Exception:
                style.theme_use('default')
        
        # 设置全局主题色
        bg_color = '#ffffff'
        text_color = '#000000'
        
        # 配置控件样式
        widgets = ['TFrame', 'TLabelframe', 'TLabel', 'TEntry', 'TButton', 'TCheckbutton']
        for widget in widgets:
            style.configure(widget, background=bg_color, foreground=text_color)
        
        # 特殊样式配置
        style.configure('TLabelframe.Label', foreground=text_color)
        style.configure('TEntry', fieldbackground=bg_color)
        style.configure('TButton', padding=(10, 5))
        style.configure('TNotebook', background=bg_color)
        style.configure('TNotebook.Tab', padding=(10, 5))
        
        # 状态标签样式
        style.configure('Status.Running.TLabel', foreground='#34a853')  # 绿色
        style.configure('Status.Stopped.TLabel', foreground='#ea4335')  # 红色
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建标签页
        self.basic_tab = BasicTab(
            parent=self.notebook,
            config_manager=self.config_manager,
            process_manager=self.process_manager,
            validator=self.validator,
            logger=self.logger
        )
        
        self.advanced_tab = AdvancedTab(
            parent=self.notebook,
            config_manager=self.config_manager,
            validator=self.validator,
            logger=self.logger
        )
        
        # 添加标签页到notebook
        self.notebook.add(self.basic_tab.frame, text="基本设置")
        self.notebook.add(self.advanced_tab.frame, text="高级设置")
    
    def _setup_callbacks(self):
        """设置回调函数"""
        # 进程状态变化回调
        self.process_manager.add_status_callback(self._on_process_status_changed)
        
        # 配置变化回调
        if self.basic_tab:
            self.basic_tab.set_config_change_callback(self._on_config_changed)
        
        if self.advanced_tab:
            self.advanced_tab.set_config_change_callback(self._on_config_changed)
    
    def _load_initial_config(self):
        """加载初始配置"""
        try:
            self.current_config = self.config_manager.load()
            
            # 更新界面
            if self.basic_tab:
                self.basic_tab.load_config(self.current_config)
            
            if self.advanced_tab:
                self.advanced_tab.load_config(self.current_config)
                
            self.logger.info("Initial configuration loaded")
            
        except Exception as e:
            self.logger.error(f"Failed to load initial config: {e}")
            messagebox.showerror("错误", f"加载配置失败: {e}")
    
    def _setup_tray(self):
        """设置系统托盘"""
        try:
            self.tray_manager = TrayManager(
                main_window=self,
                process_manager=self.process_manager,
                config_manager=self.config_manager,
                logger=self.logger
            )
            self.tray_manager.setup()
            
        except Exception as e:
            self.logger.error(f"Failed to setup tray: {e}")
            # 托盘设置失败不应该阻止程序运行
    
    def _on_process_status_changed(self, running: bool):
        """进程状态变化回调"""
        def update_ui():
            if self.basic_tab:
                self.basic_tab.update_status(running)
            
            if self.tray_manager:
                self.tray_manager.update_status(running)
        
        # 在主线程中更新UI
        self.root.after(0, update_ui)
    
    def _on_config_changed(self, config: dict):
        """配置变化回调"""
        self.current_config = config
        
        # 同步配置到其他标签页
        if self.basic_tab:
            self.basic_tab.sync_config(config)
        
        if self.advanced_tab:
            self.advanced_tab.sync_config(config)
    
    def _on_window_close(self):
        """窗口关闭事件"""
        settings = self.config_manager.get_settings()
        
        if settings.get('minimize_to_tray', True) and self.tray_manager:
            # 最小化到托盘
            self.hide_window()
            if self.tray_manager:
                self.tray_manager.show_notification(
                    "Kcptun客户端已最小化到系统托盘",
                    "程序仍在后台运行"
                )
        else:
            # 直接退出
            self.quit_application()
    
    def show_window(self):
        """显示窗口"""
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
        self.root.attributes('-topmost', True)
        self.root.update()
        self.root.attributes('-topmost', False)
    
    def hide_window(self):
        """隐藏窗口"""
        self.root.withdraw()
    
    def quit_application(self):
        """退出应用程序"""
        try:
            self.logger.info("Application shutting down...")

            # 停止进程
            if self.process_manager:
                self.process_manager.cleanup()

            # 停止托盘
            if self.tray_manager:
                self.tray_manager.stop()

            # 关闭日志
            if self.logger:
                self.logger.close()

            # 销毁窗口
            self.root.destroy()

        except Exception as e:
            print(f"Error during shutdown: {e}")
        finally:
            # 注意：不在这里调用sys.exit()，让主程序处理退出
            pass
    
    def run(self):
        """运行主循环"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.quit_application()
        except Exception as e:
            self.logger.error(f"Main loop error: {e}")
            self.quit_application()
    
    def get_current_config(self) -> dict:
        """获取当前配置"""
        return self.current_config.copy()
    
    def update_config(self, config: dict):
        """更新配置"""
        self.current_config = config
        self._on_config_changed(config)
    
    def show_error(self, title: str, message: str):
        """显示错误对话框"""
        messagebox.showerror(title, message)
    
    def show_info(self, title: str, message: str):
        """显示信息对话框"""
        messagebox.showinfo(title, message)
    
    def show_warning(self, title: str, message: str):
        """显示警告对话框"""
        messagebox.showwarning(title, message)
    
    def ask_yes_no(self, title: str, message: str) -> bool:
        """显示是否确认对话框"""
        return messagebox.askyesno(title, message)
