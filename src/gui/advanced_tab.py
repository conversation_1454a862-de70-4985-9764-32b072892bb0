# -*- coding: utf-8 -*-
"""
高级设置标签页模块
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable, Dict, Any

try:
    from ..core.config_manager import ConfigManager
    from ..core.validator import Validator
    from ..logger import KcptunLogger
except ImportError:
    from core.config_manager import ConfigManager
    from core.validator import Validator
    from logger import KcptunLogger


class AdvancedTab:
    """高级设置标签页"""
    
    def __init__(self, parent: ttk.Notebook, config_manager: ConfigManager, 
                 validator: Validator, logger: KcptunLogger):
        self.parent = parent
        self.config_manager = config_manager
        self.validator = validator
        self.logger = logger
        
        # 创建主框架
        self.frame = ttk.Frame(parent)
        
        # 界面变量
        self.mtu_var = tk.StringVar(value="1400")
        self.sndwnd_var = tk.StringVar(value="1024")
        self.rcvwnd_var = tk.StringVar(value="1024")
        self.mode_var = tk.StringVar(value="normal")
        self.crypt_var = tk.StringVar(value="aes-128")
        self.datashard_var = tk.StringVar(value="10")
        self.parityshard_var = tk.StringVar(value="3")
        self.dscp_var = tk.StringVar(value="46")
        self.keepalive_var = tk.StringVar(value="10")
        self.nocomp_var = tk.BooleanVar(value=True)
        self.enable_logging_var = tk.BooleanVar(value=True)
        
        # 回调函数
        self.config_change_callback: Optional[Callable[[dict], None]] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_bindings()
        
        self.logger.debug("AdvancedTab initialized")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 高级参数设置
        self._create_advanced_section(main_container)
        
        # 其他设置
        self._create_other_settings_section(main_container)
        
        # 操作按钮
        self._create_action_section(main_container)
    
    def _create_advanced_section(self, parent):
        """创建高级参数设置区域"""
        advanced_frame = ttk.LabelFrame(parent, text="高级参数设置")
        advanced_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        advanced_frame.grid_columnconfigure(1, weight=1)
        
        row = 0
        
        # MTU
        ttk.Label(advanced_frame, text="MTU:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        mtu_entry = ttk.Entry(advanced_frame, textvariable=self.mtu_var)
        mtu_entry.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 发送窗口
        row += 1
        ttk.Label(advanced_frame, text="发送窗口(sndwnd):").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        sndwnd_entry = ttk.Entry(advanced_frame, textvariable=self.sndwnd_var)
        sndwnd_entry.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 接收窗口
        row += 1
        ttk.Label(advanced_frame, text="接收窗口(rcvwnd):").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        rcvwnd_entry = ttk.Entry(advanced_frame, textvariable=self.rcvwnd_var)
        rcvwnd_entry.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 传输模式
        row += 1
        ttk.Label(advanced_frame, text="传输模式:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        mode_combo = ttk.Combobox(advanced_frame, textvariable=self.mode_var,
                                 values=self.validator.SUPPORTED_MODES, state="readonly")
        mode_combo.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 加密方式
        row += 1
        ttk.Label(advanced_frame, text="加密方式:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        crypt_combo = ttk.Combobox(advanced_frame, textvariable=self.crypt_var,
                                  values=self.validator.SUPPORTED_CRYPT, state="readonly")
        crypt_combo.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 数据片段
        row += 1
        ttk.Label(advanced_frame, text="数据片段(datashard):").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        datashard_entry = ttk.Entry(advanced_frame, textvariable=self.datashard_var)
        datashard_entry.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 校验片段
        row += 1
        ttk.Label(advanced_frame, text="校验片段(parityshard):").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        parityshard_entry = ttk.Entry(advanced_frame, textvariable=self.parityshard_var)
        parityshard_entry.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # DSCP
        row += 1
        ttk.Label(advanced_frame, text="DSCP:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        dscp_entry = ttk.Entry(advanced_frame, textvariable=self.dscp_var)
        dscp_entry.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 心跳包时间
        row += 1
        ttk.Label(advanced_frame, text="心跳包时间(keepalive):").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        keepalive_entry = ttk.Entry(advanced_frame, textvariable=self.keepalive_var)
        keepalive_entry.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)
    
    def _create_other_settings_section(self, parent):
        """创建其他设置区域"""
        other_frame = ttk.LabelFrame(parent, text="其他设置")
        other_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 复选框区域
        checkbox_frame = ttk.Frame(other_frame)
        checkbox_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 禁用数据压缩
        ttk.Checkbutton(
            checkbox_frame,
            text="禁用数据压缩",
            variable=self.nocomp_var
        ).pack(side=tk.LEFT, padx=(0, 20))
        
        # 启用日志输出
        ttk.Checkbutton(
            checkbox_frame,
            text="启用日志输出",
            variable=self.enable_logging_var,
            command=self._on_logging_changed
        ).pack(side=tk.LEFT)
    
    def _create_action_section(self, parent):
        """创建操作按钮区域"""
        action_frame = ttk.Frame(parent)
        action_frame.pack(fill=tk.X)
        
        # 左侧按钮
        left_frame = ttk.Frame(action_frame)
        left_frame.pack(side=tk.LEFT)
        
        ttk.Button(left_frame, text="重置为默认值", command=self._reset_to_defaults).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_frame, text="获取优化建议", command=self._show_optimization_suggestions).pack(side=tk.LEFT)
        
        # 右侧按钮
        right_frame = ttk.Frame(action_frame)
        right_frame.pack(side=tk.RIGHT)
        
        ttk.Button(right_frame, text="保存配置", command=self._save_config).pack(side=tk.RIGHT)
    
    def _setup_bindings(self):
        """设置事件绑定"""
        # 输入变化监听
        variables = [
            self.mtu_var, self.sndwnd_var, self.rcvwnd_var, self.mode_var,
            self.crypt_var, self.datashard_var, self.parityshard_var,
            self.dscp_var, self.keepalive_var, self.nocomp_var
        ]
        
        for var in variables:
            var.trace('w', self._on_input_changed)
    
    def _on_input_changed(self, *args):
        """输入变化事件"""
        # 实时验证
        self._validate_inputs()
    
    def _validate_inputs(self) -> bool:
        """验证输入"""
        config = {'advanced': self._get_current_input()}
        valid, errors = self.validator.validate_advanced_config(config)
        
        if not valid:
            # 可以在这里显示验证错误，但不要太频繁
            pass
        
        return valid
    
    def _get_current_input(self) -> dict:
        """获取当前输入的高级配置"""
        return {
            'mtu': self.mtu_var.get().strip(),
            'sndwnd': self.sndwnd_var.get().strip(),
            'rcvwnd': self.rcvwnd_var.get().strip(),
            'mode': self.mode_var.get(),
            'crypt': self.crypt_var.get(),
            'datashard': self.datashard_var.get().strip(),
            'parityshard': self.parityshard_var.get().strip(),
            'dscp': self.dscp_var.get().strip(),
            'keepalive': self.keepalive_var.get().strip(),
            'nocomp': self.nocomp_var.get()
        }
    
    def _on_logging_changed(self):
        """日志设置变化"""
        settings = self.config_manager.get_settings()
        settings['enable_logging'] = self.enable_logging_var.get()
        self.config_manager.update_settings(settings)
    
    def _reset_to_defaults(self):
        """重置为默认值"""
        if not messagebox.askyesno("确认", "是否要将所有高级设置重置为默认值？"):
            return
        
        defaults = self.config_manager.DEFAULT_CONFIG['profiles']['default']['advanced']
        
        self.mtu_var.set(defaults['mtu'])
        self.sndwnd_var.set(defaults['sndwnd'])
        self.rcvwnd_var.set(defaults['rcvwnd'])
        self.mode_var.set(defaults['mode'])
        self.crypt_var.set(defaults['crypt'])
        self.datashard_var.set(defaults['datashard'])
        self.parityshard_var.set(defaults['parityshard'])
        self.dscp_var.set(defaults['dscp'])
        self.keepalive_var.set(defaults['keepalive'])
        self.nocomp_var.set(defaults['nocomp'])
        
        messagebox.showinfo("成功", "高级设置已重置为默认值")
    
    def _show_optimization_suggestions(self):
        """显示优化建议"""
        current_config = {'advanced': self._get_current_input()}
        suggestions = self.validator.get_validation_suggestions(current_config)
        
        if suggestions:
            suggestion_text = "\n".join(f"• {s}" for s in suggestions)
            messagebox.showinfo("优化建议", f"根据当前配置，建议：\n\n{suggestion_text}")
        else:
            messagebox.showinfo("优化建议", "当前配置看起来不错，没有特别的优化建议。")
    
    def _save_config(self):
        """保存配置"""
        try:
            self._save_current_config()
            messagebox.showinfo("成功", "高级配置已保存")
        except Exception as e:
            self.logger.error(f"Save advanced config error: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def _save_current_config(self):
        """保存当前配置到配置管理器"""
        current_input = self._get_current_input()
        current_profile_name = self.config_manager.get_current_profile_name()
        
        # 获取当前完整配置
        full_config = self.config_manager.get_current_config()
        current_profile = full_config['profiles'].get(current_profile_name, {})
        
        # 更新高级设置
        if 'advanced' not in current_profile:
            current_profile['advanced'] = {}
        current_profile['advanced'].update(current_input)
        
        # 保存配置
        self.config_manager.update_profile(current_profile_name, current_profile)
        
        # 触发配置变化回调
        if self.config_change_callback:
            self.config_change_callback(full_config)

    def load_config(self, config: dict):
        """加载配置到界面"""
        try:
            current_profile_name = config.get('current_profile', 'default')
            profile = config.get('profiles', {}).get(current_profile_name, {})
            advanced = profile.get('advanced', {})

            # 加载高级设置
            self.mtu_var.set(advanced.get('mtu', '1400'))
            self.sndwnd_var.set(advanced.get('sndwnd', '1024'))
            self.rcvwnd_var.set(advanced.get('rcvwnd', '1024'))
            self.mode_var.set(advanced.get('mode', 'normal'))
            self.crypt_var.set(advanced.get('crypt', 'aes-128'))
            self.datashard_var.set(advanced.get('datashard', '10'))
            self.parityshard_var.set(advanced.get('parityshard', '3'))
            self.dscp_var.set(advanced.get('dscp', '46'))
            self.keepalive_var.set(advanced.get('keepalive', '10'))
            self.nocomp_var.set(advanced.get('nocomp', True))

            # 加载其他设置
            settings = config.get('settings', {})
            self.enable_logging_var.set(settings.get('enable_logging', True))

        except Exception as e:
            self.logger.error(f"Load advanced config error: {e}")

    def sync_config(self, config: dict):
        """同步配置（从其他标签页）"""
        # 高级设置通常不需要从其他页面同步
        pass

    def set_config_change_callback(self, callback: Callable[[dict], None]):
        """设置配置变化回调"""
        self.config_change_callback = callback
