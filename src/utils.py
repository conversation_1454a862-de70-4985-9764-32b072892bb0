import re
import os
import sys
import winreg
import subprocess
from pathlib import Path

def validate_port(port):
    """验证端口号是否有效"""
    try:
        port = int(port)
        return 0 < port < 65536
    except ValueError:
        return False

def is_valid_ip(ip):
    """验证IP地址是否有效"""
    pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    if not re.match(pattern, ip):
        return False
    
    # 验证每个数字是否在0-255之间
    return all(0 <= int(num) <= 255 for num in ip.split('.')) 

def set_autostart(enable: bool, app_name: str = "KcptunGUI"):
    """设置或取消开机启动"""
    key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
    executable_path = str(Path(sys.executable).parent / "run.py")
    
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_ALL_ACCESS) as key:
            if enable:
                winreg.SetValueEx(key, app_name, 0, winreg.REG_SZ, f'"{sys.executable}" "{executable_path}"')
            else:
                try:
                    winreg.DeleteValue(key, app_name)
                except FileNotFoundError:
                    pass
        return True
    except Exception as e:
        print(f"设置开机启动失败: {str(e)}")
        return False

def is_autostart_enabled(app_name: str = "KcptunGUI") -> bool:
    """检查是否已设置开机启动"""
    key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_READ) as key:
            winreg.QueryValueEx(key, app_name)
            return True
    except FileNotFoundError:
        return False
    except Exception:
        return False 