# -*- coding: utf-8 -*-

import os
import sys
import locale
import atexit

# Windows系统设置
if sys.platform.startswith('win'):
    try:
        # 设置控制台代码页
        import ctypes
        kernel32 = ctypes.windll.kernel32
        kernel32.SetConsoleOutputCP(65001)  # 设置控制台代码页为UTF-8
    except:
        pass

# 设置环境变量
os.environ['PYSTRAY_DEBUG'] = '1'
os.environ['PYTHONIOENCODING'] = 'utf-8'

import pystray
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import subprocess
import threading
from .config import Config
from .utils import validate_port, is_valid_ip, set_autostart, is_autostart_enabled
from pathlib import Path
from PIL import Image, ImageDraw, ImageFilter
import time
from .logger import KcptunLogger

class KcptunGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Kcptun客户端")
        self.root.geometry("605x550")
        
        # 设置窗口最小尺寸
        self.root.minsize(605, 550)
        
        # 设置DPI感知
        if os.name == 'nt':
            try:
                from ctypes import windll
                windll.shcore.SetProcessDpiAwareness(1)
            except:
                pass
        
        # 设置全局样式
        self.setup_styles()
        
        # 等待窗口创建完成后再创建按钮
        self.root.after(100, self.create_titlebar_menu)
        
        # 绑定窗口大小改变事件
        self.root.bind('<Configure>', self.on_window_configure)
        
        # 设置窗口图标
        self.icon_path = Path("resources") / "icon.png"
        if not self.icon_path.exists():
            self.create_default_icon()
        
        # 初始化路径和配置
        self.bin_dir = Path("bin")
        self.client_exe = self.bin_dir / "client_windows_amd64.exe"
        self.config = Config()
        self.running = False
        self.process = None
        self.was_running = False
        
        # 检查客户端程序是否存在
        if not self.client_exe.exists():
            messagebox.showerror("错误", "找不到Kcptun客户端程序")
            root.destroy()
            return
        
        # 先加载配置
        self.current_config = self.config.load()
        
        # 创建界面元素
        self.create_notebook()
        self.create_basic_widgets()
        self.create_advanced_widgets()
        
        # 创建系统托盘
        self.setup_tray()
        
        # 加载配置到界面
        self.load_config()
        
        # 绑定窗口关闭事件
        self.root.protocol('WM_DELETE_WINDOW', self.hide_window)
        
        # 绑定程序退出事件
        atexit.register(self.cleanup_resources)
        
        # 定期检查状态
        self.check_status()
        
        # 修改双击检测相关变量
        self.last_click_time = 0
        self.double_click_interval = 200  # 减小双击间隔为200毫秒，使更容易触发双击
        
        # 初始化日志系统
        self.logger = KcptunLogger()
        
        # 添加更现代的按钮样式
        style = ttk.Style()
        # 未选中状态 - 灰色眼睛
        style.configure('Password.TButton', 
                       padding=0,            # 移除内边距
                       relief='flat',
                       background='#ffffff',
                       foreground='#666666', # 灰色文字
                       borderwidth=0,        # 移除框
                       width=3)              # 控制按钮宽度
        
        # 选中状态 - 蓝色眼睛
        style.configure('Password.Toggled.TButton',
                       padding=0,
                       relief='flat',
                       background='#ffffff',
                       foreground='#1a73e8', # 蓝色文字
                       borderwidth=0,
                       width=3)

        # 添加进程相关的属性初始化
        self.process = None
        self.monitor_thread = None
        self.running = False

    def setup_styles(self):
        """设置全局样式"""
        style = ttk.Style()
        
        # 使用系统默认主题
        if sys.platform.startswith('win'):
            style.theme_use('vista')
        
        # 设置全局主题色
        bg_color = '#ffffff'           # 使用纯白色背景
        text_color = '#000000'         # 使用纯黑色文字
        
        # 配置所有控件的基本样式
        for widget in ['TFrame', 'TLabelframe', 'TLabel', 'TEntry', 'TButton', 'TCheckbutton']:
            style.configure(widget, background=bg_color)
            style.configure(widget, foreground=text_color)

        # 配置标签框架标题
        style.configure('TLabelframe.Label', foreground=text_color)

        # 配置输入框
        style.configure('TEntry', fieldbackground=bg_color)

        # 配置按钮
        style.configure('TButton', padding=(10, 5))

        # 配置复选框
        style.configure('TCheckbutton', background=bg_color)

        # 配置Notebook
        style.configure('TNotebook', background=bg_color)
        style.configure('TNotebook.Tab', padding=(10, 5))

    def create_titlebar_menu(self):
        """创建标题栏菜单按钮"""
        if os.name == 'nt':
            try:
                import win32gui
                import win32con
                import win32api
                from ctypes import windll, byref, sizeof, c_int, WINFUNCTYPE, c_long
                
                # 获取窗口句柄
                hwnd = self.root.winfo_id()
                
                # 获取标题栏高度
                title_bar_height = windll.user32.GetSystemMetrics(win32con.SM_CYCAPTION)
                
                # 获取窗口大小
                rect = win32gui.GetWindowRect(hwnd)
                width = rect[2] - rect[0]
                
                # 创建按钮窗口
                button_x = width - 100  # 在最小化按钮左侧
                self.button_hwnd = win32gui.CreateWindow(
                    "BUTTON",  # 使用系统按钮类
                    "☰",
                    win32con.WS_CHILD | win32con.WS_VISIBLE | win32con.BS_PUSHBUTTON | win32con.BS_NOTIFY,
                    button_x,
                    0,
                    30,
                    title_bar_height - 2,
                    hwnd,
                    1000,  # 按钮ID
                    0,
                    None
                )
                
                # 使用系统字体
                SYSTEM_FONT = 13  # 系统字体的ID
                hfont = win32gui.GetStockObject(SYSTEM_FONT)
                win32gui.SendMessage(self.button_hwnd, win32con.WM_SETFONT, hfont, 1)
                
                # 保存原始窗口过程
                self.old_button_proc = win32gui.GetWindowLong(self.button_hwnd, win32con.GWL_WNDPROC)
                
                # 创建窗口过程回调类型
                WNDPROC = WINFUNCTYPE(c_long, c_int, c_int, c_int, c_int)
                
                def button_proc(hwnd, msg, wparam, lparam):
                    try:
                        if msg == win32con.WM_COMMAND:
                            if wparam == 1000:  # 按钮ID
                                if self.root and self.root.winfo_exists():
                                    self.root.after(0, self.show_menu)
                                return 0
                        return win32gui.CallWindowProc(self.old_button_proc, hwnd, msg, wparam, lparam)
                    except Exception:
                        return 0
                
                # 保存回调函数引用
                self._button_proc = WNDPROC(button_proc)
                
                # 设置新的窗口过程
                win32gui.SetWindowLong(
                    self.button_hwnd,
                    win32con.GWL_WNDPROC,
                    self._button_proc
                )
                
            except Exception as e:
                print(f"Error creating title bar button: {e}")
                import traceback
                traceback.print_exc()

    def show_menu(self):
        """显示下拉菜单"""
        menu = tk.Menu(self.root, tearoff=0)
        menu.add_command(label="设置", command=self.show_settings_dialog)
        
        # 获取按钮位置
        if hasattr(self, 'button_hwnd'):
            rect = win32gui.GetWindowRect(self.button_hwnd)
            x = rect[0]
            y = rect[3]  # 使用按钮底部的y坐标
        else:
            x = win32api.GetCursorPos()[0]
            y = win32api.GetCursorPos()[1]
        
        # 显示菜单
        try:
            menu.tk_popup(x, y)
        finally:
            menu.grab_release()

    def create_default_icon(self):
        """创建现代风格的图标"""
        from PIL import Image, ImageDraw, ImageFilter

        # 创建一个更大尺寸的图像以获得更好的质量
        size = 128  # 更大的初始尺寸
        img = Image.new('RGBA', (size, size), color=(0,0,0,0))
        draw = ImageDraw.Draw(img)
        
        # 使用更现代的配色方案
        primary_color = (52, 152, 219)     # 明亮的蓝色
        secondary_color = (46, 204, 113)   # 翠绿色
        bg_color = (41, 128, 185, 180)     # 半透明的深蓝色
        
        # 绘制主圆形背景
        padding = size // 8
        draw.ellipse([padding, padding, size-padding, size-padding], 
                     fill=bg_color)
        
        # 绘制动态效果线条
        line_width = size // 20
        for i in range(3):
            offset = i * (size//8)
            # 绘制动态曲线
            points = [
                (padding+offset, size//2+offset),
                (size//2, padding+offset),
                (size-padding-offset, size//2+offset)
            ]
            draw.line(points, fill=primary_color, width=line_width)
            
            # 绘制反向曲线
            points = [
                (padding+offset, size//2-offset),
                (size//2, size-padding-offset),
                (size-padding-offset, size//2-offset)
            ]
            draw.line(points, fill=secondary_color, width=line_width)
        
        # 添加发光效果
        glow = img.filter(ImageFilter.GaussianBlur(radius=2))
        img = Image.alpha_composite(img, glow)
        
        # 缩放到所需尺寸
        img = img.resize((32, 32), Image.Resampling.LANCZOS)
        
        # 确保目录存在并保存
        self.icon_path.parent.mkdir(parents=True, exist_ok=True)
        img.save(self.icon_path, 'PNG')

    def setup_tray(self):
        """设置系统托盘"""
        image = Image.open(self.icon_path)

        class CustomIcon(pystray._win32.Icon):
            def __init__(self, *args, **kwargs):
                self.gui = kwargs.pop('gui')
                super().__init__(*args, **kwargs)
                self.click_timer = None
                self.click_count = 0

            def _on_notify(self, wparam, lparam):
                """处理托盘图标的通知事件"""
                try:
                    # 处理左键点击
                    if lparam == 0x0201:  # WM_LBUTTONDOWN
                        current_time = int(time.time() * 1000)
                        time_diff = current_time - self.gui.last_click_time
                        
                        if time_diff < self.gui.double_click_interval:
                            # 双击 - 显示窗口
                            self.gui.root.after(0, self.gui.show_window)
                            self.gui.last_click_time = 0
                            self.click_count = 0
                        else:
                            # 单击 - 记录时间
                            self.gui.last_click_time = current_time
                        return True
                    elif lparam == 0x0203:  # WM_LBUTTONDBLCLK
                        # 直接处理双击消息
                        self.gui.root.after(0, self.gui.show_window)
                        self.gui.last_click_time = 0
                        return True
                        
                    return super()._on_notify(wparam, lparam)
                    
                except Exception as e:
                    print(f"Error in notify handler: {e}")
                    import traceback
                    traceback.print_exc()
                    return False

        def create_profile_menu():
            """创建配置子菜单"""
            profiles = list(self.current_config['profiles'].keys())
            current_profile = self.current_config.get('current_profile', 'default')
            
            def switch_profile(profile_name):
                def handler(icon, item):
                    self.profile_var.set(profile_name)
                    self.on_profile_selected(None)
                return handler
            
            return pystray.Menu(
                *[pystray.MenuItem(
                    f"{'✓ ' if name == current_profile else '  '}{name}",
                    switch_profile(name)
                ) for name in profiles]
            )

        # 创建菜单
        menu = pystray.Menu(
            pystray.MenuItem("显示主窗口", lambda icon, item: self.show_window()),
            pystray.Menu.SEPARATOR,  # 使用官方的分隔符
            pystray.MenuItem("配置", create_profile_menu()),  # 添加配置子菜单
            pystray.MenuItem(
                "启动服务", 
                lambda icon, item: self.start_from_tray(icon, item), 
                enabled=not self.running
            ),
            pystray.MenuItem(
                "停止服务", 
                lambda icon, item: self.stop_from_tray(icon, item),
                enabled=self.running
            ),
            pystray.Menu.SEPARATOR,  # 使用官方的分隔符
            pystray.MenuItem("退出", lambda icon, item: self.quit_application(icon, item))
        )
        
        # 创建托盘图标
        self.tray_icon = CustomIcon(
            "kcptun",
            image,
            "Kcptun客户端\n双击显示主窗口\n右键显示菜单",
            menu=menu,
            gui=self
        )
        
        # 在新线程中启动托盘图标
        threading.Thread(target=self.tray_icon.run, daemon=True).start()

    def start_from_tray(self, icon, item):
        """从托盘启动服务"""
        if self.running:
            messagebox.showinfo("提示", "服务已经在运行中")
            return
        self.start_kcptun()

    def stop_from_tray(self, icon, item):
        """从托盘停止服务"""
        if not self.running:
            messagebox.showinfo("提示", "服务未在运行")
            return
        self.stop_kcptun()

    def show_window(self):
        """显示窗口"""
        self.root.after(0, lambda: [
            self.root.deiconify(),
            self.root.lift(),
            self.root.focus_force(),
            self.root.attributes('-topmost', True),
            self.root.update(),
            self.root.attributes('-topmost', False),
        ])

    def hide_window(self):
        """隐藏窗口到系统托盘"""
        self.root.withdraw()  # 隐藏
        
        # 程序仍在后台运行
        if self.running:
            self.tray_icon.notify(
                "Kcptun客户端已最小化到系统托盘",
                "程序仍在后台运行"
            )

    def quit_application(self, icon=None, item=None):
        """完全退出应用程序"""
        try:
            # 停止kcptun进程
            if self.process:
                self.stop_kcptun()
            
            # 停止所有监控线程
            if hasattr(self, 'monitor_thread') and self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=2)
            
            # 关闭日志系统
            if hasattr(self, 'logger'):
                self.logger.close()
            
            # 移除托盘图标
            if hasattr(self, 'tray_icon'):
                self.tray_icon.stop()
            
            # 销毁主窗口
            if hasattr(self, 'root') and self.root:
                self.root.destroy()
            
            # 退出程序
            sys.exit(0)
        except Exception as e:
            print(f"Error during application shutdown: {e}")
            sys.exit(1)

    def create_basic_widgets(self):
        """创建基本设置页面"""
        # 创建主容器
        main_container = ttk.Frame(self.basic_frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建状态框架
        status_frame = ttk.Frame(main_container)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 左侧状态显示
        status_left = ttk.Frame(status_frame)
        status_left.pack(side=tk.LEFT)
        
        ttk.Label(status_left, text="当前运行状态:").pack(side=tk.LEFT, padx=(0, 5))
        self.status_label = ttk.Label(status_left, 
                                    text="未运行",
                                    foreground="#ea4335")  # Google Red
        self.status_label.pack(side=tk.LEFT)

        # 基本设置框架
        input_frame = ttk.LabelFrame(main_container, text="基本连接设置")
        input_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 配置grid列权重
        input_frame.grid_columnconfigure(1, weight=1)

        # KCP IP
        ttk.Label(input_frame, text="Kcp IP:").grid(row=0, column=0, sticky=tk.E, padx=5, pady=5)
        self.kcp_ip = ttk.Entry(input_frame)
        self.kcp_ip.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)

        # KCP Port
        ttk.Label(input_frame, text="Kcp Port:").grid(row=1, column=0, sticky=tk.E, padx=5, pady=5)
        self.kcp_port = ttk.Entry(input_frame)
        self.kcp_port.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)

        # KCP Password
        ttk.Label(input_frame, text="Kcp Password:").grid(row=2, column=0, sticky=tk.E, padx=5, pady=5)
        password_frame = ttk.Frame(input_frame)
        password_frame.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        password_frame.grid_columnconfigure(0, weight=1)
        
        self.kcp_password = ttk.Entry(password_frame, show="*")
        self.kcp_password.grid(row=0, column=0, sticky=tk.EW)
        
        # 添加显示/隐藏密码的按钮
        self.show_password = tk.BooleanVar(value=False)
        self.toggle_password_btn = ttk.Button(
            password_frame, 
            text="👀",
            command=self.toggle_password_visibility,
            cursor="hand2",
            width=3
        )
        self.toggle_password_btn.grid(row=0, column=1, padx=(5, 0))

        # Local Port
        ttk.Label(input_frame, text="Local Port:").grid(row=3, column=0, sticky=tk.E, padx=5, pady=5)
        self.local_port = ttk.Entry(input_frame)
        self.local_port.grid(row=3, column=1, sticky=tk.EW, padx=5, pady=5)

        # 配置管理框架
        profile_frame = ttk.LabelFrame(main_container, text="配置管理")
        profile_frame.pack(fill=tk.X, pady=10)

        # 配置选择下拉框
        profile_select_frame = ttk.Frame(profile_frame)
        profile_select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(profile_select_frame, text="当前配置:").pack(side=tk.LEFT)
        self.profile_var = tk.StringVar()
        self.profile_combo = ttk.Combobox(profile_select_frame, textvariable=self.profile_var)
        self.profile_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.profile_combo.bind('<<ComboboxSelected>>', self.on_profile_selected)

        # 配置管理按钮
        button_frame = ttk.Frame(profile_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="新建配置", command=self.new_profile).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存为新配置", command=self.save_as_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除配置", command=self.delete_profile).pack(side=tk.LEFT, padx=5)

        # 操作按钮框架
        action_frame = ttk.Frame(main_container)
        action_frame.pack(fill=tk.X, pady=10)

        # 左侧操作按钮
        left_button_frame = ttk.Frame(action_frame)
        left_button_frame.pack(side=tk.LEFT)

        self.start_button = ttk.Button(
            left_button_frame,
            text="启动",
            command=self.start_kcptun,
            width=10
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))

        self.stop_button = ttk.Button(
            left_button_frame,
            text="停止",
            command=self.stop_kcptun,
            width=10
        )
        self.stop_button.pack(side=tk.LEFT)

        # 右侧配置按钮
        right_button_frame = ttk.Frame(action_frame)
        right_button_frame.pack(side=tk.RIGHT)

        self.save_button = ttk.Button(
            right_button_frame,
            text="保存配置",
            command=self.save_config_manual,
            width=10
        )
        self.save_button.pack(side=tk.RIGHT)

    def create_advanced_widgets(self):
        """创建高级设置页面"""
        # 创建主容器
        main_container = ttk.Frame(self.advanced_frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建高级设置框架
        advanced_frame = ttk.LabelFrame(main_container, text="高级参数设置")
        advanced_frame.pack(fill=tk.BOTH, expand=True)

        # 配置grid列
        advanced_frame.grid_columnconfigure(1, weight=1)

        # 创建所有控件
        row = 0

        # MTU
        ttk.Label(advanced_frame, text="MTU:").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.mtu = ttk.Entry(advanced_frame)
        self.mtu.insert(0, "1400")
        self.mtu.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 发送窗口
        row += 1
        ttk.Label(advanced_frame, text="发送窗口(sndwnd):").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.sndwnd = ttk.Entry(advanced_frame)
        self.sndwnd.insert(0, "1024")
        self.sndwnd.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 接收窗口
        row += 1
        ttk.Label(advanced_frame, text="接收窗口(rcvwnd):").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.rcvwnd = ttk.Entry(advanced_frame)
        self.rcvwnd.insert(0, "1024")
        self.rcvwnd.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 传输模式
        row += 1
        ttk.Label(advanced_frame, text="传输模式:").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.mode = ttk.Combobox(advanced_frame, values=["normal", "fast", "fast2", "fast3"])
        self.mode.set("normal")
        self.mode.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 加密方式
        row += 1
        ttk.Label(advanced_frame, text="加密方式:").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.crypt = ttk.Combobox(advanced_frame, values=["aes", "aes-128", "aes-192", "salsa20", "blowfish", "twofish", "cast5", "3des", "tea", "xtea", "xor", "none"])
        self.crypt.set("aes-128")
        self.crypt.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 数据片段
        row += 1
        ttk.Label(advanced_frame, text="数据片段(datashard):").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.datashard = ttk.Entry(advanced_frame)
        self.datashard.insert(0, "10")
        self.datashard.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 校验片段
        row += 1
        ttk.Label(advanced_frame, text="校验片段(parityshard):").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.parityshard = ttk.Entry(advanced_frame)
        self.parityshard.insert(0, "3")
        self.parityshard.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # DSCP
        row += 1
        ttk.Label(advanced_frame, text="DSCP:").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.dscp = ttk.Entry(advanced_frame)
        self.dscp.insert(0, "46")
        self.dscp.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 心跳包时间
        row += 1
        ttk.Label(advanced_frame, text="心跳包时间(keepalive):").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.keepalive = ttk.Entry(advanced_frame)
        self.keepalive.insert(0, "10")
        self.keepalive.grid(row=row, column=1, sticky=tk.EW, padx=5, pady=5)

        # 复选框区域
        row += 1
        checkbox_frame = ttk.Frame(advanced_frame)
        checkbox_frame.grid(row=row, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)

        # 禁用数据压缩选项
        self.nocomp_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            checkbox_frame,
            text="禁用数据压缩",
            variable=self.nocomp_var
        ).pack(side=tk.LEFT)

        # 日志设置选项
        self.enable_logging_var = tk.BooleanVar(value=self.current_config.get('settings', {}).get('enable_logging', False))
        ttk.Checkbutton(
            checkbox_frame,
            text="启用日志输出",
            variable=self.enable_logging_var,
            command=self.save_logging_settings
        ).pack(side=tk.LEFT, padx=(20, 0))

        # 按钮区域
        row += 1
        button_frame = ttk.Frame(advanced_frame)
        button_frame.grid(row=row, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=10)

        # 重置按钮
        ttk.Button(
            button_frame,
            text="重置为默认值",
            command=self.reset_advanced_settings
        ).pack(side=tk.LEFT)

        # 保存按钮
        ttk.Button(
            button_frame,
            text="保存配置",
            command=self.save_config_manual
        ).pack(side=tk.RIGHT)

    def save_logging_settings(self):
        """保存日志设置"""
        if 'settings' not in self.current_config:
            self.current_config['settings'] = {}
        self.current_config['settings']['enable_logging'] = self.enable_logging_var.get()
        self.config.save(self.current_config)

    def load_config(self):
        """载配置"""
        config_data = self.config.load()
        self.current_config = config_data
        
        # 更新配置列表
        profiles = list(config_data.get('profiles', {}).keys())
        self.profile_combo['values'] = profiles
        
        # 置当前配置
        current_profile = config_data.get('current_profile', 'default')
        self.profile_var.set(current_profile)
        
        # 加载当前置内容
        profile_data = config_data.get('profiles', {}).get(current_profile, {})
        if profile_data:  # 只当配置数据存在时才加载
            self._load_profile_data(profile_data)

    def _load_profile_data(self, profile_data):
        """加载指定配置数据到界面"""
        # 只有当字段存在时才更新，避免置
        if 'kcp_ip' in profile_data:
            self.kcp_ip.delete(0, tk.END)
            self.kcp_ip.insert(0, profile_data.get('kcp_ip', ''))
        
        if 'kcp_port' in profile_data:
            self.kcp_port.delete(0, tk.END)
            self.kcp_port.insert(0, profile_data.get('kcp_port', ''))
        
        if 'kcp_password' in profile_data:
            self.kcp_password.delete(0, tk.END)
            self.kcp_password.insert(0, profile_data.get('kcp_password', ''))
        
        if 'local_port' in profile_data:
            self.local_port.delete(0, tk.END)
            self.local_port.insert(0, profile_data.get('local_port', ''))
        
        # 高级设置
        if 'advanced' in profile_data:
            adv = profile_data['advanced']
            
            if 'mtu' in adv:
                self.mtu.delete(0, tk.END)
                self.mtu.insert(0, adv.get('mtu', '1400'))
            
            if 'sndwnd' in adv:
                self.sndwnd.delete(0, tk.END)
                self.sndwnd.insert(0, adv.get('sndwnd', '1024'))
            
            if 'rcvwnd' in adv:
                self.rcvwnd.delete(0, tk.END)
                self.rcvwnd.insert(0, adv.get('rcvwnd', '1024'))
            
            if 'mode' in adv:
                self.mode.set(adv.get('mode', 'normal'))
            
            if 'crypt' in adv:
                self.crypt.set(adv.get('crypt', 'aes-128'))
            
            if 'datashard' in adv:
                self.datashard.delete(0, tk.END)
                self.datashard.insert(0, adv.get('datashard', '10'))
            
            if 'parityshard' in adv:
                self.parityshard.delete(0, tk.END)
                self.parityshard.insert(0, adv.get('parityshard', '3'))
            
            if 'dscp' in adv:
                self.dscp.delete(0, tk.END)
                self.dscp.insert(0, adv.get('dscp', '46'))
            
            if 'keepalive' in adv:
                self.keepalive.delete(0, tk.END)
                self.keepalive.insert(0, adv.get('keepalive', '10'))
            
            if 'nocomp' in adv:
                self.nocomp_var.set(adv.get('nocomp', True))

    def _get_current_config(self):
        """获取前界面的置据"""
        return {
            'kcp_ip': self.kcp_ip.get(),
            'kcp_port': self.kcp_port.get(),
            'kcp_password': self.kcp_password.get(),
            'local_port': self.local_port.get(),
            'advanced': {
                'mtu': self.mtu.get(),
                'sndwnd': self.sndwnd.get(),
                'rcvwnd': self.rcvwnd.get(),
                'mode': self.mode.get(),
                'crypt': self.crypt.get(),
                'datashard': self.datashard.get(),
                'parityshard': self.parityshard.get(),
                'dscp': self.dscp.get(),
                'keepalive': self.keepalive.get(),
                'nocomp': self.nocomp_var.get()
            }
        }

    def save_config(self):
        """保存当前配置"""
        current_profile = self.profile_var.get()
        self.current_config['current_profile'] = current_profile
        self.current_config['profiles'][current_profile] = self._get_current_config()
        self.config.save(self.current_config)

    def new_profile(self):
        """创建新配置"""
        name = simpledialog.askstring("新建配置", "请输入配置名称:")
        if name:
            if name in self.current_config['profiles']:
                messagebox.showerror("错误", "配置名称已存在")
                return
            
            # 创建认配置
            default_config = {
                'kcp_ip': '',
                'kcp_port': '',
                'kcp_password': '',
                'local_port': '',
                'advanced': {
                    'mtu': '1400',
                    'sndwnd': '1024',
                    'rcvwnd': '1024',
                    'mode': 'normal',
                    'crypt': 'aes-128',
                    'datashard': '10',
                    'parityshard': '3',
                    'dscp': '46',
                    'keepalive': '10',
                    'nocomp': True
                }
            }
            
            # 保存新配置
            self.current_config['profiles'][name] = default_config
            self.current_config['current_profile'] = name
            self.config.save(self.current_config)
            
            # 更新配置列表
            self.profile_combo['values'] = list(self.current_config['profiles'].keys())
            self.profile_var.set(name)
            
            # 加载认置到界面
            self._load_profile_data(default_config)

    def save_as_profile(self):
        """保存为新配置"""
        name = simpledialog.askstring("保存新配置", "请输入配置名称:")
        if name:
            if name in self.current_config['profiles']:
                if not messagebox.askyesno("确认", "配置已存在是否覆盖？"):
                    return
            
            self.current_config['profiles'][name] = self._get_current_config()
            self.current_config['current_profile'] = name
            self.config.save(self.current_config)
            
            # 更新配置列表
            self.profile_combo['values'] = list(self.current_config['profiles'].keys())
            self.profile_var.set(name)

    def delete_profile(self):
        """删除当前配置"""
        current_profile = self.profile_var.get()
        if current_profile == 'default':
            messagebox.showerror("错误", "默认配置不能删除")
            return
        
        if messagebox.askyesno("确认", f"是否删除配置 {current_profile}？"):
            del self.current_config['profiles'][current_profile]
            self.current_config['current_profile'] = 'default'
            self.config.save(self.current_config)
            
            # 新配置列表
            self.profile_combo['values'] = list(self.current_config['profiles'].keys())
            self.profile_var.set('default')
            self._load_profile_data(self.current_config['profiles']['default'])

    def on_profile_selected(self, event):
        """选择配置时触发"""
        profile_name = self.profile_var.get()
        
        # 如果选择的是当前配置不需要切换
        if profile_name == self.current_config['current_profile']:
            return
        
        # 保存当前配置
        current_profile = self.current_config['current_profile']
        self.current_config['profiles'][current_profile] = self._get_current_config()
        
        # 切换到新配置
        self.current_config['current_profile'] = profile_name
        self.config.save(self.current_config)
        
        # 加载新配置
        if profile_name in self.current_config['profiles']:
            self._load_profile_data(self.current_config['profiles'][profile_name])
            
            # 如果之前在运行状态，停止当前进程并启动新配置
            if self.running:
                try:
                    self.stop_kcptun()
                    time.sleep(1)  # 等待进程完全停止
                    self.start_kcptun()
                    # 显示通知
                    if hasattr(self, 'tray_icon'):
                        self.tray_icon.notify(
                            "配置切换",
                            f"已切换到配置: {profile_name} 并重新启动服务"
                        )
                except Exception as e:
                    messagebox.showerror("错误", f"启动新配置失败: {str(e)}")
            else:
                # 仅显示切换通知
                if hasattr(self, 'tray_icon'):
                    self.tray_icon.notify(
                        "配置切换",
                        f"已切换到配置: {profile_name}"
                    )

    def start_kcptun(self):
        if not self.validate_inputs():
            return

        self.save_config()
        
        cmd = [
            str(self.client_exe),
            "-r", f"{self.kcp_ip.get()}:{self.kcp_port.get()}",
            "-l", f":{self.local_port.get()}",
            "-key", self.kcp_password.get(),
            "-mode", self.mode.get(),
            "-mtu", self.mtu.get(),
            "-sndwnd", self.sndwnd.get(),
            "-rcvwnd", self.rcvwnd.get(),
            "-crypt", self.crypt.get(),
            "-datashard", self.datashard.get(),
            "-parityshard", self.parityshard.get(),
            "-dscp", self.dscp.get(),
            "-keepalive", self.keepalive.get()
        ]

        if self.nocomp_var.get():
            cmd.append("-nocomp")

        try:
            # 记录启动命令
            self.logger.info(f"Starting kcptun with command: {' '.join(cmd)}")
            
            # 确保之前的进程已经完全停止
            if self.process:
                self.stop_kcptun()
                time.sleep(1)
            
            # 使用最单的方式启动进程
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,  # 忽略标准输出
                stderr=subprocess.DEVNULL,  # 忽略错误输出
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                cwd=str(self.bin_dir),  # 设置工作目录
                env=os.environ.copy()
            )
            
            # 等待短暂时间确认进程启动
            time.sleep(0.5)
            if self.process.poll() is not None:
                raise Exception("进程启动失败")
            
            self.running = True
            self.was_running = True
            self.update_status()
            self.logger.info("Kcptun started successfully")
            
            # 启动简单的状态检查线程
            self.monitor_thread = threading.Thread(target=self._check_process_status, daemon=True)
            self.monitor_thread.start()
            
        except Exception as e:
            self.logger.error(f"Failed to start kcptun: {str(e)}")
            messagebox.showerror("错误", f"启动失败: {str(e)}")
            if self.process:
                try:
                    self.process.terminate()
                except:
                    pass
                self.process = None
            self.running = False
            self.update_status()

    def _check_process_status(self):
        """简单的进程状态检查"""
        try:
            while self.process and self.running:
                try:
                    if self.process.poll() is not None:
                        self.running = False
                        self.root.after(0, self.update_status)
                        self.logger.warning("Kcptun process terminated unexpectedly")
                        break
                    time.sleep(1)
                except (AttributeError, OSError) as e:
                    self.logger.error(f"Process monitoring error: {e}")
                    break
        except Exception as e:
            self.logger.error(f"Monitor error: {str(e)}")
        finally:
            if self.running:
                self.running = False
                try:
                    self.root.after(0, self.update_status)
                except Exception:
                    pass

    def stop_kcptun(self):
        """停止kcptun进程"""
        try:
            if self.process:
                self.logger.info("Stopping kcptun...")
                self.running = False
                
                # 先停止监控线程
                if hasattr(self, 'monitor_thread') and self.monitor_thread and self.monitor_thread.is_alive():
                    self.monitor_thread.join(timeout=2)  # 等待监控线程结束
                
                # Windows下使用taskkill强制结束进程树
                if os.name == 'nt':
                    try:
                        subprocess.run(['taskkill', '/F', '/T', '/PID', str(self.process.pid)], 
                                     capture_output=True,
                                     creationflags=subprocess.CREATE_NO_WINDOW,
                                     timeout=5)  # 添加超时
                    except subprocess.TimeoutExpired:
                        self.logger.error("Taskkill timeout, trying to kill process directly")
                        self.process.kill()
                else:
                    self.process.terminate()
                    try:
                        self.process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        self.process.kill()
                
                # 清理进程资源
                try:
                    if self.process and self.process.stdout:
                        self.process.stdout.close()
                except Exception as e:
                    self.logger.debug(f"Error closing stdout: {e}")
                
                try:
                    if self.process and self.process.stderr:
                        self.process.stderr.close()
                except Exception as e:
                    self.logger.debug(f"Error closing stderr: {e}")
                
                self.logger.info("Kcptun stopped")
        except Exception as e:
            self.logger.error(f"Error stopping kcptun: {str(e)}")
        finally:
            self.process = None
            self.running = False
            self.update_status()

    def validate_inputs(self):
        if not all([self.kcp_ip.get(), self.kcp_port.get(), 
                   self.kcp_password.get(), self.local_port.get()]):
            messagebox.showerror("错误", "所有字段都必须填写")
            return False
            
        if not is_valid_ip(self.kcp_ip.get()):
            messagebox.showerror("错误", "无效的IP地址")
            return False
            
        if not validate_port(self.kcp_port.get()) or not validate_port(self.local_port.get()):
            messagebox.showerror("错误", "端口必须1-65535之间的数字")
            return False
            
        return True

    def monitor_process(self):
        """监控kcptun进程状态"""
        try:
            if self.process:
                while True:
                    if self.process.poll() is not None:
                        # 进程已结束
                        self.running = False
                        self.root.after(0, self.update_status)
                        self.logger.warning("Kcptun process terminated unexpectedly")
                        break
                    time.sleep(1)
        except Exception as e:
            self.logger.error(f"Error monitoring process: {str(e)}")
            self.running = False
            self.root.after(0, self.update_status)

    def update_status(self):
        """更新状态显示"""
        if self.running:
            self.status_label.config(text="运行中", foreground="#34a853")  # Google Green
            self.start_button.state(['disabled'])
            self.stop_button.state(['!disabled'])
        else:
            self.status_label.config(text="未运行", foreground="#ea4335")  # Google Red
            self.start_button.state(['!disabled'])
            self.stop_button.state(['disabled'])
        self.update_tray_menu()

    def check_status(self):
        if self.process and self.process.poll() is not None:
            self.running = False
            self.process = None
        self.update_status() 

    def save_config_manual(self):
        """手动保存配置"""
        try:
            self.save_config()
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def clear_inputs(self):
        """清空所有输入框"""
        # 基本设置
        self.kcp_ip.delete(0, tk.END)
        self.kcp_port.delete(0, tk.END)
        self.kcp_password.delete(0, tk.END)
        self.local_port.delete(0, tk.END)
        
        # 高级设置
        self.mtu.delete(0, tk.END)
        self.sndwnd.delete(0, tk.END)
        self.rcvwnd.delete(0, tk.END)
        self.mode.set("normal")
        self.crypt.set("aes-128")
        self.datashard.delete(0, tk.END)
        self.parityshard.delete(0, tk.END)
        self.dscp.delete(0, tk.END)
        self.keepalive.delete(0, tk.END)
        self.nocomp_var.set(True) 

    def reset_advanced_settings(self):
        """重置高级设置为默认值"""
        if messagebox.askyesno("确认", "是否要将所有高级设置重置认值？"):
            self.mtu.delete(0, tk.END)
            self.mtu.insert(0, "1400")
            
            self.sndwnd.delete(0, tk.END)
            self.sndwnd.insert(0, "1024")
            
            self.rcvwnd.delete(0, tk.END)
            self.rcvwnd.insert(0, "1024")
            
            self.mode.set("normal")
            self.crypt.set("aes-128")
            
            self.datashard.delete(0, tk.END)
            self.datashard.insert(0, "10")
            
            self.parityshard.delete(0, tk.END)
            self.parityshard.insert(0, "3")
            
            self.dscp.delete(0, tk.END)
            self.dscp.insert(0, "46")
            
            self.keepalive.delete(0, tk.END)
            self.keepalive.insert(0, "10")
            
            self.nocomp_var.set(True)
            
            messagebox.showinfo("成功", "高级设置已重置为默认值") 

    def create_notebook(self):
        """创建主notebook用于分页"""
        # 创建主notebook用于分页
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建基本设置和高级设置页面
        self.basic_frame = ttk.Frame(self.notebook)
        self.advanced_frame = ttk.Frame(self.notebook)
        
        # 配置frame的网格布局
        self.basic_frame.grid_columnconfigure(0, weight=1)
        self.advanced_frame.grid_columnconfigure(0, weight=1)
        
        # 添加页面到notebook
        self.notebook.add(self.basic_frame, text="基本设置")
        self.notebook.add(self.advanced_frame, text="高级设置")
        
        # 确保notebook占满整个窗口
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

    def update_tray_menu(self):
        """更新托盘菜单状态"""
        try:
            def on_clicked(icon, item):
                self.show_window()
            
            def create_profile_menu():
                """创建配置子菜单"""
                profiles = list(self.current_config['profiles'].keys())
                current_profile = self.current_config.get('current_profile', 'default')
                
                def switch_profile(profile_name):
                    def handler(icon, item):
                        self.profile_var.set(profile_name)
                        self.on_profile_selected(None)
                    return handler
                
                return pystray.Menu(
                    *[pystray.MenuItem(
                        f"{'✓ ' if name == current_profile else '  '}{name}",
                        switch_profile(name)
                    ) for name in profiles]
                )
            
            # 创建新菜单
            menu = pystray.Menu(
                pystray.MenuItem("显示主窗口", on_clicked),
                pystray.Menu.SEPARATOR,  # 用官方的分隔符
                pystray.MenuItem("配置", create_profile_menu()),  # 添加配置子菜单
                pystray.MenuItem(
                    "启动服务", 
                    lambda icon, item: self.start_from_tray(icon, item), 
                    enabled=not self.running
                ),
                pystray.MenuItem(
                    "停止服务", 
                    lambda icon, item: self.stop_from_tray(icon, item),
                    enabled=self.running
                ),
                pystray.Menu.SEPARATOR,  # 使用官方的分隔符
                pystray.MenuItem("退出", lambda icon, item: self.quit_application(icon, item))
            )
            
            # 更新托盘图标菜单
            if hasattr(self, 'tray_icon'):
                self.tray_icon.menu = menu
                
        except Exception as e:
            print(f"Error updating tray menu: {e}")

    def toggle_password_visibility(self):
        """切换密码显示/隐藏状态"""
        current_show = self.kcp_password.cget('show')
        if current_show == '*':
            self.kcp_password.configure(show='')
            self.toggle_password_btn.configure(
                style='Password.Toggled.TButton',
                text="👀"  # 使用单个眼睛
            )
            self.show_password.set(True)
        else:
            self.kcp_password.configure(show='*')
            self.toggle_password_btn.configure(
                style='Password.TButton',
                text="👀"  # 使用单个眼睛
            )
            self.show_password.set(False)

    def cleanup_resources(self):
        """清理所有源"""
        try:
            if self.process:
                self.stop_kcptun()
            if hasattr(self, 'logger'):
                self.logger.close()
            if hasattr(self, 'button_hwnd') and self.button_hwnd:
                try:
                    import win32gui
                    import win32con
                    # 恢复原始窗口过程
                    if hasattr(self, 'old_button_proc') and self.old_button_proc:
                        try:
                            win32gui.SetWindowLong(
                                self.root.winfo_id(),
                                win32con.GWL_WNDPROC,
                                self.old_button_proc
                            )
                        except:
                            pass
                    try:
                        win32gui.DestroyWindow(self.button_hwnd)
                    except:
                        pass
                    self.button_hwnd = None
                except Exception as e:
                    print(f"Error destroying button window: {e}")
        except Exception as e:
            print(f"Error during cleanup: {e}")

    def show_settings_dialog(self):
        """显示设置对话框"""
        settings_dialog = tk.Toplevel(self.root)
        settings_dialog.title("设置")
        settings_dialog.geometry("300x150")
        settings_dialog.resizable(False, False)
        
        # 设置模态
        settings_dialog.transient(self.root)
        settings_dialog.grab_set()
        
        # 居中显示
        settings_dialog.update_idletasks()
        width = settings_dialog.winfo_width()
        height = settings_dialog.winfo_height()
        x = (settings_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (settings_dialog.winfo_screenheight() // 2) - (height // 2)
        settings_dialog.geometry(f'{width}x{height}+{x}+{y}')
        
        # 创建设置选项
        main_frame = ttk.Frame(settings_dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 开机启动选项
        autostart_var = tk.BooleanVar(value=is_autostart_enabled())
        autostart_check = ttk.Checkbutton(
            main_frame,
            text="开机启动",
            variable=autostart_var,
            style='Main.TCheckbutton'
        )
        autostart_check.pack(anchor=tk.W, pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))
        
        def save_settings():
            # 保存开机启动设置
            set_autostart(autostart_var.get())
            settings_dialog.destroy()
            messagebox.showinfo("提示", "设置已保存")
        
        # 保存按钮
        save_button = ttk.Button(
            button_frame,
            text="保存",
            style='Action.TButton',
            command=save_settings
        )
        save_button.pack(side=tk.RIGHT, padx=5)
        
        # 取消按钮
        cancel_button = ttk.Button(
            button_frame,
            text="取消",
            style='Action.TButton',
            command=settings_dialog.destroy
        )
        cancel_button.pack(side=tk.RIGHT, padx=5)

    def on_window_configure(self, event):
        """窗口大小改变时重新定位按钮"""
        if event.widget == self.root and hasattr(self, 'button_hwnd'):
            try:
                import win32gui
                import win32con
                
                # 获取窗口大小
                hwnd = self.root.winfo_id()
                rect = win32gui.GetWindowRect(hwnd)
                width = rect[2] - rect[0]
                
                # 移动按钮
                win32gui.SetWindowPos(
                    self.button_hwnd,
                    0,
                    width - 100,  # x position
                    0,           # y position
                    0, 0,        # width, height (0表示保持不变)
                    win32con.SWP_NOSIZE | win32con.SWP_NOZORDER
                )
            except Exception as e:
                print(f"Error repositioning button: {e}")