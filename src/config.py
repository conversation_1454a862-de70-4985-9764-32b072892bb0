import json
import os
from pathlib import Path

class Config:
    def __init__(self):
        self.config_dir = Path("resources")
        self.config_file = self.config_dir / "config.json"
        self._ensure_config_dir()

    def _ensure_config_dir(self):
        """确保配置目录存在"""
        self.config_dir.mkdir(parents=True, exist_ok=True)

    def load(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 确保配置文件包含基本字段
                    if 'profiles' not in data:
                        data = {
                            'current_profile': 'default',
                            'profiles': {
                                'default': data
                            }
                        }
                    # 添加日志控制选项，默认关闭
                    if 'settings' not in data:
                        data['settings'] = {
                            'enable_logging': False
                        }
                    return data
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
        return {
            'current_profile': 'default',
            'profiles': {
                'default': {}
            },
            'settings': {
                'enable_logging': False
            }
        }

    def save(self, config_data):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}") 