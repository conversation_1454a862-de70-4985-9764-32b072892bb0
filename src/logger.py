import logging
import logging.handlers
import os
from pathlib import Path
import sys

class KcptunLogger:
    def __init__(self):
        self.logger = logging.getLogger('kcptun')
        self.logger.setLevel(logging.DEBUG)
        
        # 创建日志目录
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件处理器 - 按天滚动
        file_handler = logging.handlers.TimedRotatingFileHandler(
            self.log_dir / "kcptun.log",
            when='midnight',
            interval=1,
            backupCount=7,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器 - 使用基本处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # 进程监控器
        self.process_monitor = None
    
    def start_process_monitor(self, process):
        """启进程输出监控"""
        if self.process_monitor and self.process_monitor.is_alive():
            self.process_monitor.join()
            
        def monitor():
            while process and process.poll() is None:
                if process.stdout:
                    try:
                        line = process.stdout.readline()
                        if line:
                            self.logger.info(f"[kcptun] {line.decode('utf-8', errors='replace').strip()}")
                    except:
                        pass
                if process.stderr:
                    try:
                        line = process.stderr.readline()
                        if line:
                            self.logger.error(f"[kcptun] {line.decode('utf-8', errors='replace').strip()}")
                    except:
                        pass
            
            if process and process.returncode:
                self.logger.error(f"[kcptun] Process exited with code {process.returncode}")
        
        import threading
        self.process_monitor = threading.Thread(target=monitor, daemon=True)
        self.process_monitor.start()
    
    def stop_process_monitor(self):
        """停止进程监控"""
        if self.process_monitor and self.process_monitor.is_alive():
            self.process_monitor.join()
            self.process_monitor = None
    
    def debug(self, msg):
        self.logger.debug(msg)
    
    def info(self, msg):
        self.logger.info(msg)
    
    def warning(self, msg):
        self.logger.warning(msg)
    
    def error(self, msg):
        self.logger.error(msg)
    
    def critical(self, msg):
        self.logger.critical(msg)
    
    def close(self):
        """关闭日志处理器"""
        try:
            for handler in self.logger.handlers[:]:
                handler.close()
                self.logger.removeHandler(handler)
        except Exception as e:
            print(f"Error closing logger: {e}") 