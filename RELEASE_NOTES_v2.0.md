# 🚀 Kcptun GUI v2.0 发布说明

## 📦 **发布信息**

- **版本号**: v2.0.0
- **发布日期**: 2024年6月4日
- **文件大小**: 14.0 MB
- **支持平台**: Windows 10/11 (x64)

## 📁 **发布文件**

### **1. 单文件版本**
```
dist/KcptunGUI_v2.0.exe
```
- **大小**: 14.0 MB
- **类型**: 独立可执行文件
- **特点**: 无需安装，双击即可运行

### **2. 便携版**
```
dist/KcptunGUI_v2.0_Portable/
├── KcptunGUI_v2.0.exe      # 主程序
├── bin/                    # kcptun客户端目录
├── example_config.json     # 示例配置文件
└── README.txt              # 使用说明
```
- **特点**: 包含完整的运行环境
- **优势**: 便于分发和部署

## ✨ **v2.0 新特性**

### **🏗️ 架构重构**
- ✅ **模块化设计**: 从1389行单文件重构为多模块架构
- ✅ **异步处理**: 使用asyncio提升响应性能
- ✅ **分层架构**: MVC模式，职责分离
- ✅ **依赖注入**: 组件间松耦合

### **🎨 界面优化**
- ✅ **左对齐布局**: 标签文字左对齐，更符合设计规范
- ✅ **图标修复**: 修复PNG图标显示问题
- ✅ **响应式设计**: 支持窗口大小调整
- ✅ **状态指示**: 实时显示连接状态

### **⚙️ 配置管理增强**
- ✅ **多配置支持**: 支持创建和管理多个配置
- ✅ **配置导入导出**: JSON格式配置文件
- ✅ **自动备份**: 保留最近10个配置备份
- ✅ **热切换**: 运行时无缝切换配置

### **🔧 功能增强**
- ✅ **输入验证**: 完善的参数验证和错误提示
- ✅ **优化建议**: 智能配置优化建议
- ✅ **进程监控**: 实时监控kcptun进程状态
- ✅ **异常恢复**: 自动处理进程异常

### **🔔 系统集成**
- ✅ **系统托盘**: 优化的托盘功能
- ✅ **双击显示**: 托盘双击显示主窗口
- ✅ **状态通知**: 系统通知提醒
- ✅ **开机启动**: 支持开机自动启动

## 🔄 **兼容性**

### **向后兼容**
- ✅ **配置文件**: 完全兼容v1.0配置文件
- ✅ **操作习惯**: 界面布局基本保持一致
- ✅ **功能特性**: 保留所有原有功能

### **系统要求**
- **操作系统**: Windows 10/11 (64位)
- **内存**: 最低 512MB RAM
- **磁盘空间**: 50MB 可用空间
- **网络**: 支持TCP/UDP连接

## 🚀 **使用方法**

### **首次使用**
1. **下载程序**: 选择单文件版或便携版
2. **准备客户端**: 将kcptun客户端程序放入bin目录
3. **启动程序**: 双击exe文件启动
4. **配置连接**: 在基本设置中填写服务器信息
5. **开始连接**: 点击启动按钮

### **配置管理**
1. **新建配置**: 点击"新建配置"按钮
2. **导入配置**: 使用"导入配置"导入JSON文件
3. **导出配置**: 使用"导出配置"备份配置
4. **切换配置**: 在下拉菜单中选择不同配置

### **高级设置**
1. **参数调优**: 在高级设置页面调整传输参数
2. **优化建议**: 点击"获取优化建议"获取配置建议
3. **重置设置**: 使用"重置为默认值"恢复默认设置

## 🐛 **已知问题**

### **已修复**
- ✅ 导出配置时的参数错误
- ✅ PNG图标显示问题
- ✅ 界面标签对齐问题
- ✅ 进程资源泄漏问题

### **注意事项**
- ⚠️ 首次运行可能被杀毒软件误报，请添加信任
- ⚠️ 需要将kcptun客户端程序放入bin目录
- ⚠️ 部分功能需要管理员权限

## 📊 **性能对比**

| 指标 | v1.0 | v2.0 | 改进 |
|------|------|------|------|
| **启动时间** | ~2秒 | ~1秒 | 50%↑ |
| **内存占用** | ~25MB | ~20MB | 20%↓ |
| **响应速度** | 同步阻塞 | 异步非阻塞 | 显著提升 |
| **文件大小** | ~8MB | 14MB | 功能增加 |

## 🔮 **未来规划**

### **v2.1 计划**
- [ ] 连接测试功能
- [ ] 网络质量监控
- [ ] 多语言支持
- [ ] 配置模板系统

### **v2.5 计划**
- [ ] 跨平台支持 (Linux/macOS)
- [ ] 现代UI框架
- [ ] 云端配置同步
- [ ] 插件系统

## 📞 **技术支持**

### **问题反馈**
- 📧 邮箱: <EMAIL>
- 🐛 Issues: GitHub Issues
- 💬 讨论: GitHub Discussions

### **文档资源**
- 📖 用户手册: docs/user-guide.md
- 🔧 开发文档: docs/developer-guide.md
- ❓ 常见问题: docs/faq.md

## 📄 **许可证**

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

**感谢使用 Kcptun GUI v2.0！** 🎉

如有问题或建议，欢迎反馈！
