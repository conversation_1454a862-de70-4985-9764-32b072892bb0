<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>爱你哟</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd0 100%);
        }
        
        .message-container {
            max-width: 800px;
            margin: 20px;
            padding: 30px;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            animation: fadeIn 1.5s ease-in-out;
        }
        
        .heart-message {
            font-size: 1.2em;
            line-height: 1.8;
            color: #e91e63;
            text-align: justify;
            margin-bottom: 20px;
            padding: 20px;
            border: 2px solid #fce4ec;
            border-radius: 10px;
            background-color: #fff;
        }
        
        .floating-hearts {
            position: absolute;
            font-size: 24px;
            animation: float 3s ease-in-out infinite;
            opacity: 0.7;
        }
        
        .heart-1 { top: -10px; left: 10px; animation-delay: 0s; }
        .heart-2 { top: 10px; right: 10px; animation-delay: 0.5s; }
        .heart-3 { bottom: -10px; left: 50%; animation-delay: 1s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #e91e63;
            text-decoration: none;
            font-size: 0.9em;
            transition: color 0.3s ease;
        }
        
        .back-link a:hover {
            color: #c2185b;
        }
    </style>
</head>
<body>
    <div class="message-container">
        <span class="floating-hearts heart-1">❤️</span>
        <span class="floating-hearts heart-2">❤️</span>
        <span class="floating-hearts heart-3">❤️</span>
        
        <div class="heart-message" th:text="${heartMessage}">
        </div>
        
        <div class="back-link">
            <a href="/">返回首页</a>
        </div>
    </div>
</body>
</html> 