<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>登录</title>
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>
    <div class="login-container">
        <h2>用户登录</h2>
        
        <!-- 消息提示 -->
        <div th:if="${success}" class="alert alert-success" th:text="${success}"></div>
        <div th:if="${error}" class="alert alert-danger" th:text="${error}"></div>
        <div th:if="${param.logout}" class="alert alert-success">您已成功退出登录</div>
        
        <!-- 登录表单 -->
        <form th:action="@{/auth/login}" method="post" th:object="${loginRequest}">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" th:field="*{username}" 
                       required minlength="4" maxlength="20">
                <div class="error-message" th:if="${#fields.hasErrors('username')}" 
                     th:errors="*{username}"></div>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" th:field="*{password}" 
                       required minlength="6" maxlength="20">
                <div class="error-message" th:if="${#fields.hasErrors('password')}" 
                     th:errors="*{password}"></div>
            </div>
            
            <div class="form-group">
                <button type="submit">登录</button>
            </div>
            
            <div class="form-links">
                <a th:href="@{/auth/register}">还没有账号？立即注册</a>
                <a th:href="@{/auth/forgot-password}">忘记密码？</a>
            </div>
        </form>
    </div>
</body>
</html> 