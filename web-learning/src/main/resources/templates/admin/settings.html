<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>系统设置</title>
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="stylesheet" th:href="@{/css/admin.css}">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>管理后台</h3>
            </div>
            <nav class="sidebar-nav">
                <a th:href="@{/admin/users}" class="nav-item">用户管理</a>
                <a th:href="@{/admin/roles}" class="nav-item">角色管理</a>
                <a href="#" class="nav-item active">系统设置</a>
            </nav>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <header class="content-header">
                <h2>系统设置</h2>
            </header>
            
            <div class="settings-section">
                <!-- 系统参数设置 -->
                <div class="setting-card">
                    <h3>基本设置</h3>
                    <form class="setting-form">
                        <div class="form-group">
                            <label>系统名称</label>
                            <input type="text" name="systemName" value="Web Learning">
                        </div>
                        <div class="form-group">
                            <label>系统描述</label>
                            <textarea name="systemDescription" rows="3">Web学习平台</textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">保存设置</button>
                    </form>
                </div>
                
                <!-- 安全设置 -->
                <div class="setting-card">
                    <h3>安全设置</h3>
                    <form class="setting-form">
                        <div class="form-group">
                            <label>密码最小长度</label>
                            <input type="number" name="minPasswordLength" value="6">
                        </div>
                        <div class="form-group">
                            <label>登录失败次数限制</label>
                            <input type="number" name="maxLoginAttempts" value="5">
                        </div>
                        <button type="submit" class="btn btn-primary">保存设置</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 