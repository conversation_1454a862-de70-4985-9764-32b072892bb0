<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>用户管理</title>
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="stylesheet" th:href="@{/css/admin.css}">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>管理后台</h3>
            </div>
            <nav class="sidebar-nav">
                <a href="#" class="nav-item active">用户管理</a>
                <a href="#" class="nav-item">角色管理</a>
                <a href="#" class="nav-item">系统设置</a>
            </nav>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <header class="content-header">
                <h2>用户管理</h2>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="showAddUserModal()">添加用户</button>
                </div>
            </header>
            
            <!-- 搜索区域 -->
            <div class="search-section">
                <form class="search-form">
                    <input type="text" placeholder="搜索用户名/邮箱" name="keyword">
                    <button type="submit" class="btn btn-secondary">搜索</button>
                </form>
            </div>
            
            <!-- 用户列表 -->
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>昵称</th>
                            <th>邮箱</th>
                            <th>角色</th>
                            <th>注册时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="user : ${users}">
                            <td th:text="${user.id}">1</td>
                            <td th:text="${user.username}">admin</td>
                            <td th:text="${user.nickname}">管理员</td>
                            <td th:text="${user.email}"><EMAIL></td>
                            <td th:text="${user.roles}">ADMIN</td>
                            <td th:text="${#temporals.format(user.createdAt, 'yyyy-MM-dd HH:mm')}">2024-01-01</td>
                            <td>
                                <button class="btn btn-sm btn-primary" 
                                        th:onclick="'editUser(' + ${user.id} + ')'">编辑</button>
                                <button class="btn btn-sm btn-danger" 
                                        th:onclick="'deleteUser(' + ${user.id} + ')'">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="pagination" th:if="${totalPages > 1}">
                <a th:href="@{/admin/users(page=${currentPage - 1})}" 
                   th:class="${currentPage == 1 ? 'disabled' : ''}">&laquo;</a>
                <span th:each="i : ${#numbers.sequence(1, totalPages)}">
                    <a th:href="@{/admin/users(page=${i})}" 
                       th:text="${i}" 
                       th:class="${currentPage == i ? 'active' : ''}"></a>
                </span>
                <a th:href="@{/admin/users(page=${currentPage + 1})}" 
                   th:class="${currentPage == totalPages ? 'disabled' : ''}">&raquo;</a>
            </div>
        </div>
    </div>
    
    <!-- 用户编辑模态框 -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>编辑用户</h3>
            <form id="userForm">
                <input type="hidden" id="userId">
                <div class="form-group">
                    <label for="editUsername">用户名</label>
                    <input type="text" id="editUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="editNickname">昵称</label>
                    <input type="text" id="editNickname" name="nickname">
                </div>
                <div class="form-group">
                    <label for="editEmail">邮箱</label>
                    <input type="email" id="editEmail" name="email">
                </div>
                <div class="form-group">
                    <label for="editRoles">角色</label>
                    <select id="editRoles" name="roles" multiple>
                        <option value="ROLE_USER">普通用户</option>
                        <option value="ROLE_ADMIN">管理员</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
</body>
</html> 