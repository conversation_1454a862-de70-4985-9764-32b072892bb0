<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>角色管理</title>
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="stylesheet" th:href="@{/css/admin.css}">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>管理后台</h3>
            </div>
            <nav class="sidebar-nav">
                <a th:href="@{/admin/users}" class="nav-item">用户管理</a>
                <a href="#" class="nav-item active">角色管理</a>
                <a href="#" class="nav-item">系统设置</a>
            </nav>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <header class="content-header">
                <h2>角色管理</h2>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="showAddRoleModal()">添加角色</button>
                </div>
            </header>
            
            <!-- 角色列表 -->
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>角色名称</th>
                            <th>描述</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="role : ${roles}">
                            <td th:text="${role.id}">1</td>
                            <td th:text="${role.name}">ROLE_USER</td>
                            <td th:text="${role.description}">普通用户</td>
                            <td th:text="${#temporals.format(role.createdAt, 'yyyy-MM-dd HH:mm')}">2024-01-01</td>
                            <td>
                                <button class="btn btn-sm btn-primary" 
                                        th:onclick="'editRole(' + ${role.id} + ')'">编辑</button>
                                <button class="btn btn-sm btn-danger" 
                                        th:onclick="'deleteRole(' + ${role.id} + ')'">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 角色编辑模态框 -->
    <div id="roleModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>编辑角色</h3>
            <form id="roleForm">
                <input type="hidden" id="roleId">
                <div class="form-group">
                    <label for="roleName">角色名称</label>
                    <input type="text" id="roleName" name="name" required>
                </div>
                <div class="form-group">
                    <label for="roleDescription">描述</label>
                    <textarea id="roleDescription" name="description" rows="3"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
    
    <script th:src="@{/js/admin.js}"></script>
    <script th:src="@{/js/role-admin.js}"></script>
</body>
</html> 