<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>注册</title>
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>
    <div class="register-container">
        <h2>用户注册</h2>
        
        <!-- 消息提示 -->
        <div th:if="${success}" class="alert alert-success" th:text="${success}"></div>
        <div th:if="${error}" class="alert alert-danger" th:text="${error}"></div>
        
        <!-- 注册表单 -->
        <form th:action="@{/auth/register}" method="post" th:object="${userDTO}">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" th:field="*{username}" 
                       required minlength="4" maxlength="20">
                <div class="error-message" th:if="${#fields.hasErrors('username')}" 
                     th:errors="*{username}"></div>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" th:field="*{password}" 
                       required minlength="6" maxlength="20">
                <div class="error-message" th:if="${#fields.hasErrors('password')}" 
                     th:errors="*{password}"></div>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱</label>
                <input type="email" id="email" name="email" th:field="*{email}">
                <div class="error-message" th:if="${#fields.hasErrors('email')}" 
                     th:errors="*{email}"></div>
            </div>
            
            <div class="form-group">
                <label for="nickname">昵称</label>
                <input type="text" id="nickname" name="nickname" th:field="*{nickname}" 
                       maxlength="50">
                <div class="error-message" th:if="${#fields.hasErrors('nickname')}" 
                     th:errors="*{nickname}"></div>
            </div>
            
            <div class="form-group">
                <button type="submit">注册</button>
            </div>
            
            <div class="form-links">
                <a th:href="@{/auth/login}">已有账号？立即登录</a>
            </div>
        </form>
    </div>
</body>
</html> 