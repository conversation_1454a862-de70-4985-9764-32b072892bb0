<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>个人资料</title>
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>
    <div class="profile-container">
        <h2>个人资料</h2>
        
        <!-- 消息提示 -->
        <div th:if="${success}" class="alert alert-success" th:text="${success}"></div>
        <div th:if="${error}" class="alert alert-danger" th:text="${error}"></div>
        
        <!-- 头像部分 -->
        <div class="avatar-section">
            <img th:src="${user.avatar != null ? user.avatar : '/images/default-avatar.png'}" 
                 alt="用户头像" class="avatar">
            <form th:action="@{/profile/avatar}" method="post" enctype="multipart/form-data">
                <div class="file-upload">
                    <input type="file" name="file" id="avatar-upload" accept="image/*">
                    <label for="avatar-upload">选择新头像</label>
                </div>
                <button type="submit" class="btn btn-primary">更新头像</button>
            </form>
        </div>
        
        <!-- 个人信息表单 -->
        <form th:action="@{/profile/update}" method="post" class="profile-form">
            <div class="form-group">
                <label for="nickname">昵称</label>
                <input type="text" id="nickname" name="nickname" 
                       th:value="${user.nickname}" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="email">邮箱</label>
                <input type="email" id="email" name="email" 
                       th:value="${user.email}" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="password">新密码</label>
                <input type="password" id="password" name="password" 
                       class="form-control" placeholder="留空则不修改">
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存修改</button>
                <a th:href="@{/}" class="btn btn-secondary">返回首页</a>
            </div>
        </form>
    </div>
</body>
</html> 