/* 全局样式 */
body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 容器样式 */
.login-container,
.register-container {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

/* 标题样式 */
h2 {
    color: #333;
    text-align: center;
    margin-bottom: 2rem;
}

/* 表单组样式 */
.form-group {
    margin-bottom: 1.5rem;
}

/* 标签样式 */
label {
    display: block;
    margin-bottom: 0.5rem;
    color: #666;
}

/* 输入框样式 */
input {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

input:focus {
    border-color: #4a90e2;
    outline: none;
}

/* 按钮样式 */
button {
    width: 100%;
    padding: 1rem;
    background: #4a90e2;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background: #357abd;
}

/* 链接样式 */
.form-links {
    margin-top: 1rem;
    text-align: center;
}

.form-links a {
    color: #4a90e2;
    text-decoration: none;
    margin: 0 0.5rem;
}

.form-links a:hover {
    text-decoration: underline;
}

/* 错误提示样式 */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    text-align: center;
}

.alert-danger {
    background: #ffe6e6;
    color: #dc3545;
    border: 1px solid #dc3545;
}

/* 个人资料页面样式 */
.profile-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.avatar-section {
    text-align: center;
    margin-bottom: 2rem;
}

.avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
    border: 3px solid #4a90e2;
}

.file-upload {
    position: relative;
    display: inline-block;
    margin: 1rem 0;
}

.file-upload input[type="file"] {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    cursor: pointer;
}

.file-upload label {
    padding: 0.5rem 1rem;
    background: #4a90e2;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.file-upload label:hover {
    background: #357abd;
}

.profile-form {
    max-width: 500px;
    margin: 0 auto;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
}

.btn {
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

.btn-primary {
    background: #4a90e2;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    text-decoration: none;
}

.btn:hover {
    opacity: 0.9;
}

/* 提示消息样式 */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    text-align: center;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 首页样式 */
.home-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 100;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a90e2;
    text-decoration: none;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-link {
    color: #666;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.logout-form {
    margin: 0;
}

.btn-link {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0;
}

.main-content {
    flex: 1;
    padding-top: 80px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.welcome-section {
    text-align: center;
    padding: 4rem 1rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 10px;
    margin: 2rem 1rem;
}

.features-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem 1rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 1rem;
    margin-top: auto;
}

/* 错误消息样式 */
.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .features-section {
        grid-template-columns: 1fr;
    }
    
    .nav {
        flex-direction: column;
        gap: 1rem;
    }
    
    .welcome-section {
        padding: 2rem 1rem;
    }
} 