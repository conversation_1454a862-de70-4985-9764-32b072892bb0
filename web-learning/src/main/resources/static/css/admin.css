/* 管理后台布局 */
.admin-container {
    display: flex;
    min-height: 100vh;
    background: #f4f6f9;
}

/* 侧边栏样式 */
.sidebar {
    width: 250px;
    background: #343a40;
    color: #fff;
    padding: 1rem 0;
}

.sidebar-header {
    padding: 0 1rem;
    margin-bottom: 2rem;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
}

.nav-item {
    padding: 0.75rem 1rem;
    color: #c2c7d0;
    text-decoration: none;
    transition: all 0.3s;
}

.nav-item:hover,
.nav-item.active {
    background: #2b3035;
    color: #fff;
}

/* 主内容区样式 */
.main-content {
    flex: 1;
    padding: 2rem;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

/* 表格样式 */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
}

/* 按钮样式 */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

/* 搜索区域样式 */
.search-section {
    margin-bottom: 2rem;
}

.search-form {
    display: flex;
    gap: 1rem;
    max-width: 500px;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination a {
    padding: 0.5rem 1rem;
    background: white;
    border: 1px solid #dee2e6;
    color: #007bff;
    text-decoration: none;
}

.pagination a.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.pagination a.disabled {
    color: #6c757d;
    pointer-events: none;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
}

.modal-content {
    background: white;
    width: 90%;
    max-width: 500px;
    margin: 2rem auto;
    padding: 2rem;
    border-radius: 4px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
} 