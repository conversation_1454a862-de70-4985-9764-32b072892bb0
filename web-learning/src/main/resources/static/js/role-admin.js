// 角色管理相关功能
function showAddRoleModal() {
    document.getElementById('roleId').value = '';
    document.getElementById('roleForm').reset();
    document.getElementById('roleModal').style.display = 'block';
}

function editRole(roleId) {
    fetch(`/api/roles/${roleId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('roleId').value = data.id;
            document.getElementById('roleName').value = data.name;
            document.getElementById('roleDescription').value = data.description;
            document.getElementById('roleModal').style.display = 'block';
        });
}

function deleteRole(roleId) {
    if (confirm('确定要删除这个角色吗？删除角色可能会影响到已分配该角色的用户。')) {
        fetch(`/api/roles/${roleId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('删除失败，请稍后重试');
            }
        });
    }
}

// 角色表单提交处理
document.getElementById('roleForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const roleId = document.getElementById('roleId').value;
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    fetch(`/api/roles/${roleId || ''}`, {
        method: roleId ? 'PUT' : 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => {
        if (response.ok) {
            closeModal();
            location.reload();
        } else {
            alert('保存失败，请稍后重试');
        }
    });
}); 