// 用户管理相关功能
function showAddUserModal() {
    document.getElementById('userId').value = '';
    document.getElementById('userForm').reset();
    document.getElementById('userModal').style.display = 'block';
}

function editUser(userId) {
    // 获取用户信息并显示在模态框中
    fetch(`/api/users/${userId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('userId').value = data.id;
            document.getElementById('editUsername').value = data.username;
            document.getElementById('editNickname').value = data.nickname;
            document.getElementById('editEmail').value = data.email;
            // 设置角色
            const roleSelect = document.getElementById('editRoles');
            Array.from(roleSelect.options).forEach(option => {
                option.selected = data.roles.includes(option.value);
            });
            document.getElementById('userModal').style.display = 'block';
        });
}

function closeModal() {
    document.getElementById('userModal').style.display = 'none';
}

function deleteUser(userId) {
    if (confirm('确定要删除这个用户吗？')) {
        fetch(`/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('删除失败，请稍后重试');
            }
        });
    }
}

// 表单提交处理
document.getElementById('userForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const userId = document.getElementById('userId').value;
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    fetch(`/api/users/${userId || ''}`, {
        method: userId ? 'PUT' : 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => {
        if (response.ok) {
            closeModal();
            location.reload();
        } else {
            alert('保存失败，请稍后重试');
        }
    });
});

// 关闭模态框
document.querySelector('.close').addEventListener('click', closeModal);
window.addEventListener('click', function(e) {
    if (e.target == document.getElementById('userModal')) {
        closeModal();
    }
}); 