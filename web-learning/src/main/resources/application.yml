server:
  port: 8083

spring:
  application:
    name: web-learning
  
  thymeleaf:
    cache: false
    mode: HTML
    encoding: UTF-8
    prefix: classpath:/templates/
    suffix: .html
  
  security:
    headers:
      frame: DENY
      xss: 1; mode=block
      content-type-options: nosniff
      content-security-policy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
    csrf:
      cookie:
        name: XSRF-TOKEN
        http-only: false
        secure: true

  # 允许bean覆盖
  main:
    allow-bean-definition-overriding: true

# 用户中心配置
user-center:
  url: http://localhost:8084
  connect-timeout: 5000
  read-timeout: 5000

# 统一日志配置
common:
  logging:
    enabled: true
    log-path: logs
    level: INFO
    module-name: ${spring.application.name}
    max-history: 30
    max-file-size: 100MB
    enable-console: true
    pattern: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([%thread]){cyan} %clr(%-5level){red} %clr(%logger{36}){yellow} %clr([%X{traceId}]){green} - %msg%n"

    # 包级别日志配置
    package-levels:
      com.example: DEBUG
      org.springframework: INFO

    # 异步日志配置
    async:
      enabled: true
      queue-size: 512

    # MDC配置
    mdc:
      id-generator: snowflake
      max-length: 32
      enable-prefix: true
      prefix: "redis-"
      include-timestamp: true
      timestamp-format: "MMddHHmmss"
      # snowflake配置
      snowflake:
        worker-id: 1
        data-center-id: 1
        sequence: 0

# 只保留日志级别配置
logging:
  level:
    root: INFO
    com.example: DEBUG
    org.springframework.data.redis: INFO
