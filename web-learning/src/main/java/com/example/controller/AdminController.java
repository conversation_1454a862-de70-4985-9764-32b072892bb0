package com.example.controller;

import com.example.client.UserClient;
import com.example.common.model.Result;
import com.example.user.dto.UserDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Controller
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
@RequiredArgsConstructor
public class AdminController {
    private final UserClient userClient;
    
    @GetMapping("/users")
    public String userList(@RequestParam(defaultValue = "1") int page,
                         @RequestParam(defaultValue = "10") int size,
                         @RequestParam(required = false) String keyword,
                         Model model) {
        Result<Page<UserDTO>> result = userClient.findUsers(page, size, keyword);
        if (result.isSuccess()) {
            model.addAttribute("users", result.getData().getContent());
            model.addAttribute("currentPage", page);
            model.addAttribute("totalPages", result.getData().getTotalPages());
            model.addAttribute("keyword", keyword);
        }
        return "admin/users";
    }
    
    @GetMapping("/roles")
    public String roleList(Model model) {
        Result<List<RoleDTO>> result = userClient.findAllRoles();
        if (result.isSuccess()) {
            model.addAttribute("roles", result.getData());
        }
        return "admin/roles";
    }
    
    @GetMapping("/settings")
    public String settings(Model model) {
        // 系统设置页面
        return "admin/settings";
    }
} 