package com.example.controller;

import com.example.client.UserClient;
import com.example.common.model.Result;
import com.example.user.dto.UserDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Controller
@RequiredArgsConstructor
public class WebController {
    private final UserClient userClient;
    
    @GetMapping("/")
    public String home() {
        return "home";
    }
    
    @GetMapping("/login")
    public String loginPage() {
        return "login";
    }
    
    @GetMapping("/register")
    public String registerPage() {
        return "register";
    }
    
    @GetMapping("/profile")
    public String profilePage(Model model) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated()) {
            Result<UserDTO> result = userClient.validateToken(auth.getCredentials().toString());
            if (result.isSuccess()) {
                model.addAttribute("user", result.getData());
                return "profile";
            }
        }
        return "redirect:/login";
    }
    
    @PostMapping("/profile/avatar")
    public String handleAvatarUpload(@RequestParam("file") MultipartFile file, Model model) {
        try {
            String avatarUrl = uploadAvatar(file);  // 实现文件上传
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            UserDTO user = userClient.validateToken(auth.getCredentials().toString()).getData();
            user.setAvatar(avatarUrl);
            userClient.update(user.getId(), user);
            model.addAttribute("success", "头像更新成功");
        } catch (Exception e) {
            model.addAttribute("error", "头像更新失败: " + e.getMessage());
        }
        return "profile";
    }
    
    private String uploadAvatar(MultipartFile file) {
        // 实现文件上传逻辑
        return "avatar-url";
    }
} 