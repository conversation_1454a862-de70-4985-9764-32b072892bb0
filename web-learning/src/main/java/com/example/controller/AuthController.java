package com.example.controller;

import com.example.client.UserClient;
import com.example.common.model.Result;
import com.example.model.LoginRequest;
import com.example.user.dto.UserDTO;
import jakarta.servlet.http.HttpSession;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Slf4j
@Controller
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {
    private final UserClient userClient;
    
    @GetMapping("/login")
    public String loginPage(Model model) {
        if (!model.containsAttribute("loginRequest")) {
            model.addAttribute("loginRequest", new LoginRequest());
        }
        return "login";
    }
    
    @GetMapping("/register")
    public String registerPage(Model model) {
        if (!model.containsAttribute("userDTO")) {
            model.addAttribute("userDTO", new UserDTO());
        }
        return "register";
    }
    
    @PostMapping("/login")
    public String handleLogin(@Valid @ModelAttribute LoginRequest loginRequest,
                            BindingResult bindingResult,
                            HttpSession session,
                            RedirectAttributes redirectAttributes) {
        if (bindingResult.hasErrors()) {
            redirectAttributes.addFlashAttribute("error", "请输入有效的用户名和密码");
            redirectAttributes.addFlashAttribute("loginRequest", loginRequest);
            redirectAttributes.addFlashAttribute("org.springframework.validation.BindingResult.loginRequest", bindingResult);
            return "redirect:/auth/login";
        }
        
        try {
            Result<String> result = userClient.login(
                loginRequest.getUsername(), 
                loginRequest.getPassword()
            );
            
            if (result.isSuccess()) {
                session.setAttribute("token", result.getData());
                return "redirect:/profile";
            } else {
                redirectAttributes.addFlashAttribute("error", result.getMessage());
                redirectAttributes.addFlashAttribute("loginRequest", loginRequest);
                return "redirect:/auth/login";
            }
        } catch (Exception e) {
            log.error("Login failed: ", e);
            redirectAttributes.addFlashAttribute("error", "登录失败,请稍后重试");
            redirectAttributes.addFlashAttribute("loginRequest", loginRequest);
            return "redirect:/auth/login";
        }
    }
    
    @PostMapping("/register")
    public String handleRegister(@Valid @ModelAttribute UserDTO userDTO,
                               BindingResult bindingResult,
                               RedirectAttributes redirectAttributes) {
        if (bindingResult.hasErrors()) {
            redirectAttributes.addFlashAttribute("error", "请检查输入信息");
            redirectAttributes.addFlashAttribute("userDTO", userDTO);
            redirectAttributes.addFlashAttribute("org.springframework.validation.BindingResult.userDTO", bindingResult);
            return "redirect:/auth/register";
        }
        
        try {
            Result<UserDTO> result = userClient.register(userDTO);
            if (result.isSuccess()) {
                redirectAttributes.addFlashAttribute("success", "注册成功，请登录");
                return "redirect:/auth/login";
            } else {
                redirectAttributes.addFlashAttribute("error", result.getMessage());
                redirectAttributes.addFlashAttribute("userDTO", userDTO);
                return "redirect:/auth/register";
            }
        } catch (Exception e) {
            log.error("Registration failed: ", e);
            redirectAttributes.addFlashAttribute("error", "注册失败,请稍后重试");
            redirectAttributes.addFlashAttribute("userDTO", userDTO);
            return "redirect:/auth/register";
        }
    }
    
    @PostMapping("/logout")
    public String handleLogout(HttpSession session) {
        session.invalidate();
        return "redirect:/auth/login?logout=true";
    }
} 