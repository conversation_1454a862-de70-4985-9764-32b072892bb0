package com.example.controller;

import com.example.client.UserClient;
import com.example.common.model.Result;
import com.example.user.dto.UserDTO;
import com.example.util.SessionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Slf4j
@Controller
@RequiredArgsConstructor
public class PageController {
    private final UserClient userClient;
    
    @GetMapping("/")
    public String home(Model model) {
        UserDTO currentUser = SessionUtil.getCurrentUser();
        model.addAttribute("user", currentUser);
        return "home";
    }
    
    @GetMapping("/profile")
    public String profile(Model model) {
        UserDTO currentUser = SessionUtil.getCurrentUser();
        Result<UserDTO> result = userClient.findById(currentUser.getId());
        if (result.isSuccess()) {
            model.addAttribute("user", result.getData());
            return "profile";
        }
        return "redirect:/auth/login";
    }
    
    @GetMapping("/error")
    public String error() {
        return "error";
    }
} 