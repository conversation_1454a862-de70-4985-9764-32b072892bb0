package com.example.controller;

import com.example.client.UserClient;
import com.example.common.model.Result;
import com.example.service.FileService;
import com.example.user.dto.UserDTO;
import com.example.util.SessionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Slf4j
@Controller
@RequestMapping("/profile")
@RequiredArgsConstructor
public class ProfileController {
    private final UserClient userClient;
    private final FileService fileService;
    
    @PostMapping("/update")
    public String updateProfile(UserDTO userDTO, RedirectAttributes redirectAttributes) {
        try {
            UserDTO currentUser = SessionUtil.getCurrentUser();
            userDTO.setId(currentUser.getId());
            
            Result<UserDTO> result = userClient.update(currentUser.getId(), userDTO);
            if (result.isSuccess()) {
                redirectAttributes.addFlashAttribute("success", "个人资料更新成功");
            } else {
                redirectAttributes.addFlashAttribute("error", result.getMessage());
            }
        } catch (Exception e) {
            log.error("Update profile failed: ", e);
            redirectAttributes.addFlashAttribute("error", "更新失败，请稍后重试");
        }
        return "redirect:/profile";
    }
    
    @PostMapping("/avatar")
    public String updateAvatar(@RequestParam("file") MultipartFile file, 
                             RedirectAttributes redirectAttributes) {
        try {
            UserDTO currentUser = SessionUtil.getCurrentUser();
            String avatarUrl = fileService.uploadAvatar(file);
            
            UserDTO updateDTO = new UserDTO();
            updateDTO.setId(currentUser.getId());
            updateDTO.setAvatar(avatarUrl);
            
            Result<UserDTO> result = userClient.update(currentUser.getId(), updateDTO);
            if (result.isSuccess()) {
                redirectAttributes.addFlashAttribute("success", "头像更新成功");
            } else {
                redirectAttributes.addFlashAttribute("error", result.getMessage());
            }
        } catch (Exception e) {
            log.error("Update avatar failed: ", e);
            redirectAttributes.addFlashAttribute("error", "头像更新失败，请稍后重试");
        }
        return "redirect:/profile";
    }
} 