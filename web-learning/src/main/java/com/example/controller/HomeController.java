package com.example.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Slf4j
@Controller
public class HomeController {

    @GetMapping({"/", "/home"})
    public String home(Model model) {
        log.info("访问首页");
        model.addAttribute("message", "欢迎来到我们的小天地! 💕");
        return "home";
    }

    @GetMapping("/message")
    public String message(Model model) {
        log.info("访问暖心消息页面");
        String heartMessage = "宝贝，听说你今天去广州吃饭了，应该很开心吧！出差辛苦了，路上要小心，安全第一，尽量别太累了。" +
                "回肇庆的路上记得照顾好自己，早点休息，调整好状态。虽然我不在身边，但你有任何需要的，随时告诉我，" +
                "我会尽力帮忙的。希望你一切顺利，盼你来郑州！❤️";
        model.addAttribute("heartMessage", heartMessage);
        return "message";
    }
} 