package com.example.client;

import com.example.common.model.Result;
import com.example.user.api.UserApi;
import com.example.user.dto.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UserClientFallback implements FallbackFactory<UserApi> {
    
    @Override
    public UserApi create(Throwable cause) {
        return new UserApi() {
            @Override
            public Result<UserDTO> register(UserDTO userDTO) {
                log.error("用户注册服务降级", cause);
                return Result.error("SERVICE_DOWN", "注册服务暂时不可用");
            }
            
            @Override
            public Result<String> login(String username, String password) {
                log.error("用户登录服务降级", cause);
                return Result.error("SERVICE_DOWN", "登录服务暂时不可用");
            }
            
            @Override
            public Result<UserDTO> validateToken(String token) {
                log.error("Token验证服务降级", cause);
                return Result.error("SERVICE_DOWN", "验证服务暂时不可用");
            }
            
            @Override
            public Result<UserDTO> update(Long id, UserDTO userDTO) {
                log.error("用户更新服务降级", cause);
                return Result.error("SERVICE_DOWN", "更新服务暂时不可用");
            }
            
            @Override
            public Result<Void> delete(Long id) {
                log.error("用户删除服务降级", cause);
                return Result.error("SERVICE_DOWN", "删除服务暂时不可用");
            }
            
            @Override
            public Result<UserDTO> findById(Long id) {
                log.error("用户查询服务降级", cause);
                return Result.error("SERVICE_DOWN", "查询服务暂时不可用");
            }
        };
    }
} 