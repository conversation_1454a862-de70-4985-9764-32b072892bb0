package com.example.client;

import com.example.user.api.UserApi;
import com.example.common.model.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

@FeignClient(
    name = "user-center",
    url = "${user-center.url:http://localhost:8084}",
    fallbackFactory = UserClientFallback.class
)
@Component
public interface UserClient extends UserApi {
    // 继承UserApi接口即可,无需额外定义方法
} 