package com.example.service.impl;

import com.example.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

@Slf4j
@Service
public class LocalFileServiceImpl implements FileService {
    
    @Value("${app.upload.dir:uploads}")
    private String uploadDir;
    
    @Value("${app.upload.avatar.dir:avatars}")
    private String avatarDir;
    
    @Override
    public String uploadAvatar(MultipartFile file) throws IOException {
        String filename = generateFilename(file);
        Path uploadPath = getUploadPath(avatarDir);
        Path filePath = uploadPath.resolve(filename);
        
        Files.copy(file.getInputStream(), filePath);
        
        return String.format("/uploads/avatars/%s", filename);
    }
    
    @Override
    public void deleteFile(String fileUrl) throws IOException {
        if (fileUrl != null && !fileUrl.isEmpty()) {
            Path filePath = Paths.get(uploadDir).resolve(
                fileUrl.substring(fileUrl.indexOf("/uploads/") + 9)
            );
            Files.deleteIfExists(filePath);
        }
    }
    
    private String generateFilename(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename != null ? 
            originalFilename.substring(originalFilename.lastIndexOf(".")) : "";
        return UUID.randomUUID().toString() + extension;
    }
    
    private Path getUploadPath(String subDir) throws IOException {
        Path uploadPath = Paths.get(uploadDir, subDir);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }
        return uploadPath;
    }
} 