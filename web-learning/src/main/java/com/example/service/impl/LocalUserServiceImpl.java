package com.example.service.impl;

import com.example.model.User;
import com.example.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Service
@RequiredArgsConstructor
public class LocalUserServiceImpl implements UserService {
    private final PasswordEncoder passwordEncoder;
    private final Map<Long, User> userMap = new ConcurrentHashMap<>();
    private final Map<String, User> userNameMap = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);

    @Override
    public User register(User user) {
        log.info("本地用户注册: {}", user.getUsername());
        
        if (userNameMap.containsKey(user.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        user.setId(idGenerator.getAndIncrement());
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        userMap.put(user.getId(), user);
        userNameMap.put(user.getUsername(), user);
        
        return user;
    }

    @Override
    public Optional<User> findByUsername(String username) {
        return Optional.ofNullable(userNameMap.get(username));
    }

    @Override
    public List<User> findAll() {
        return new ArrayList<>(userMap.values());
    }

    @Override
    public User update(User user) {
        if (!userMap.containsKey(user.getId())) {
            throw new RuntimeException("用户不存在");
        }
        
        User existingUser = userMap.get(user.getId());
        if (user.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        
        userMap.put(user.getId(), user);
        userNameMap.put(user.getUsername(), user);
        
        return user;
    }

    @Override
    public void delete(Long id) {
        User user = userMap.remove(id);
        if (user != null) {
            userNameMap.remove(user.getUsername());
        }
    }

    @Override
    public Optional<User> findById(Long id) {
        return Optional.ofNullable(userMap.get(id));
    }
} 