package com.example.service;

import com.example.model.User;
import java.util.List;
import java.util.Optional;

public interface UserService {
    /**
     * 注册本地用户
     */
    User register(User user);

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据ID查找用户
     */
    Optional<User> findById(Long id);

    /**
     * 获取所有用户
     */
    List<User> findAll();

    /**
     * 更新用户信息
     */
    User update(User user);

    /**
     * 删除用户
     */
    void delete(Long id);
} 