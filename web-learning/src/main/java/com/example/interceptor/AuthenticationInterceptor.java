package com.example.interceptor;

import com.example.client.UserClient;
import com.example.common.model.Result;
import com.example.user.dto.UserDTO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationInterceptor implements HandlerInterceptor {
    private final UserClient userClient;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        HttpSession session = request.getSession(false);
        if (session != null) {
            String token = (String) session.getAttribute("token");
            if (token != null) {
                try {
                    Result<UserDTO> result = userClient.validateToken(token);
                    if (result.isSuccess()) {
                        request.setAttribute("currentUser", result.getData());
                        return true;
                    }
                } catch (Exception e) {
                    log.error("Token validation failed: ", e);
                }
            }
        }
        response.sendRedirect("/auth/login");
        return false;
    }
} 