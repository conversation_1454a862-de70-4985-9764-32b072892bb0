package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(
    scanBasePackages = "com.example",
    exclude = {
        org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class,
        org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class  // 排除数据源自动配置
    }
)
@EnableFeignClients
public class WebLearningApplication {
    public static void main(String[] args) {
        SpringApplication.run(WebLearningApplication.class, args);
    }
} 