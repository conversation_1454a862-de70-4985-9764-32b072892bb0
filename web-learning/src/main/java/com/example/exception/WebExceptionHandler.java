package com.example.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@Slf4j
@ControllerAdvice("com.example.controller")
public class WebExceptionHandler {

    @ExceptionHandler(Exception.class)
    public String handleException(Exception e, Model model) {
        log.error("Unexpected error: ", e);
        model.addAttribute("error", "系统错误，请稍后重试");
        return "error";
    }
    
    @ExceptionHandler(SecurityException.class)
    public String handleSecurityException(SecurityException e, Model model) {
        log.error("Security error: ", e);
        model.addAttribute("error", "访问被拒绝");
        return "error";
    }
} 