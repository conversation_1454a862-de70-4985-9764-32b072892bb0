# Web Learning Module

这是一个基于Spring Boot的Web学习模块,用于演示Web开发相关的功能和最佳实践。

## 功能特性

- Spring MVC
- Thymeleaf模板引擎
- 响应式布局
- 统一日志处理

## 如何运行

1. 确保安装了JDK 17和Maven
2. 在项目根目录执行: `mvn clean install`
3. 进入web-learning目录: `cd web-learning`
4. 运行应用: `mvn spring-boot:run`
5. 访问: `http://localhost:8083`

## 项目结构

- `src/main/java/com/example`
  - `WebLearningApplication.java`: 应用入口
  - `controller/`: 控制器
  - `service/`: 业务逻辑
  - `model/`: 数据模型
- `src/main/resources`
  - `templates/`: Thymeleaf模板
  - `static/`: 静态资源
  - `application.yml`: 配置文件 