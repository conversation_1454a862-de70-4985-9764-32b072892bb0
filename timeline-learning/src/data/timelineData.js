export const timelineData = [
  {
    date: '2020-10-12',
    amount: 30000,
    purpose: '北京车牌费用',
    transferPerson: '薛明丽(建行)',
    note: '岳海洋指示，用于办理北京车牌'
  },
  {
    date: '2020-10-21',
    amount: 50000,
    purpose: '100张加油卡',
    transferPerson: '岳海洋',
    note: '总价值9.1万元，分4笔转账'
  },
  {
    date: '2020-10-23',
    amount: 41000,
    purpose: '100张加油卡尾款',
    transferPerson: '薛明丽',
    note: '完成交易，双方清算'
  },
  {
    date: '2020-10-26',
    amount: 970000,
    purpose: '1000张加油卡',
    transferPerson: '岳海洋',
    note: '分4笔转账40万元'
  },
  {
    date: '2020-10-27',
    amount: 400000,
    purpose: '加油卡交易',
    transferPerson: '薛明丽(华夏银行)',
    note: '继续加油卡交易'
  },
  {
    date: '2020-10-28',
    amount: 570000,
    purpose: '加油卡交易',
    transferPerson: '薛明丽',
    note: '分别转账55万元和2万元'
  },
  {
    date: '2020-11-01',
    amount: 0,
    purpose: '加油卡交付',
    transferPerson: '王杰',
    note: '签收45张加油卡，余卡邮寄北京'
  },
  {
    date: '2020-11-09',
    amount: 0,
    purpose: '加油卡交付',
    transferPerson: '田景屹',
    note: '西客站交付1100张加油卡，确认欠款9.7万'
  },
  {
    date: '2020-11-10',
    amount: 97000,
    purpose: '加油卡尾款',
    transferPerson: '薛明丽',
    note: '支付欠款9.7万元'
  },
  {
    date: '2020-11-18',
    amount: 300000,
    purpose: '300张加油卡',
    transferPerson: '薛明丽',
    note: '分两笔转账(24万+6万)'
  },
  {
    date: '2020-11-19',
    amount: 238300,
    purpose: '加油卡尾款',
    transferPerson: '薛明丽',
    note: '支付欠款240300元'
  },
  {
    date: '2020-11-23',
    amount: 200000,
    purpose: '履约保证金',
    transferPerson: '薛明丽',
    note: '非加油卡款项'
  },
  {
    date: '2020-12-02',
    amount: 200000,
    purpose: '加油卡款',
    transferPerson: '薛明丽',
    note: '分两笔各10万元'
  },
  {
    date: '2020-12-17',
    amount: 150000,
    purpose: '中石化现卡定金',
    transferPerson: '薛明丽',
    note: '购买1200张中石化现卡'
  },
  {
    date: '2020-12-20',
    amount: 174800,
    purpose: '中石化现卡定金',
    transferPerson: '岳海洋',
    note: '1200张中石化现卡交易完成'
  }
]; 