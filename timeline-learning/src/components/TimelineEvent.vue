<template>
  <div 
    class="timeline-event"
    :class="{ active: isActive }"
    :style="{ backgroundColor: color }"
    @click="$emit('click')"
  >
    <div class="event-date">{{ formattedDate }}</div>
    <div class="event-amount">{{ formattedAmount }}</div>
    <div class="event-purpose">{{ event.purpose }}</div>
    <div class="event-note" v-show="isActive">{{ event.note }}</div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { formatDate, formatAmount } from '../utils/formatters';

const props = defineProps({
  event: Object,
  isActive: Boolean,
  color: String
});

const formattedDate = computed(() => formatDate(props.event.date));
const formattedAmount = computed(() => formatAmount(props.event.amount));
</script> 