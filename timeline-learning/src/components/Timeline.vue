// src/data/timelineData.js
export const timelineData = [
  {
    date: '2020-10-12',
    amount: 30000,
    purpose: '北京车牌费用',
    transferPerson: '薛明丽(建行)',
    note: '岳海洋指示，用于办理北京车牌'
  },
  {
    date: '2020-10-21',
    amount: 50000,
    purpose: '100张加油卡',
    transferPerson: '岳海洋',
    note: '总价值9.1万元，分4笔转账'
  },
  {
    date: '2020-10-23',
    amount: 41000,
    purpose: '100张加油卡尾款',
    transferPerson: '薛明丽',
    note: '完成交易，双方清算'
  },
  {
    date: '2020-10-26',
    amount: 970000,
    purpose: '1000张加油卡',
    transferPerson: '岳海洋',
    note: '分4笔转账40万元'
  },
  {
    date: '2020-10-27',
    amount: 400000,
    purpose: '加油卡交易',
    transferPerson: '薛明丽(华夏银行)',
    note: '继续加油卡交易'
  },
  {
    date: '2020-10-28',
    amount: 570000,
    purpose: '加油卡交易',
    transferPerson: '薛明丽',
    note: '分别转账55万元和2万元'
  },
  {
    date: '2020-11-01',
    amount: 0,
    purpose: '加油卡交付',
    transferPerson: '王杰',
    note: '签收45张加油卡，余卡邮寄北京'
  },
  {
    date: '2020-11-09',
    amount: 0,
    purpose: '加油卡交付',
    transferPerson: '田景屹',
    note: '西客站交付1100张加油卡，确认欠款9.7万'
  },
  {
    date: '2020-11-10',
    amount: 97000,
    purpose: '加油卡尾款',
    transferPerson: '薛明丽',
    note: '支付欠款9.7万元'
  },
  {
    date: '2020-11-18',
    amount: 300000,
    purpose: '300张加油卡',
    transferPerson: '薛明丽',
    note: '分两笔转账(24万+6万)'
  },
  {
    date: '2020-11-19',
    amount: 238300,
    purpose: '加油卡尾款',
    transferPerson: '薛明丽',
    note: '支付欠款240300元'
  },
  {
    date: '2020-11-23',
    amount: 200000,
    purpose: '履约保证金',
    transferPerson: '薛明丽',
    note: '非加油卡款项'
  },
  {
    date: '2020-12-02',
    amount: 200000,
    purpose: '加油卡款',
    transferPerson: '薛明丽',
    note: '分两笔各10万元'
  },
  {
    date: '2020-12-17',
    amount: 150000,
    purpose: '中石化现卡定金',
    transferPerson: '薛明丽',
    note: '购买1200张中石化现卡'
  },
  {
    date: '2020-12-20',
    amount: 174800,
    purpose: '中石化现卡定金',
    transferPerson: '岳海洋',
    note: '1200张中石化现卡交易完成'
  }
];

// src/constants/timeline.js
export const MONTHS = ['十月', '十一月', '十二月'];
export const EVENT_COLORS = ['#B8D7FF', '#FFE4B8', '#B8FFD9', '#FFB8B8', '#E4B8FF', '#B8FFF9'];

// src/utils/formatters.js
export const formatDate = (dateString) => dateString.split('-')[2] + '日';
export const formatAmount = (amount) => (amount / 10000).toFixed(2) + '万元';

<template>
  <div class="timeline-container">
    <div class="overview-section">
      <h2>交易总览</h2>
      <div class="overview-content">
        <div class="overview-item">
          <h3>💰 交易金额</h3>
          <div class="overview-details">
            <p>总转账金额：<span class="highlight">{{ getTotalAmount() }}万元</span></p>
            <p>最大单笔：<span class="highlight">97万元</span>（1000张加油卡）</p>
            <p>交易笔数：<span class="highlight">15笔</span></p>
          </div>
        </div>
        <div class="overview-item">
          <h3>📊 交易分类</h3>
          <div class="overview-details">
            <p>加油卡交易：<span class="highlight">11笔</span></p>
            <p>车牌费用：<span class="highlight">1笔</span></p>
            <p>保证金：<span class="highlight">1笔</span></p>
            <p>其他交易：<span class="highlight">2笔</span></p>
          </div>
        </div>
        <div class="overview-item">
          <h3>👥 交易参与方</h3>
          <div class="overview-details">
            <div class="participant">
              <p class="role">主要转账人：</p>
              <ul>
                <li>薛明丽：<span class="highlight">11笔</span></li>
                <li>岳海洋：<span class="highlight">3笔</span></li>
                <li>其他：<span class="highlight">1笔</span></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="month-selector">
      <button 
        v-for="month in months" 
        :key="month"
        :class="{ active: selectedMonth === month }"
        @click="selectedMonth = month"
        :aria-label="`${month}交易记录，总金额${getMonthTotal(groupedData[month])}万元`"
        :aria-pressed="selectedMonth === month"
      >
        {{ month }}份交易
        <span class="month-total">({{ getMonthTotal(groupedData[month]) }}万元)</span>
      </button>
    </div>

    <div class="month-content" v-if="selectedMonth">
      <div class="timeline">
        <div class="timeline-line"></div>
        <div class="timeline-events">
          <div v-for="(item, index) in groupedData[selectedMonth]" 
               :key="index" 
               class="timeline-event"
               :class="{ active: selectedEvent === index }"
               :style="{ backgroundColor: getEventColor(index) }"
               @click="handleEventClick(index)">
            <div class="event-date">{{ formatDate(item.date) }}</div>
            <div class="event-amount">{{ formatAmount(item.amount) }}</div>
            <div class="event-purpose">{{ item.purpose }}</div>
            <div class="event-note" v-show="selectedEvent === index">{{ item.note }}</div>
          </div>
        </div>
      </div>
      <div class="transaction-details">
        <table>
          <thead>
            <tr>
              <th>日期</th>
              <th>金额</th>
              <th>用途</th>
              <th>转账人</th>
              <th>备注</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in groupedData[selectedMonth]" 
                :key="index"
                :class="{ 'highlighted': selectedEvent === index }">
              <td>{{ item.date.split('-')[2] }}</td>
              <td>{{ formatAmount(item.amount) }}</td>
              <td>{{ item.purpose }}</td>
              <td>{{ item.transferPerson }}</td>
              <td>{{ item.note }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { timelineData as rawTimelineData } from '../data/timelineData'
import { EVENT_COLORS } from '../constants/colors'
import { formatDate, formatAmount } from '../utils/formatters'

// 简单的节流函数
const throttle = (fn, delay) => {
  let lastCall = 0;
  return (...args) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      fn(...args);
      lastCall = now;
    }
  };
};

const timelineData = ref(rawTimelineData);

const selectedMonth = ref('十月')
const selectedEvent = ref(null)
const months = ['十月', '十一月', '十二月']

const groupedData = computed(() => {
  const groups = new Map([
    ['十月', []],
    ['十一月', []],
    ['十二月', []]
  ]);
  
  timelineData.value.forEach(item => {
    const month = item.date.split('-')[1];
    const monthMap = {
      '10': '十月',
      '11': '十一月',
      '12': '十二月'
    };
    const groupKey = monthMap[month];
    if (groupKey) {
      groups.get(groupKey).push(item);
    }
  });
  
  return Object.fromEntries(groups);
})

const getMonthTotal = (monthData) => {
  if (!Array.isArray(monthData)) return '0.00';
  try {
    return (monthData.reduce((sum, item) => sum + (item.amount || 0), 0) / 10000).toFixed(2);
  } catch (error) {
    console.error('计算月度总额时出错:', error);
    return '0.00';
  }
}

const getEventColor = (index) => {
  return EVENT_COLORS[index % EVENT_COLORS.length]
}

const handleEventClick = throttle((index) => {
  selectedEvent.value = selectedEvent.value === index ? null : index;
}, 300);

// 计算总金额
const getTotalAmount = () => {
  const total = timelineData.value.reduce((sum, item) => sum + item.amount, 0);
  return (total / 10000).toFixed(2);
};
</script>

<style scoped>
.timeline-container {
  padding: 20px;
  background: #fff;
}

.overview-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 40px;
}

.overview-content {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.overview-item {
  width: 32%;
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.overview-item ul {
  list-style: none;
  margin: 10px 0 0 20px;
}

.month-selector {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
}

.month-selector button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  background: #f0f0f0;
  cursor: pointer;
  font-size: 1.1em;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.month-selector button.active {
  background: #0396FF;
  color: white;
  box-shadow: 0 4px 15px rgba(3, 150, 255, 0.2);
}

.month-selector button:hover {
  transform: translateY(-2px);
}

.month-total {
  font-size: 0.9em;
  margin-top: 5px;
  opacity: 0.8;
}

.timeline {
  position: relative;
  padding: 40px 0;
  margin-bottom: 30px;
  overflow-x: auto;
}

.timeline-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
}

.timeline-events {
  position: relative;
  display: flex;
  justify-content: space-between;
  gap: 20px;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.timeline-event {
  width: 180px;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 auto;
}

.timeline-event.active {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.timeline-event:hover {
  transform: translateY(-5px);
}

.event-date {
  font-weight: bold;
  margin-bottom: 5px;
}

.event-amount {
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 5px;
}

.event-purpose {
  font-size: 0.9em;
  margin-bottom: 5px;
}

.event-note {
  font-size: 0.8em;
  color: #666;
}

.transaction-details {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

th {
  background: #f1f1f1;
  font-weight: bold;
}

tr.highlighted {
  background-color: #f8f9fa;
}

.month-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .overview-content {
    flex-direction: column;
    gap: 20px;
  }

  .overview-item {
    width: 100%;
  }

  .timeline-events {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .timeline-line {
    display: none;
  }

  .timeline-event {
    width: 100%;
    max-width: 300px;
  }
}

.overview-item h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.2em;
}

.overview-details {
  color: #666;
}

.overview-details p {
  margin: 8px 0;
  font-size: 0.95em;
}

.highlight {
  color: #0396FF;
  font-weight: 600;
}

.participant {
  margin-top: 10px;
}

.role {
  font-weight: 500;
  margin-bottom: 5px;
}

.overview-details ul {
  list-style: none;
  margin: 5px 0 0 15px;
}

.overview-details li {
  margin: 5px 0;
  color: #666;
}
</style> 