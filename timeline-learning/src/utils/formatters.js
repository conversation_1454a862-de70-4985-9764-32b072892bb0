export const formatDate = (dateString) => {
  return dateString.split('-')[2] + '日';
};

export const formatAmount = (amount) => {
  return (amount / 10000).toFixed(2) + '万元';
};

export const formatMonth = (month) => {
  const monthMap = {
    '10': '十月',
    '11': '十一月',
    '12': '十二月'
  };
  return monthMap[month] || month;
};

export const formatFullDate = (dateString) => {
  const [year, month, day] = dateString.split('-');
  return `${year}年${formatMonth(month)}${day}日`;
}; 