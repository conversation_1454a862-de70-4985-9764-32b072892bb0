<template>
  <div class="app">
    <h1>2020年转账事件详细时间轴</h1>
    <Timeline />
  </div>
</template>

<script setup>
import Timeline from './components/Timeline.vue'
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  color: #2c3e50;
}

.app {
  max-width: 1600px;
  margin: 0 auto;
  padding: 30px 40px;
}

h1 {
  text-align: center;
  margin: 30px 0 50px;
  color: #2c3e50;
  font-size: 2.2em;
  font-weight: 600;
}

@media (max-width: 768px) {
  .app {
    padding: 20px;
  }
  
  h1 {
    font-size: 1.8em;
    margin: 20px 0 30px;
  }
}
</style>
