export interface TimelineEvent {
  id: string;
  date: Date;
  title: string;
  description: string;
  category: string;
  importance: number;
  amount?: number;
}

export interface Category {
  id: string;
  name: string;
  color: string;
}

export interface MonthSummary {
  date: string;
  label: string;
  totalAmount: number;
  transactionCount: number;
  categoryBreakdown: CategoryBreakdown[];
}

export interface CategoryBreakdown {
  id: string;
  name: string;
  color: string;
  amount: number;
  percentage: number;
} 