import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const distDir = path.resolve(__dirname, '../dist');

try {
    // 读取 index.html
    const htmlFile = path.join(distDir, 'index.html');
    let htmlContent = fs.readFileSync(htmlFile, 'utf-8');

    // 读取所有资源文件
    const assetsDir = path.join(distDir, 'assets');
    const files = fs.readdirSync(assetsDir);

    // 处理每个资源文件
    files.forEach(file => {
        const filePath = path.join(assetsDir, file);
        const content = fs.readFileSync(filePath, 'utf-8');

        if (file.endsWith('.js')) {
            // 内联 JavaScript
            htmlContent = htmlContent.replace(
                new RegExp(`<script[^>]*src="./assets/${file}"[^>]*></script>`),
                `<script type="module">${content}</script>`
            );
        } else if (file.endsWith('.css')) {
            // 内联 CSS
            htmlContent = htmlContent.replace(
                new RegExp(`<link[^>]*href="./assets/${file}"[^>]*>`),
                `<style>${content}</style>`
            );
        }
    });

    // 保存修改后的 HTML
    fs.writeFileSync(htmlFile, htmlContent);

    // 删除 assets 目录
    fs.rmSync(assetsDir, { recursive: true, force: true });

    console.log('构建完成！现在可以直接打开 dist/index.html 文件了。');
} catch (error) {
    console.error('构建过程中出错：', error);
} 