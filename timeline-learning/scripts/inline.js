import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const distDir = path.resolve(__dirname, '../dist');

async function inlineResources() {
    try {
        // 等待一下确保文件都已经生成
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 读取 index.html
        const htmlPath = path.join(distDir, 'index.html');
        let html = fs.readFileSync(htmlPath, 'utf-8');

        // 读取 assets 目录
        const assetsDir = path.join(distDir, 'assets');
        const files = fs.readdirSync(assetsDir);

        // 处理每个文件
        for (const file of files) {
            const filePath = path.join(assetsDir, file);
            const content = fs.readFileSync(filePath, 'utf-8');

            if (file.endsWith('.js')) {
                // 替换 JavaScript 文件引用
                const scriptRegex = new RegExp(`<script[^>]*src="[\./]*assets/${file}"[^>]*></script>`);
                html = html.replace(scriptRegex, `<script type="module">${content}</script>`);
            } else if (file.endsWith('.css')) {
                // 替换 CSS 文件引用
                const cssRegex = new RegExp(`<link[^>]*href="[\./]*assets/${file}"[^>]*>`);
                html = html.replace(cssRegex, `<style>${content}</style>`);
            }
        }

        // 写入新的 HTML 文件
        const standalonePath = path.join(distDir, 'standalone.html');
        fs.writeFileSync(standalonePath, html);

        console.log('构建完成！请使用 dist/standalone.html');
    } catch (error) {
        console.error('Error:', error);
    }
}

inlineResources(); 