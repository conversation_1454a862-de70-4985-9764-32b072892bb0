import { readFileSync } from 'fs';
import { resolve } from 'path';

export default function inlinePlugin() {
  return {
    name: 'vite:inline',
    transformIndexHtml: {
      enforce: 'post',
      transform(html, ctx) {
        // 只在构建时处理
        if (!ctx || !ctx.bundle) return html;

        // 内联 JavaScript
        html = html.replace(
          /<script type="module" crossorigin src="\.\/assets\/([^"]+)"><\/script>/g,
          (match, fileName) => {
            const file = readFileSync(resolve('dist/assets', fileName), 'utf-8');
            return `<script type="module">${file}</script>`;
          }
        );

        // 内联 CSS
        html = html.replace(
          /<link rel="stylesheet" crossorigin href="\.\/assets\/([^"]+)">/g,
          (match, fileName) => {
            const file = readFileSync(resolve('dist/assets', fileName), 'utf-8');
            return `<style>${file}</style>`;
          }
        );

        return html;
      },
    },
  };
} 