{"name": "timeline-learning", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build-standalone": "vite build && node scripts/inline.js"}, "dependencies": {"d3": "^7.9.0", "lodash-es": "^4.17.21", "vue": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "terser": "^5.16.0", "typescript": "^5.0.0", "vite": "^6.0.1"}}