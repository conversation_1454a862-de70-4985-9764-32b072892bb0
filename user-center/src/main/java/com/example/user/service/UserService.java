package com.example.user.service;

import com.example.user.dto.UserDTO;
import com.example.common.model.Result;

public interface UserService {
    Result<UserDTO> register(UserDTO userDTO);
    Result<String> login(String username, String password);
    Result<UserDTO> validateToken(String token);
    Result<UserDTO> findById(Long id);
    Result<UserDTO> update(Long id, UserDTO userDTO);
    Result<Void> delete(Long id);
} 