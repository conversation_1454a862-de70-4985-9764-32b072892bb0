package com.example.user.service.impl;

import com.example.common.model.Result;
import com.example.user.entity.Role;
import com.example.user.repository.RoleRepository;
import com.example.user.service.RoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final RoleRepository roleRepository;
    private static final String DEFAULT_ROLE_NAME = "USER";

    @Override
    @Transactional
    public Role getOrCreateDefaultRole() {
        return roleRepository.findByName(DEFAULT_ROLE_NAME)
            .orElseGet(() -> {
                Role defaultRole = new Role();
                defaultRole.setName(DEFAULT_ROLE_NAME);
                defaultRole.setDescription("Default user role");
                return roleRepository.save(defaultRole);
            });
    }

    @Override
    @Transactional(readOnly = true)
    public Result<List<Role>> findAll() {
        List<Role> roles = roleRepository.findAll();
        return Result.success(roles);
    }

    @Override
    @Transactional(readOnly = true)
    public Result<Role> findById(Long id) {
        return roleRepository.findById(id)
            .map(Result::success)
            .orElse(Result.error("ROLE_NOT_FOUND", "Role not found with id: " + id));
    }

    @Override
    @Transactional
    public Result<Role> create(Role role) {
        // 检查角色名是否已存在
        if (roleRepository.existsByName(role.getName())) {
            return Result.error("ROLE_EXISTS", "Role name already exists: " + role.getName());
        }

        Role savedRole = roleRepository.save(role);
        return Result.success(savedRole);
    }

    @Override
    @Transactional
    public Result<Role> update(Long id, Role role) {
        return roleRepository.findById(id)
            .<Result<Role>>map(existingRole -> {
                // 如果角色名变更了，检查新名称是否已存在
                if (!existingRole.getName().equals(role.getName()) && 
                    roleRepository.existsByName(role.getName())) {
                    return Result.error("ROLE_EXISTS", "Role name already exists: " + role.getName());
                }

                existingRole.setName(role.getName());
                existingRole.setDescription(role.getDescription());
                Role updatedRole = roleRepository.save(existingRole);
                return Result.success(updatedRole);
            })
            .orElse(Result.error("ROLE_NOT_FOUND", "Role not found with id: " + id));
    }

    @Override
    @Transactional
    public Result<Void> delete(Long id) {
        return roleRepository.findById(id)
            .<Result<Void>>map(role -> {
                // 检查角色是否还被用户使用
                if (!role.getUsers().isEmpty()) {
                    return Result.error("ROLE_IN_USE", 
                        "Cannot delete role as it is still assigned to users");
                }
                
                roleRepository.delete(role);
                return Result.success(null);
            })
            .orElse(Result.error("ROLE_NOT_FOUND", "Role not found with id: " + id));
    }
} 