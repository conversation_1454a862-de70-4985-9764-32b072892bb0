package com.example.user.service.impl;

import com.example.common.model.Result;
import com.example.user.constant.MessageConstants;
import com.example.user.dto.UserDTO;
import com.example.user.entity.User;
import com.example.user.repository.UserRepository;
import com.example.user.service.UserService;
import com.example.user.service.RoleService;
import com.example.user.util.JwtUtil;
import com.example.user.util.UserConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.HashSet;

@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final RoleService roleService;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;

    @Override
    @Transactional
    public Result<UserDTO> register(UserDTO userDTO) {
        try {
            // 检查用户名是否已存在
            if (userRepository.existsByUsername(userDTO.getUsername())) {
                return Result.error("USERNAME_EXISTS", MessageConstants.USERNAME_EXISTS);
            }

            // 使用 toUser() 方法转换，然后设置加密密码
            User user = UserConverter.toEntity(userDTO);
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
            user.setRoles(new HashSet<>(Collections.singleton(roleService.getOrCreateDefaultRole())));

            User savedUser = userRepository.save(user);
            return Result.success(UserConverter.toDTO(savedUser));
        } catch (Exception e) {
            return Result.error("REGISTRATION_FAILED", e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Result<String> login(String username, String password) {
        return userRepository.findByUsername(username)
            .filter(user -> passwordEncoder.matches(password, user.getPassword()))
            .map(user -> Result.success(jwtUtil.generateToken(user)))
            .orElse(Result.error("LOGIN_FAILED", MessageConstants.PASSWORD_INCORRECT));
    }

    @Override
    @Transactional(readOnly = true)
    public Result<UserDTO> validateToken(String token) {
        try {
            String username = jwtUtil.validateTokenAndGetUsername(token);
            return userRepository.findByUsername(username)
                .map(user -> Result.success(UserConverter.toDTO(user)))
                .orElse(Result.error("INVALID_TOKEN", "Token validation failed"));
        } catch (Exception e) {
            return Result.error("INVALID_TOKEN", e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Result<UserDTO> findById(Long id) {
        return userRepository.findById(id)
            .map(user -> Result.success(UserConverter.toDTO(user)))
            .orElse(Result.error("USER_NOT_FOUND", MessageConstants.USER_NOT_FOUND));
    }

    @Override
    @Transactional
    public Result<UserDTO> update(Long id, UserDTO userDTO) {
        return userRepository.findById(id)
            .<Result<UserDTO>>map(user -> {
                // 如果要更改用户名，检查新用户名是否已存在
                if (!user.getUsername().equals(userDTO.getUsername()) &&
                    userRepository.existsByUsername(userDTO.getUsername())) {
                    return Result.error("USERNAME_EXISTS", MessageConstants.USERNAME_EXISTS);
                }

                // 只更新允许更新的字段
                user.setUsername(userDTO.getUsername());
                user.setNickname(userDTO.getNickname());
                user.setEmail(userDTO.getEmail());
                if (userDTO.getPassword() != null) {
                    user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
                }
                if (userDTO.getAvatar() != null) {
                    user.setAvatar(userDTO.getAvatar());
                }

                User updatedUser = userRepository.save(user);
                return Result.success(UserConverter.toDTO(updatedUser));
            })
            .orElse(Result.error("USER_NOT_FOUND", MessageConstants.USER_NOT_FOUND));
    }

    @Override
    @Transactional
    public Result<Void> delete(Long id) {
        return userRepository.findById(id)
            .<Result<Void>>map(user -> {
                userRepository.delete(user);
                return Result.success(null);
            })
            .orElse(Result.error("USER_NOT_FOUND", MessageConstants.USER_NOT_FOUND));
    }
} 