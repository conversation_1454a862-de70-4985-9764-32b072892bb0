package com.example.user.exception;

import com.example.user.constant.ErrorConstants;
import com.example.user.constant.MessageConstants;
import lombok.Getter;

@Getter
public class UserException extends BaseException {
    public UserException(String errorCode, String message) {
        super(errorCode, message);
    }
    
    public static UserException userNotFound() {
        return new UserException(ErrorConstants.USER_NOT_FOUND, MessageConstants.USER_NOT_FOUND);
    }
    
    public static UserException userExists() {
        return new UserException(ErrorConstants.USER_EXISTS, MessageConstants.USER_EXISTS);
    }
    
    public static UserException invalidPassword() {
        return new UserException(ErrorConstants.INVALID_PASSWORD, MessageConstants.INVALID_PASSWORD);
    }
    
    public static UserException invalidToken() {
        return new UserException(ErrorConstants.INVALID_TOKEN, MessageConstants.INVALID_TOKEN);
    }
} 