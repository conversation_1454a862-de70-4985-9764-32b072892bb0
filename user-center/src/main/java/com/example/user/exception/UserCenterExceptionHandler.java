package com.example.user.exception;

import com.example.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import java.util.HashMap;
import java.util.Map;

@ControllerAdvice("com.example.user")  // 只处理用户中心模块的异常
@Slf4j
public class UserCenterExceptionHandler {
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Result<?>> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("参数验证失败: ", e);
        String message = e.getBindingResult().getFieldErrors().stream()
            .map(FieldError::getDefaultMessage)
            .findFirst()
            .orElse("参数验证失败");
        return ResponseEntity.badRequest()
            .body(Result.error("INVALID_PARAMETER", message));
    }
    
    @ExceptionHandler({UserNotFoundException.class, UsernameExistsException.class})
    public ResponseEntity<Result<?>> handleUserException(BaseException e) {
        log.error("用户操作异常: ", e);
        return ResponseEntity.status(e instanceof UserNotFoundException ? 
            HttpStatus.NOT_FOUND : HttpStatus.CONFLICT)
            .body(Result.error(e.getCode(), e.getMessage()));
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Result<?>> handleException(Exception e) {
        log.error("系统异常: ", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(Result.error("SYSTEM_ERROR", "系统错误,请稍后重试"));
    }
} 