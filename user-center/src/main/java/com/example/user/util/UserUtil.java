package com.example.user.util;

import com.example.user.dto.UserDTO;
import com.example.user.entity.User;
import org.springframework.security.crypto.password.PasswordEncoder;

public final class UserUtil {
    private UserUtil() {}
    
    public static User createNewUser(UserDTO userDTO, PasswordEncoder passwordEncoder) {
        User user = new User();
        user.setUsername(userDTO.getUsername());
        user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        user.setNickname(userDTO.getNickname());
        user.setEmail(userDTO.getEmail());
        user.setAvatar(userDTO.getAvatar());
        return user;
    }
    
    public static void updateUser(User user, UserDTO userDTO, PasswordEncoder passwordEncoder) {
        if (userDTO.getNickname() != null) {
            user.setNickname(userDTO.getNickname());
        }
        if (userDTO.getEmail() != null) {
            user.setEmail(userDTO.getEmail());
        }
        if (userDTO.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        }
        if (userDTO.getAvatar() != null) {
            user.setAvatar(userDTO.getAvatar());
        }
    }
} 