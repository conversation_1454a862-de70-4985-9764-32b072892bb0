<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.et.security.mapper.UserMapper">
  <resultMap id="BaseResultMap" type="com.et.security.entity.User">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 14 16:21:03 CST 2024.
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="locked" jdbcType="BIT" property="locked" />
    <collection property="roles" ofType="com.et.security.entity.Role">
      <id column="id" jdbcType="INTEGER" property="id" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="nameZh" jdbcType="VARCHAR" property="nameZh" />
    </collection>
  </resultMap>
  <insert id="insert" parameterType="com.et.security.entity.User">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 14 16:21:03 CST 2024.
    -->
    insert into user (id, username, password, 
      enabled, locked)
    values (#{id,jdbcType=INTEGER}, #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, 
      #{enabled,jdbcType=BIT}, #{locked,jdbcType=BIT})
  </insert>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 14 16:21:03 CST 2024.
    -->
    select id, username, password, enabled, locked
    from user
  </select>
  <select id="loadUserByUsername" resultMap="BaseResultMap">
    select * from user  u left join user_role ur  on u.id =ur.uid LEFT JOIN `role`  r on ur.rid =r.id  where username =#{username}
  </select>
</mapper>