package com.learning.designpatterns.creational.factory;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 工厂方法模式测试类
 * 
 * 测试各种工厂方法模式实现的正确性
 * 
 * <AUTHOR>
 */
@DisplayName("工厂方法模式测试")
class FactoryMethodTest {
    
    @Test
    @DisplayName("文件日志记录器工厂测试")
    void testFileLoggerFactory() {
        LoggerFactory factory = new FileLoggerFactory();
        Logger logger = factory.createLogger();
        
        assertNotNull(logger);
        assertInstanceOf(FileLogger.class, logger);
        assertEquals("FILE", logger.getType());
        
        // 测试日志记录功能
        assertDoesNotThrow(() -> logger.log("测试文件日志"));
    }
    
    @Test
    @DisplayName("控制台日志记录器工厂测试")
    void testConsoleLoggerFactory() {
        LoggerFactory factory = new ConsoleLoggerFactory();
        Logger logger = factory.createLogger();
        
        assertNotNull(logger);
        assertInstanceOf(ConsoleLogger.class, logger);
        assertEquals("CONSOLE", logger.getType());
        
        // 测试日志记录功能
        assertDoesNotThrow(() -> logger.log("测试控制台日志"));
    }
    
    @Test
    @DisplayName("数据库日志记录器工厂测试")
    void testDatabaseLoggerFactory() {
        LoggerFactory factory = new DatabaseLoggerFactory();
        Logger logger = factory.createLogger();
        
        assertNotNull(logger);
        assertInstanceOf(DatabaseLogger.class, logger);
        assertEquals("DATABASE", logger.getType());
        
        // 测试日志记录功能
        assertDoesNotThrow(() -> logger.log("测试数据库日志"));
    }
    
    @Test
    @DisplayName("支付宝支付工厂测试")
    void testAlipayFactory() {
        PaymentFactory factory = new AlipayFactory();
        Payment payment = factory.createPayment();
        
        assertNotNull(payment);
        assertInstanceOf(AlipayPayment.class, payment);
        assertEquals("支付宝", payment.getPaymentMethod());
        assertEquals(0.6, payment.getFeeRate());
    }
    
    @Test
    @DisplayName("微信支付工厂测试")
    void testWechatPayFactory() {
        PaymentFactory factory = new WechatPayFactory();
        Payment payment = factory.createPayment();
        
        assertNotNull(payment);
        assertInstanceOf(WechatPayment.class, payment);
        assertEquals("微信支付", payment.getPaymentMethod());
        assertEquals(0.6, payment.getFeeRate());
    }
    
    @Test
    @DisplayName("银行卡支付工厂测试")
    void testBankCardFactory() {
        PaymentFactory factory = new BankCardFactory();
        Payment payment = factory.createPayment();
        
        assertNotNull(payment);
        assertInstanceOf(BankCardPayment.class, payment);
        assertEquals("银行卡", payment.getPaymentMethod());
        assertEquals(1.0, payment.getFeeRate());
    }
    
    @Test
    @DisplayName("支付流程测试")
    void testPaymentProcess() {
        PaymentFactory factory = new AlipayFactory();
        
        // 测试正常支付金额
        assertTrue(factory.processPayment(100.0));
        
        // 测试无效支付金额
        assertFalse(factory.processPayment(0));
        assertFalse(factory.processPayment(-10));
    }
    
    @Test
    @DisplayName("日志记录流程测试")
    void testLoggerProcess() {
        LoggerFactory factory = new FileLoggerFactory();
        
        // 测试模板方法
        assertDoesNotThrow(() -> factory.logMessage("测试消息"));
    }
}
