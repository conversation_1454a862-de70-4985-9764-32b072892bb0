package com.learning.designpatterns.creational.singleton;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 单例模式测试类
 * 
 * 测试各种单例模式实现的正确性和线程安全性
 * 
 * <AUTHOR>
 */
@DisplayName("单例模式测试")
class SingletonTest {
    
    @Test
    @DisplayName("饿汉式单例测试")
    void testEagerSingleton() {
        EagerSingleton instance1 = EagerSingleton.getInstance();
        EagerSingleton instance2 = EagerSingleton.getInstance();
        
        assertNotNull(instance1);
        assertNotNull(instance2);
        assertSame(instance1, instance2, "两个实例应该是同一个对象");
    }
    
    @Test
    @DisplayName("懒汉式单例测试")
    void testLazySingleton() {
        LazySingleton instance1 = LazySingleton.getInstance();
        LazySingleton instance2 = LazySingleton.getInstance();
        
        assertNotNull(instance1);
        assertNotNull(instance2);
        assertSame(instance1, instance2, "两个实例应该是同一个对象");
    }
    
    @Test
    @DisplayName("双重检查锁定单例测试")
    void testDoubleCheckedSingleton() {
        DoubleCheckedSingleton instance1 = DoubleCheckedSingleton.getInstance();
        DoubleCheckedSingleton instance2 = DoubleCheckedSingleton.getInstance();
        
        assertNotNull(instance1);
        assertNotNull(instance2);
        assertSame(instance1, instance2, "两个实例应该是同一个对象");
    }
    
    @Test
    @DisplayName("枚举单例测试")
    void testEnumSingleton() {
        EnumSingleton instance1 = EnumSingleton.INSTANCE;
        EnumSingleton instance2 = EnumSingleton.INSTANCE;
        
        assertNotNull(instance1);
        assertNotNull(instance2);
        assertSame(instance1, instance2, "两个实例应该是同一个对象");
    }
    
    @Test
    @DisplayName("静态内部类单例测试")
    void testStaticInnerClassSingleton() {
        StaticInnerClassSingleton instance1 = StaticInnerClassSingleton.getInstance();
        StaticInnerClassSingleton instance2 = StaticInnerClassSingleton.getInstance();
        
        assertNotNull(instance1);
        assertNotNull(instance2);
        assertSame(instance1, instance2, "两个实例应该是同一个对象");
    }
    
    @Test
    @DisplayName("懒汉式单例多线程安全测试")
    void testLazySingletonThreadSafety() throws InterruptedException {
        testSingletonThreadSafety(() -> LazySingleton.getInstance());
    }
    
    @Test
    @DisplayName("双重检查锁定单例多线程安全测试")
    void testDoubleCheckedSingletonThreadSafety() throws InterruptedException {
        testSingletonThreadSafety(() -> DoubleCheckedSingleton.getInstance());
    }
    
    @Test
    @DisplayName("静态内部类单例多线程安全测试")
    void testStaticInnerClassSingletonThreadSafety() throws InterruptedException {
        testSingletonThreadSafety(() -> StaticInnerClassSingleton.getInstance());
    }
    
    /**
     * 通用的单例线程安全测试方法
     */
    private void testSingletonThreadSafety(SingletonSupplier supplier) throws InterruptedException {
        final int threadCount = 100;
        final CountDownLatch latch = new CountDownLatch(threadCount);
        final Set<Object> instances = ConcurrentHashMap.newKeySet();
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    Object instance = supplier.get();
                    instances.add(instance);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        assertTrue(latch.await(10, TimeUnit.SECONDS), "所有线程应该在10秒内完成");
        assertEquals(1, instances.size(), "应该只有一个实例被创建");
        
        executor.shutdown();
    }
    
    /**
     * 单例提供者函数式接口
     */
    @FunctionalInterface
    private interface SingletonSupplier {
        Object get();
    }
}
