package com.learning.designpatterns.behavioral.observer;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;

/**
 * 观察者模式测试类
 * 
 * 测试观察者模式的各种实现和功能
 * 
 * <AUTHOR>
 */
@DisplayName("观察者模式测试")
class ObserverTest {
    
    private StockPrice stockPrice;
    private Investor investor1;
    private Investor investor2;
    private StockAnalyst analyst;
    
    private NewsAgency newsAgency;
    private NewsSubscriber subscriber1;
    private NewsSubscriber subscriber2;
    
    @BeforeEach
    void setUp() {
        // 初始化股票相关对象
        stockPrice = new StockPrice("AAPL", 150.0);
        investor1 = new Investor("张三", 145.0, 160.0);
        investor2 = new Investor("李四", 140.0, 165.0);
        analyst = new StockAnalyst("王分析师");
        
        // 初始化新闻相关对象
        newsAgency = new NewsAgency("科技日报");
        subscriber1 = new NewsSubscriber("用户A", Arrays.asList("技术", "科技"));
        subscriber2 = new NewsSubscriber("用户B");
    }
    
    @Test
    @DisplayName("股票价格观察者注册和移除测试")
    void testStockPriceObserverRegistration() {
        // 测试添加观察者
        stockPrice.addObserver(investor1);
        stockPrice.addObserver(investor2);
        stockPrice.addObserver(analyst);
        
        assertEquals(3, stockPrice.getObservers().size());
        
        // 测试移除观察者
        stockPrice.removeObserver(investor1);
        assertEquals(2, stockPrice.getObservers().size());
        assertFalse(stockPrice.getObservers().contains(investor1));
    }
    
    @Test
    @DisplayName("股票价格变化通知测试")
    void testStockPriceChangeNotification() {
        stockPrice.addObserver(investor1);
        stockPrice.addObserver(analyst);
        
        // 记录初始价格
        double initialPrice = stockPrice.getPrice();
        assertEquals(150.0, initialPrice);
        
        // 改变价格
        stockPrice.setPrice(155.0);
        assertEquals(155.0, stockPrice.getPrice());
        
        // 验证分析师记录了价格变化
        assertEquals(155.0, analyst.getLastPrice());
    }
    
    @Test
    @DisplayName("投资者决策逻辑测试")
    void testInvestorDecisionLogic() {
        stockPrice.addObserver(investor1);
        
        // 测试买入阈值
        stockPrice.setPrice(145.0); // 等于买入阈值
        
        // 测试卖出阈值
        stockPrice.setPrice(160.0); // 等于卖出阈值
        
        // 测试观望区间
        stockPrice.setPrice(152.0); // 在买入和卖出阈值之间
        
        // 验证投资者的阈值设置
        assertEquals(145.0, investor1.getBuyThreshold());
        assertEquals(160.0, investor1.getSellThreshold());
    }
    
    @Test
    @DisplayName("新闻机构观察者注册和移除测试")
    void testNewsAgencyObserverRegistration() {
        // 测试添加订阅者
        newsAgency.addObserver(subscriber1);
        newsAgency.addObserver(subscriber2);
        
        assertEquals(2, newsAgency.getSubscriberCount());
        
        // 测试移除订阅者
        newsAgency.removeObserver(subscriber1);
        assertEquals(1, newsAgency.getSubscriberCount());
    }
    
    @Test
    @DisplayName("新闻发布和接收测试")
    void testNewsPublishingAndReceiving() {
        newsAgency.addObserver(subscriber1);
        newsAgency.addObserver(subscriber2);
        
        // 发布新闻
        String testNews = "人工智能技术取得重大突破";
        newsAgency.publishNews(testNews);
        
        // 验证新闻发布
        assertEquals(testNews, newsAgency.getLatestNews());
        assertNotNull(newsAgency.getPublishTime());
        assertEquals(1, newsAgency.getNewsHistory().size());
        
        // 验证订阅者接收到新闻
        // subscriber1 有技术偏好，应该接收这条新闻
        assertEquals(1, subscriber1.getReceivedNewsCount());
        
        // subscriber2 没有偏好设置，应该接收所有新闻
        assertEquals(1, subscriber2.getReceivedNewsCount());
    }
    
    @Test
    @DisplayName("订阅者偏好过滤测试")
    void testSubscriberPreferenceFiltering() {
        // 设置有特定偏好的订阅者
        NewsSubscriber techSubscriber = new NewsSubscriber("技术爱好者", Arrays.asList("技术", "科技", "AI"));
        newsAgency.addObserver(techSubscriber);
        
        // 发布相关新闻
        newsAgency.publishNews("最新AI技术突破");
        assertEquals(1, techSubscriber.getReceivedNewsCount());
        
        // 发布不相关新闻
        newsAgency.publishNews("今日天气晴朗");
        // 应该还是1条，因为天气新闻不符合技术偏好
        assertEquals(1, techSubscriber.getReceivedNewsCount());
    }
    
    @Test
    @DisplayName("订阅者偏好管理测试")
    void testSubscriberPreferenceManagement() {
        // 测试添加偏好
        subscriber2.addPreference("科技");
        assertTrue(subscriber2.getPreferences().contains("科技"));
        
        // 测试移除偏好
        subscriber2.removePreference("科技");
        assertFalse(subscriber2.getPreferences().contains("科技"));
    }
    
    @Test
    @DisplayName("多个股票价格变化测试")
    void testMultipleStockPriceChanges() {
        stockPrice.addObserver(analyst);
        
        // 连续多次价格变化
        stockPrice.setPrice(155.0);
        stockPrice.setPrice(148.0);
        stockPrice.setPrice(162.0);
        
        // 验证最终价格
        assertEquals(162.0, stockPrice.getPrice());
        assertEquals(162.0, analyst.getLastPrice());
    }
    
    @Test
    @DisplayName("新闻历史记录测试")
    void testNewsHistory() {
        newsAgency.publishNews("新闻1");
        newsAgency.publishNews("新闻2");
        newsAgency.publishNews("新闻3");
        
        assertEquals(3, newsAgency.getNewsHistory().size());
        assertEquals("新闻3", newsAgency.getLatestNews());
    }
}
