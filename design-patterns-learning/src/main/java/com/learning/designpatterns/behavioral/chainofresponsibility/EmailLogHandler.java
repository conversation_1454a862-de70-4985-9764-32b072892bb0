package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 邮件日志处理器
 * 
 * 处理ERROR级别及以上的日志，发送邮件通知
 * 
 * <AUTHOR>
 */
@Slf4j
public class EmailLogHandler extends LogHandler {
    
    @Override
    protected boolean canHandle(LogMessage message) {
        // 只处理ERROR级别及以上的日志
        return message.getLevel().getPriority() >= LogLevel.ERROR.getPriority();
    }
    
    @Override
    protected void writeLog(LogMessage message) {
        String recipients = getRecipients(message.getLevel());
        log.info("📧 发送邮件通知到 {}: {}", recipients, message.getFormattedMessage());
        
        // 模拟邮件发送操作
        try {
            Thread.sleep(300);
            log.info("✅ 邮件发送成功");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("邮件发送被中断");
        }
    }
    
    private String getRecipients(LogLevel level) {
        return switch (level) {
            case ERROR -> "<EMAIL>";
            case FATAL -> "<EMAIL>, <EMAIL>, <EMAIL>";
            default -> "<EMAIL>";
        };
    }
}
