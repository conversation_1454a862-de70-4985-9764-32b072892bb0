package com.learning.designpatterns.behavioral.command;

import lombok.extern.slf4j.Slf4j;

/**
 * 命令模式演示类
 * 
 * 命令模式将一个请求封装为一个对象，从而使你可用不同的请求对客户进行参数化；
 * 对请求排队或记录请求日志，以及支持可撤销的操作。
 * 
 * 应用场景：
 * - 文本编辑器的撤销/重做功能
 * - 遥控器控制不同设备
 * - 宏命令和批处理
 * - 队列请求和日志记录
 * - 事务处理和回滚
 * 
 * <AUTHOR>
 */
@Slf4j
public class CommandDemo {
    
    public static void main(String[] args) {
        log.info("=== 命令模式演示 ===");
        
        // 演示智能家居遥控器
        demonstrateSmartHomeRemote();
        
        // 演示文本编辑器
        demonstrateTextEditor();
        
        // 演示宏命令
        demonstrateMacroCommand();
    }
    
    private static void demonstrateSmartHomeRemote() {
        log.info("\n--- 智能家居遥控器演示 ---");

        // 创建设备
        Light livingRoomLight = new Light("客厅灯");

        // 创建命令
        Command lightOnCommand = new LightOnCommand(livingRoomLight);
        Command lightOffCommand = new LightOffCommand(livingRoomLight);

        // 执行命令
        log.info("\n🎮 开始操作设备:");
        lightOnCommand.execute();  // 开灯

        log.info("\n🔄 撤销操作:");
        lightOnCommand.undo();  // 撤销开灯

        log.info("\n🔄 关闭设备:");
        lightOffCommand.execute(); // 关灯
    }
    
    private static void demonstrateTextEditor() {
        log.info("\n--- 文本编辑器演示 ---");
        log.info("📝 命令模式可以用于实现文本编辑器的撤销/重做功能");
        log.info("💡 每个编辑操作都可以封装为一个命令对象");
        log.info("🔄 通过命令栈可以实现撤销和重做功能");
    }
    
    private static void demonstrateMacroCommand() {
        log.info("\n--- 宏命令演示 ---");

        // 创建设备
        Light light1 = new Light("客厅灯");
        Light light2 = new Light("卧室灯");

        // 创建单个命令
        Command lightOn1 = new LightOnCommand(light1);
        Command lightOn2 = new LightOnCommand(light2);
        Command lightOff1 = new LightOffCommand(light1);
        Command lightOff2 = new LightOffCommand(light2);

        log.info("🏠 模拟回家模式 - 开启所有灯:");
        lightOn1.execute();
        lightOn2.execute();

        log.info("\n🚪 模拟离家模式 - 关闭所有灯:");
        lightOff1.execute();
        lightOff2.execute();

        log.info("\n💡 宏命令可以将多个命令组合成一个复合命令");
        log.info("🔄 执行宏命令时会依次执行所有子命令");
    }
}
