package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 风景路径策略
 * 
 * 优先选择风景优美的路径，适合旅游和观光
 * 
 * <AUTHOR>
 */
@Slf4j
public class ScenicRouteStrategy implements RouteStrategy {
    
    @Override
    public RouteInfo planRoute(String start, String destination) {
        log.info("🌄 计算风景路径...");
        
        // 模拟路径计算
        simulateCalculation();
        
        // 风景路径距离和时间都较长，费用中等，但风景优美
        double distance = calculateDistance(start, destination) * 1.4; // 距离较长
        double duration = calculateBaseDuration(start, destination) * 1.6; // 时间较长
        double cost = calculateBaseCost(start, destination) * 1.1; // 费用中等
        
        String routeName = String.format("%s -> %s (风景路线)", start, destination);
        String description = "途经名胜古迹和自然风光，适合旅游观光，时间较长";
        
        log.info("🏞️ 风景路径规划完成");
        
        return new RouteInfo(routeName, distance, duration, cost, description);
    }
    
    @Override
    public String getStrategyName() {
        return "风景路径";
    }
    
    /**
     * 模拟路径计算过程
     */
    private void simulateCalculation() {
        try {
            log.info("🗺️ 搜索旅游景点...");
            Thread.sleep(300);
            
            log.info("🏔️ 寻找风景道路...");
            Thread.sleep(250);
            
            log.info("📸 标记拍照点...");
            Thread.sleep(200);
            
            log.info("🏛️ 添加历史古迹...");
            Thread.sleep(280);
            
            log.info("🌸 规划最佳观景时间...");
            Thread.sleep(220);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("路径计算被中断", e);
        }
    }
    
    /**
     * 计算基础距离
     */
    private double calculateDistance(String start, String destination) {
        // 模拟距离计算
        return 1200 + Math.random() * 100; // 1200-1300km
    }
    
    /**
     * 计算基础时间
     */
    private double calculateBaseDuration(String start, String destination) {
        // 模拟时间计算
        return 12 + Math.random() * 2; // 12-14小时
    }
    
    /**
     * 计算基础费用
     */
    private double calculateBaseCost(String start, String destination) {
        // 模拟费用计算
        return 800 + Math.random() * 200; // 800-1000元
    }
}
