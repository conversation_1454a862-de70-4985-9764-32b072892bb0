package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;

/**
 * 电子产品类
 * 
 * 具体的购物车商品元素
 * 
 * <AUTHOR>
 */
@Getter
public class Electronics implements CartItem {
    
    private final String name;
    private final double price;
    private final String brand;
    private final int warrantyMonths;
    
    public Electronics(String name, double price, String brand, int warrantyMonths) {
        this.name = name;
        this.price = price;
        this.brand = brand;
        this.warrantyMonths = warrantyMonths;
    }
    
    @Override
    public void accept(CartItemVisitor visitor) {
        visitor.visit(this);
    }
    
    @Override
    public String toString() {
        return String.format("电子产品[%s - %s, ¥%.2f, 保修%d个月]", name, brand, price, warrantyMonths);
    }
}
