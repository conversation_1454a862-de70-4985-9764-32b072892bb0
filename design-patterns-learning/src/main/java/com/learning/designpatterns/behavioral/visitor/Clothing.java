package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;

/**
 * 服装类
 * 
 * 具体的购物车商品元素
 * 
 * <AUTHOR>
 */
@Getter
public class Clothing implements CartItem {
    
    private final String name;
    private final double price;
    private final String brand;
    private final String size;
    private final String color;
    
    public Clothing(String name, double price, String brand, String size, String color) {
        this.name = name;
        this.price = price;
        this.brand = brand;
        this.size = size;
        this.color = color;
    }
    
    @Override
    public void accept(CartItemVisitor visitor) {
        visitor.visit(this);
    }
    
    @Override
    public String toString() {
        return String.format("服装[%s - %s, ¥%.2f, %s码, %s]", name, brand, price, size, color);
    }
}
