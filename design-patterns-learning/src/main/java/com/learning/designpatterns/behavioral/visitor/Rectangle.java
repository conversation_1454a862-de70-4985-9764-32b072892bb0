package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;

/**
 * 矩形类
 * 
 * <AUTHOR>
 */
@Getter
public class Rectangle implements Shape {
    
    private final double width;
    private final double height;
    
    public Rectangle(double width, double height) {
        this.width = width;
        this.height = height;
    }
    
    @Override
    public void accept(ShapeVisitor visitor) {
        visitor.visit(this);
    }
}
