package com.learning.designpatterns.behavioral.state;

import lombok.extern.slf4j.Slf4j;

/**
 * 无硬币状态
 * 
 * 售货机等待用户投币的状态
 * 
 * <AUTHOR>
 */
@Slf4j
public class NoCoinState implements VendingMachineState {
    
    private final VendingMachine vendingMachine;
    
    public NoCoinState(VendingMachine vendingMachine) {
        this.vendingMachine = vendingMachine;
    }
    
    @Override
    public void insertCoin() {
        log.info("💰 投币成功，请选择商品");
        vendingMachine.setState(vendingMachine.getHasCoinState());
    }
    
    @Override
    public void ejectCoin() {
        log.warn("❌ 没有投币，无法退币");
    }
    
    @Override
    public void selectProduct() {
        log.warn("❌ 请先投币");
    }
    
    @Override
    public void dispenseProduct() {
        log.warn("❌ 请先投币并选择商品");
    }
    
    @Override
    public String getStateName() {
        return "等待投币";
    }
}
