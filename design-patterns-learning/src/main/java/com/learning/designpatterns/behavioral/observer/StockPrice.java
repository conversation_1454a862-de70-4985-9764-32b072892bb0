package com.learning.designpatterns.behavioral.observer;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * 股票价格主题类
 * 
 * 实现了Subject接口，当股票价格发生变化时，
 * 会自动通知所有关注此股票的观察者
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class StockPrice implements Subject {
    
    /**
     * 股票代码
     */
    private final String symbol;
    
    /**
     * 当前价格
     */
    private double price;
    
    /**
     * 观察者列表
     */
    private final List<Observer> observers;
    
    /**
     * 构造函数
     * 
     * @param symbol 股票代码
     * @param price 初始价格
     */
    public StockPrice(String symbol, double price) {
        this.symbol = symbol;
        this.price = price;
        this.observers = new ArrayList<>();
        log.info("创建股票 {} ，初始价格: ${}", symbol, price);
    }
    
    /**
     * 设置股票价格
     * 
     * 当价格发生变化时，自动通知所有观察者
     * 
     * @param newPrice 新价格
     */
    public void setPrice(double newPrice) {
        if (this.price != newPrice) {
            double oldPrice = this.price;
            this.price = newPrice;
            
            log.info("股票 {} 价格变化: ${} -> ${}", symbol, oldPrice, newPrice);
            
            // 通知所有观察者
            notifyObservers();
        }
    }
    
    @Override
    public void addObserver(Observer observer) {
        if (!observers.contains(observer)) {
            observers.add(observer);
            log.info("新增观察者关注股票 {}", symbol);
        }
    }
    
    @Override
    public void removeObserver(Observer observer) {
        if (observers.remove(observer)) {
            log.info("观察者取消关注股票 {}", symbol);
        }
    }
    
    @Override
    public void notifyObservers() {
        log.info("通知 {} 个观察者股票 {} 的价格变化", observers.size(), symbol);
        
        for (Observer observer : observers) {
            try {
                observer.update(this);
            } catch (Exception e) {
                log.error("通知观察者时发生错误", e);
            }
        }
    }
    
    /**
     * 获取价格变化百分比
     * 
     * @param oldPrice 旧价格
     * @return 变化百分比
     */
    public double getPriceChangePercentage(double oldPrice) {
        if (oldPrice == 0) {
            return 0;
        }
        return ((price - oldPrice) / oldPrice) * 100;
    }
    
    @Override
    public String toString() {
        return String.format("StockPrice{symbol='%s', price=%.2f}", symbol, price);
    }
}
