package com.learning.designpatterns.behavioral.state;

import lombok.extern.slf4j.Slf4j;

/**
 * 售罄状态
 * 
 * 商品库存为0的状态
 * 
 * <AUTHOR>
 */
@Slf4j
public class SoldOutState implements VendingMachineState {
    
    private final VendingMachine vendingMachine;
    
    public SoldOutState(VendingMachine vendingMachine) {
        this.vendingMachine = vendingMachine;
    }
    
    @Override
    public void insertCoin() {
        log.warn("❌ 商品售罄，暂停服务");
        log.info("💰 自动退币");
    }
    
    @Override
    public void ejectCoin() {
        log.warn("❌ 没有投币，无法退币");
    }
    
    @Override
    public void selectProduct() {
        log.warn("❌ 商品售罄，无法选择商品");
    }
    
    @Override
    public void dispenseProduct() {
        log.warn("❌ 商品售罄，无法出货");
    }
    
    @Override
    public String getStateName() {
        return "商品售罄";
    }
}
