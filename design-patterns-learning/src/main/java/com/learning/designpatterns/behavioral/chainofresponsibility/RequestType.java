package com.learning.designpatterns.behavioral.chainofresponsibility;

/**
 * 请求类型枚举
 * 
 * <AUTHOR>
 */
public enum RequestType {
    PURCHASE("采购"),
    PROJECT("项目"),
    EXPENSE("报销"),
    LEAVE("请假");
    
    private final String description;
    
    RequestType(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return description;
    }
}
