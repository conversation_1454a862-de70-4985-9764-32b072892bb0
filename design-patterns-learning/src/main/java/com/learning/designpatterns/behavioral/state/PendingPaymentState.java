package com.learning.designpatterns.behavioral.state;

import lombok.extern.slf4j.Slf4j;

/**
 * 待支付状态
 * 
 * <AUTHOR>
 */
@Slf4j
public class PendingPaymentState implements OrderState {
    
    private final Order order;
    
    public PendingPaymentState(Order order) {
        this.order = order;
    }
    
    @Override
    public void pay() {
        log.info("💳 支付成功，订单状态已更新");
        // 简化实现，不切换到其他状态
    }
    
    @Override
    public void ship() {
        log.warn("❌ 订单未支付，无法发货");
    }
    
    @Override
    public void deliver() {
        log.warn("❌ 订单未支付，无法送达");
    }
    
    @Override
    public void complete() {
        log.warn("❌ 订单未支付，无法完成");
    }
    
    @Override
    public void cancel() {
        log.info("🚫 订单已取消");
        // 简化实现，不切换到其他状态
    }
    
    @Override
    public String getStateName() {
        return "待支付";
    }
}
