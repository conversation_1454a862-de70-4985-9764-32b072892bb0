package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 咖啡类
 * 
 * 实现咖啡的具体制作步骤
 * 
 * <AUTHOR>
 */
@Slf4j
public class Coffee extends Beverage {
    
    @Override
    protected void brew() {
        log.info("☕ 用沸水冲泡咖啡粉");
        try {
            Thread.sleep(800); // 模拟冲泡时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("☕ 咖啡冲泡完成，香气四溢");
    }
    
    @Override
    protected void addCondiments() {
        log.info("🥛 添加牛奶和糖");
        log.info("🍯 调整甜度，增加口感层次");
    }
    
    @Override
    protected String getBeverageName() {
        return "咖啡";
    }
}
