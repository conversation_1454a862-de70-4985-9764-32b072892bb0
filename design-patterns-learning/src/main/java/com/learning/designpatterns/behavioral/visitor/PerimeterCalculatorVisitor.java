package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 周长计算访问者
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class PerimeterCalculatorVisitor implements ShapeVisitor {
    
    private double totalPerimeter = 0.0;
    private int shapeCount = 0;
    
    @Override
    public void visit(Circle circle) {
        double perimeter = 2 * Math.PI * circle.getRadius();
        log.info("🔵 圆形 (半径: {}) - 周长: {:.2f}", circle.getRadius(), perimeter);
        totalPerimeter += perimeter;
        shapeCount++;
    }
    
    @Override
    public void visit(Rectangle rectangle) {
        double perimeter = 2 * (rectangle.getWidth() + rectangle.getHeight());
        log.info("🔲 矩形 ({}x{}) - 周长: {:.2f}", rectangle.getWidth(), rectangle.getHeight(), perimeter);
        totalPerimeter += perimeter;
        shapeCount++;
    }
    
    @Override
    public void visit(Triangle triangle) {
        double perimeter = triangle.getA() + triangle.getB() + triangle.getC();
        log.info("🔺 三角形 ({}, {}, {}) - 周长: {:.2f}", 
                triangle.getA(), triangle.getB(), triangle.getC(), perimeter);
        totalPerimeter += perimeter;
        shapeCount++;
    }
    
    public double getAveragePerimeter() {
        return shapeCount > 0 ? totalPerimeter / shapeCount : 0.0;
    }
}
