package com.learning.designpatterns.behavioral.visitor;

import lombok.extern.slf4j.Slf4j;

/**
 * HTML导出访问者
 * 
 * <AUTHOR>
 */
@Slf4j
public class HTMLExportVisitor implements DocumentElementVisitor {
    
    @Override
    public void visit(Paragraph paragraph) {
        log.info("HTML: <p>{}</p>", paragraph.getContent());
    }
    
    @Override
    public void visit(Heading heading) {
        log.info("HTML: <h{}>{}</h{}>", heading.getLevel(), heading.getText(), heading.getLevel());
    }
    
    @Override
    public void visit(Image image) {
        log.info("HTML: <img src=\"{}\" alt=\"{}\" />", image.getSrc(), image.getAlt());
    }
    
    @Override
    public void visit(Table table) {
        log.info("HTML: <table>...</table> ({}行{}列)", table.getRows().size(), table.getHeaders().size());
    }
}
