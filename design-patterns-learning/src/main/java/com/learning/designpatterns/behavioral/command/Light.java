package com.learning.designpatterns.behavioral.command;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 灯光设备类
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class Light {
    
    private final String location;
    private boolean isOn = false;
    
    public Light(String location) {
        this.location = location;
    }
    
    public void on() {
        isOn = true;
        log.info("💡 {} 已开启", location);
    }
    
    public void off() {
        isOn = false;
        log.info("🔌 {} 已关闭", location);
    }
}
