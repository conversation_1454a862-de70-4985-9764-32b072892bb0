package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;

/**
 * 归并排序策略
 * 
 * 实现归并排序算法
 * 时间复杂度：O(n log n)，空间复杂度：O(n)
 * 
 * <AUTHOR>
 */
@Slf4j
public class MergeSortStrategy implements SortStrategy {
    
    private int comparisons = 0;
    private int merges = 0;
    
    @Override
    public void sort(int[] data) {
        log.info("🔀 开始归并排序");
        log.info("原始数据: {}", Arrays.toString(data));
        
        long startTime = System.nanoTime();
        comparisons = 0;
        merges = 0;
        
        mergeSort(data, 0, data.length - 1);
        
        long endTime = System.nanoTime();
        double duration = (endTime - startTime) / 1_000_000.0; // 转换为毫秒
        
        log.info("✅ 归并排序完成");
        log.info("排序结果: {}", Arrays.toString(data));
        log.info("📊 统计信息 - 比较次数: {}, 合并次数: {}, 耗时: {:.2f}ms", 
                comparisons, merges, duration);
    }
    
    /**
     * 归并排序递归实现
     * 
     * @param data 数组
     * @param left 左边界
     * @param right 右边界
     */
    private void mergeSort(int[] data, int left, int right) {
        if (left < right) {
            // 找到中点
            int mid = left + (right - left) / 2;
            
            log.info("🔄 分割数组: [{}-{}] -> [{}-{}] 和 [{}-{}]", 
                    left, right, left, mid, mid + 1, right);
            
            // 递归排序左半部分
            mergeSort(data, left, mid);
            
            // 递归排序右半部分
            mergeSort(data, mid + 1, right);
            
            // 合并两个已排序的部分
            merge(data, left, mid, right);
        }
    }
    
    /**
     * 合并两个已排序的子数组
     * 
     * @param data 数组
     * @param left 左边界
     * @param mid 中点
     * @param right 右边界
     */
    private void merge(int[] data, int left, int mid, int right) {
        merges++;
        
        // 创建临时数组
        int[] leftArray = new int[mid - left + 1];
        int[] rightArray = new int[right - mid];
        
        // 复制数据到临时数组
        System.arraycopy(data, left, leftArray, 0, leftArray.length);
        System.arraycopy(data, mid + 1, rightArray, 0, rightArray.length);
        
        log.info("🔀 合并子数组: {} 和 {}", 
                Arrays.toString(leftArray), Arrays.toString(rightArray));
        
        // 合并临时数组回到原数组
        int i = 0, j = 0, k = left;
        
        while (i < leftArray.length && j < rightArray.length) {
            comparisons++;
            
            if (leftArray[i] <= rightArray[j]) {
                data[k] = leftArray[i];
                i++;
            } else {
                data[k] = rightArray[j];
                j++;
            }
            k++;
        }
        
        // 复制剩余元素
        while (i < leftArray.length) {
            data[k] = leftArray[i];
            i++;
            k++;
        }
        
        while (j < rightArray.length) {
            data[k] = rightArray[j];
            j++;
            k++;
        }
        
        log.info("✅ 合并完成: {}", 
                Arrays.toString(Arrays.copyOfRange(data, left, right + 1)));
    }
    
    @Override
    public String getAlgorithmName() {
        return "归并排序";
    }
    
    @Override
    public String getTimeComplexity() {
        return "O(n log n)";
    }
}
