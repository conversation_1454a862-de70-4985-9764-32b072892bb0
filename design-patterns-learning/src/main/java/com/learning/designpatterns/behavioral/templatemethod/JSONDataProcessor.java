package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * JSON数据处理器
 * 
 * 实现JSON格式数据的具体处理步骤
 * 
 * <AUTHOR>
 */
@Slf4j
public class JSONDataProcessor extends DataProcessor {
    
    @Override
    protected String readData(String source) {
        log.info("📄 从JSON文件读取数据: {}", source);
        // 模拟读取JSON文件
        return "{\"users\":[{\"name\":\"John\",\"age\":25,\"email\":\"<EMAIL>\"}," +
               "{\"name\":\"Jane\",\"age\":30,\"email\":\"<EMAIL>\"}]}";
    }
    
    @Override
    protected Object parseData(String rawData) {
        log.info("🔧 解析JSON格式数据");
        // 模拟JSON解析
        log.info("📊 JSON数据解析完成");
        return rawData; // 简化实现
    }
    
    @Override
    protected Object transformData(Object parsedData) {
        log.info("🔄 转换JSON数据为标准格式");
        // 模拟数据转换
        return "转换后的JSON数据: 包含2个用户记录";
    }
    
    @Override
    protected void saveData(Object data) {
        log.info("💾 将处理后的JSON数据保存到NoSQL数据库");
        log.info("📝 保存内容: {}", data);
    }
    
    @Override
    protected boolean shouldGenerateReport() {
        return true; // JSON处理需要详细报告
    }
    
    @Override
    protected void generateReport(Object data) {
        super.generateReport(data);
        log.info("📊 生成JSON处理的详细统计报告");
    }
    
    @Override
    protected String getDataFormat() {
        return "JSON";
    }
}
