package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 面积计算访问者
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class AreaCalculatorVisitor implements ShapeVisitor {
    
    private double totalArea = 0.0;
    private int shapeCount = 0;
    
    @Override
    public void visit(Circle circle) {
        double area = Math.PI * circle.getRadius() * circle.getRadius();
        log.info("🔵 圆形 (半径: {}) - 面积: {:.2f}", circle.getRadius(), area);
        totalArea += area;
        shapeCount++;
    }
    
    @Override
    public void visit(Rectangle rectangle) {
        double area = rectangle.getWidth() * rectangle.getHeight();
        log.info("🔲 矩形 ({}x{}) - 面积: {:.2f}", rectangle.getWidth(), rectangle.getHeight(), area);
        totalArea += area;
        shapeCount++;
    }
    
    @Override
    public void visit(Triangle triangle) {
        // 使用海伦公式计算三角形面积
        double s = (triangle.getA() + triangle.getB() + triangle.getC()) / 2;
        double area = Math.sqrt(s * (s - triangle.getA()) * (s - triangle.getB()) * (s - triangle.getC()));
        log.info("🔺 三角形 ({}, {}, {}) - 面积: {:.2f}", 
                triangle.getA(), triangle.getB(), triangle.getC(), area);
        totalArea += area;
        shapeCount++;
    }
    
    public double getAverageArea() {
        return shapeCount > 0 ? totalArea / shapeCount : 0.0;
    }
}
