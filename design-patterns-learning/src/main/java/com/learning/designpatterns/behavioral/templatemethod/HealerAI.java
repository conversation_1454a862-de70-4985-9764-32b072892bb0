package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 治疗师AI
 * 
 * <AUTHOR>
 */
@Slf4j
public class HealerAI extends GameAI {
    
    @Override
    protected void analyzeSituation() {
        log.info("💚 分析队友状态");
        log.info("❤️ 检查生命值情况");
        log.info("🛡️ 评估威胁等级");
    }
    
    @Override
    protected void planStrategy() {
        log.info("🎯 制定治疗优先级");
        log.info("💚 选择治疗目标");
        log.info("🛡️ 规划安全位置");
    }
    
    @Override
    protected void executeAction() {
        log.info("💚 施展治疗术");
        log.info("✨ 使用群体治疗");
        log.info("🛡️ 保持后排位置");
    }
    
    @Override
    protected boolean shouldEvaluateResult() {
        return true; // 治疗师需要持续评估治疗效果
    }
    
    @Override
    protected void evaluateResult() {
        super.evaluateResult();
        log.info("💚 检查治疗效果");
        log.info("📊 统计治疗量");
    }
    
    @Override
    protected String getAIType() {
        return "治疗师AI";
    }
}
