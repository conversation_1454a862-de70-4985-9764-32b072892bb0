package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 游戏AI抽象类
 * 
 * 定义了AI行为的模板方法
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class GameAI {
    
    /**
     * 模板方法：定义AI的完整行为流程
     */
    public final void takeTurn() {
        log.info("🤖 {} 开始行动", getAIType());
        
        // 1. 感知环境
        perceiveEnvironment();
        
        // 2. 分析情况
        analyzeSituation();
        
        // 3. 制定策略
        planStrategy();
        
        // 4. 执行行动
        executeAction();
        
        // 5. 评估结果（钩子方法）
        if (shouldEvaluateResult()) {
            evaluateResult();
        }
        
        log.info("✅ {} 行动完成", getAIType());
    }
    
    /**
     * 感知环境 - 所有AI都需要感知
     */
    protected void perceiveEnvironment() {
        log.info("👁️ 感知周围环境");
        log.info("🔍 扫描敌人位置");
        log.info("💚 检查自身状态");
    }
    
    /**
     * 分析情况 - 抽象方法
     */
    protected abstract void analyzeSituation();
    
    /**
     * 制定策略 - 抽象方法
     */
    protected abstract void planStrategy();
    
    /**
     * 执行行动 - 抽象方法
     */
    protected abstract void executeAction();
    
    /**
     * 钩子方法：是否需要评估结果
     */
    protected boolean shouldEvaluateResult() {
        return true;
    }
    
    /**
     * 评估结果
     */
    protected void evaluateResult() {
        log.info("📊 评估行动效果");
    }
    
    /**
     * 获取AI类型 - 抽象方法
     */
    protected abstract String getAIType();
}
