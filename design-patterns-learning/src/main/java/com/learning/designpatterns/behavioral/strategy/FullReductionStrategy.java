package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 满减促销策略
 * 
 * 满足一定金额后减免固定金额
 * 
 * <AUTHOR>
 */
@Slf4j
public class FullReductionStrategy implements PromotionStrategy {
    
    private final double threshold;
    private final double reduction;
    
    /**
     * 构造函数
     * 
     * @param threshold 满减门槛
     * @param reduction 减免金额
     */
    public FullReductionStrategy(double threshold, double reduction) {
        this.threshold = threshold;
        this.reduction = reduction;
    }
    
    @Override
    public double calculatePrice(List<ShoppingCart.Item> items, double originalTotal) {
        if (originalTotal >= threshold) {
            double finalPrice = originalTotal - reduction;
            log.info("🎯 满减计算: 消费 ¥{:.2f} >= ¥{:.2f}，减免 ¥{:.2f}", 
                    originalTotal, threshold, reduction);
            return Math.max(finalPrice, 0); // 确保价格不为负
        } else {
            double needed = threshold - originalTotal;
            log.info("❌ 未达到满减条件: 还需消费 ¥{:.2f} 即可享受满减优惠", needed);
            return originalTotal;
        }
    }
    
    @Override
    public String getStrategyName() {
        return String.format("满%.0f减%.0f", threshold, reduction);
    }
    
    @Override
    public String getDescription() {
        return String.format("单笔消费满%.0f元立减%.0f元", threshold, reduction);
    }
}
