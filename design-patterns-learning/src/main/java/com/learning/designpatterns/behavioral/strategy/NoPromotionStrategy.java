package com.learning.designpatterns.behavioral.strategy;

import java.util.List;

/**
 * 无促销策略
 * 
 * 不应用任何促销，按原价计算
 * 
 * <AUTHOR>
 */
public class NoPromotionStrategy implements PromotionStrategy {
    
    @Override
    public double calculatePrice(List<ShoppingCart.Item> items, double originalTotal) {
        return originalTotal;
    }
    
    @Override
    public String getStrategyName() {
        return "无促销";
    }
    
    @Override
    public String getDescription() {
        return "按原价计算，无任何优惠";
    }
}
