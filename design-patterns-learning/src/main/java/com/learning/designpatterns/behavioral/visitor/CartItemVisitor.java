package com.learning.designpatterns.behavioral.visitor;

/**
 * 购物车商品访问者接口
 * 
 * 定义了访问不同类型商品的方法
 * 
 * <AUTHOR>
 */
public interface CartItemVisitor {
    
    /**
     * 访问图书
     * 
     * @param book 图书对象
     */
    void visit(Book book);
    
    /**
     * 访问电子产品
     * 
     * @param electronics 电子产品对象
     */
    void visit(Electronics electronics);
    
    /**
     * 访问服装
     * 
     * @param clothing 服装对象
     */
    void visit(Clothing clothing);
}
