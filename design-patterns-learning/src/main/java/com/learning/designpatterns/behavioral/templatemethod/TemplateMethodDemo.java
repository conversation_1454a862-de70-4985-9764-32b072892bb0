package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 模板方法模式演示类
 * 
 * 模板方法模式在一个方法中定义一个算法的骨架，而将一些步骤延迟到子类中。
 * 模板方法使得子类可以在不改变算法结构的情况下，重新定义算法中的某些步骤。
 * 
 * 应用场景：
 * - 数据处理流程（读取→处理→保存）
 * - 饮料制作流程（煮水→冲泡→倒入杯子→添加调料）
 * - 游戏AI行为（感知→思考→行动）
 * - 文档生成流程（收集数据→格式化→输出）
 * - 测试框架（初始化→执行→清理）
 * 
 * <AUTHOR>
 */
@Slf4j
public class TemplateMethodDemo {
    
    public static void main(String[] args) {
        log.info("=== 模板方法模式演示 ===");
        
        // 演示饮料制作
        demonstrateBeverageMaking();
        
        // 演示数据处理
        demonstrateDataProcessing();
        
        // 演示游戏AI
        demonstrateGameAI();
    }
    
    private static void demonstrateBeverageMaking() {
        log.info("\n--- 饮料制作模板方法演示 ---");
        
        // 制作咖啡
        log.info("☕ 制作咖啡:");
        Beverage coffee = new Coffee();
        coffee.prepareBeverage();
        
        // 制作茶
        log.info("\n🍵 制作茶:");
        Beverage tea = new Tea();
        tea.prepareBeverage();
        
        // 制作奶茶
        log.info("\n🧋 制作奶茶:");
        Beverage milkTea = new MilkTea();
        milkTea.prepareBeverage();
        
        // 制作果汁
        log.info("\n🥤 制作果汁:");
        Beverage juice = new FruitJuice();
        juice.prepareBeverage();
    }
    
    private static void demonstrateDataProcessing() {
        log.info("\n--- 数据处理模板方法演示 ---");
        
        // CSV数据处理
        log.info("📊 CSV数据处理:");
        DataProcessor csvProcessor = new CSVDataProcessor();
        csvProcessor.processData("users.csv");
        
        // JSON数据处理
        log.info("\n📋 JSON数据处理:");
        DataProcessor jsonProcessor = new JSONDataProcessor();
        jsonProcessor.processData("users.json");
        
        // XML数据处理
        log.info("\n📄 XML数据处理:");
        DataProcessor xmlProcessor = new XMLDataProcessor();
        xmlProcessor.processData("users.xml");
        
        // 数据库数据处理
        log.info("\n🗄️ 数据库数据处理:");
        DataProcessor dbProcessor = new DatabaseDataProcessor();
        dbProcessor.processData("user_table");
    }
    
    private static void demonstrateGameAI() {
        log.info("\n--- 游戏AI模板方法演示 ---");
        
        // 战士AI
        log.info("⚔️ 战士AI行为:");
        GameAI warriorAI = new WarriorAI();
        warriorAI.takeTurn();
        
        // 法师AI
        log.info("\n🔮 法师AI行为:");
        GameAI mageAI = new MageAI();
        mageAI.takeTurn();
        
        // 弓箭手AI
        log.info("\n🏹 弓箭手AI行为:");
        GameAI archerAI = new ArcherAI();
        archerAI.takeTurn();
        
        // 治疗师AI
        log.info("\n💚 治疗师AI行为:");
        GameAI healerAI = new HealerAI();
        healerAI.takeTurn();
    }
}
