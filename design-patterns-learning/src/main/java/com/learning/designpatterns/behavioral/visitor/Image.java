package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;

/**
 * 图片类
 * 
 * <AUTHOR>
 */
@Getter
public class Image implements DocumentElement {
    
    private final String src;
    private final String alt;
    
    public Image(String src, String alt) {
        this.src = src;
        this.alt = alt;
    }
    
    @Override
    public void accept(DocumentElementVisitor visitor) {
        visitor.visit(this);
    }
}
