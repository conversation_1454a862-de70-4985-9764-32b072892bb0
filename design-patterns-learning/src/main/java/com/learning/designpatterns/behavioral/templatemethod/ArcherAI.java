package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 弓箭手AI
 * 
 * <AUTHOR>
 */
@Slf4j
public class ArcherAI extends GameAI {
    
    @Override
    protected void analyzeSituation() {
        log.info("🏹 分析射击环境");
        log.info("🎯 测量射击距离");
        log.info("💨 评估风向影响");
    }
    
    @Override
    protected void planStrategy() {
        log.info("🎯 制定远程攻击策略");
        log.info("🏹 选择最佳射击位置");
        log.info("🎯 瞄准关键目标");
    }
    
    @Override
    protected void executeAction() {
        log.info("🏹 发射精准箭矢");
        log.info("🎯 使用穿透射击");
        log.info("🏃 保持安全距离");
    }
    
    @Override
    protected String getAIType() {
        return "弓箭手AI";
    }
}
