package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;

/**
 * 图书类
 * 
 * 具体的购物车商品元素
 * 
 * <AUTHOR>
 */
@Getter
public class Book implements CartItem {
    
    private final String name;
    private final double price;
    private final String author;
    private final String isbn;
    
    public Book(String name, double price, String author, String isbn) {
        this.name = name;
        this.price = price;
        this.author = author;
        this.isbn = isbn;
    }
    
    @Override
    public void accept(CartItemVisitor visitor) {
        visitor.visit(this);
    }
    
    @Override
    public String toString() {
        return String.format("图书[%s - %s, ¥%.2f]", name, author, price);
    }
}
