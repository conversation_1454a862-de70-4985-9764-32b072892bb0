package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 策略模式演示类
 * 
 * 策略模式定义了一系列算法，把它们一个个封装起来，并且使它们可相互替换。
 * 策略模式让算法的变化独立于使用算法的客户。
 * 
 * 应用场景：
 * - 排序算法选择（快排、归并、堆排序）
 * - 支付方式选择（信用卡、支付宝、微信）
 * - 压缩算法选择（ZIP、RAR、7Z）
 * - 路径规划算法（最短路径、最快路径、最省油路径）
 * - 促销策略（满减、打折、买赠）
 * 
 * <AUTHOR>
 */
@Slf4j
public class StrategyDemo {
    
    public static void main(String[] args) {
        log.info("=== 策略模式演示 ===");
        
        // 演示排序策略
        demonstrateSortingStrategies();
        
        // 演示促销策略
        demonstratePromotionStrategies();
        
        // 演示路径规划策略
        demonstrateNavigationStrategies();
    }
    
    private static void demonstrateSortingStrategies() {
        log.info("\n--- 排序策略演示 ---");
        
        int[] data = {64, 34, 25, 12, 22, 11, 90, 88, 76, 50, 42};
        
        // 创建排序上下文
        SortContext sortContext = new SortContext();
        
        // 使用冒泡排序
        sortContext.setSortStrategy(new BubbleSortStrategy());
        sortContext.sort(data.clone());
        
        // 使用快速排序
        sortContext.setSortStrategy(new QuickSortStrategy());
        sortContext.sort(data.clone());
        
        // 使用归并排序
        sortContext.setSortStrategy(new MergeSortStrategy());
        sortContext.sort(data.clone());
    }
    
    private static void demonstratePromotionStrategies() {
        log.info("\n--- 促销策略演示 ---");
        
        // 创建购物车
        ShoppingCart cart = new ShoppingCart();
        cart.addItem("笔记本电脑", 5999.0, 1);
        cart.addItem("无线鼠标", 199.0, 2);
        cart.addItem("机械键盘", 599.0, 1);
        
        // 显示原价
        cart.showCart();
        
        // 应用不同的促销策略
        cart.setPromotionStrategy(new DiscountStrategy(0.1)); // 9折
        cart.calculateTotal();
        
        cart.setPromotionStrategy(new FullReductionStrategy(6000, 500)); // 满6000减500
        cart.calculateTotal();
        
        cart.setPromotionStrategy(new BuyOneGetOneStrategy()); // 买一送一
        cart.calculateTotal();
        
        cart.setPromotionStrategy(new NoPromotionStrategy()); // 无促销
        cart.calculateTotal();
    }
    
    private static void demonstrateNavigationStrategies() {
        log.info("\n--- 路径规划策略演示 ---");
        
        // 创建导航系统
        NavigationSystem navigation = new NavigationSystem();
        
        String start = "北京";
        String destination = "上海";
        
        // 使用不同的路径规划策略
        navigation.setRouteStrategy(new FastestRouteStrategy());
        navigation.planRoute(start, destination);
        
        navigation.setRouteStrategy(new ShortestRouteStrategy());
        navigation.planRoute(start, destination);
        
        navigation.setRouteStrategy(new EconomicalRouteStrategy());
        navigation.planRoute(start, destination);
        
        navigation.setRouteStrategy(new ScenicRouteStrategy());
        navigation.planRoute(start, destination);
    }
}
