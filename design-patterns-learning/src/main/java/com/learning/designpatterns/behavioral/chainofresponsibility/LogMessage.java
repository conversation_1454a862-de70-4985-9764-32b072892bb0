package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.Getter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日志消息类
 * 
 * <AUTHOR>
 */
@Getter
public class LogMessage {
    
    private final LogLevel level;
    private final String message;
    private final LocalDateTime timestamp;
    
    public LogMessage(LogLevel level, String message) {
        this.level = level;
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }
    
    public String getFormattedMessage() {
        return String.format("[%s] %s - %s", 
                timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                level, 
                message);
    }
    
    @Override
    public String toString() {
        return getFormattedMessage();
    }
}
