package com.learning.designpatterns.behavioral.observer;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 新闻机构主题类
 * 
 * 实现了Subject接口，当发布新闻时，
 * 会自动通知所有订阅者
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class NewsAgency implements Subject {
    
    /**
     * 新闻机构名称
     */
    private final String name;
    
    /**
     * 最新新闻
     */
    private String latestNews;
    
    /**
     * 新闻发布时间
     */
    private LocalDateTime publishTime;
    
    /**
     * 观察者列表（订阅者）
     */
    private final List<Observer> observers;
    
    /**
     * 新闻历史记录
     */
    private final List<NewsItem> newsHistory;
    
    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 构造函数
     * 
     * @param name 新闻机构名称
     */
    public NewsAgency(String name) {
        this.name = name;
        this.observers = new ArrayList<>();
        this.newsHistory = new ArrayList<>();
        log.info("新闻机构 {} 已成立，开始提供新闻服务", name);
    }
    
    /**
     * 发布新闻
     * 
     * @param news 新闻内容
     */
    public void publishNews(String news) {
        this.latestNews = news;
        this.publishTime = LocalDateTime.now();
        
        // 添加到历史记录
        newsHistory.add(new NewsItem(news, publishTime));
        
        log.info("📰 {} 发布新闻: {}", name, news);
        log.info("发布时间: {}", publishTime.format(TIME_FORMATTER));
        
        // 通知所有订阅者
        notifyObservers();
    }
    
    @Override
    public void addObserver(Observer observer) {
        if (!observers.contains(observer)) {
            observers.add(observer);
            log.info("新增订阅者关注 {}", name);
        }
    }
    
    @Override
    public void removeObserver(Observer observer) {
        if (observers.remove(observer)) {
            log.info("订阅者取消关注 {}", name);
        }
    }
    
    @Override
    public void notifyObservers() {
        log.info("向 {} 个订阅者推送新闻", observers.size());
        
        for (Observer observer : observers) {
            try {
                observer.update(this);
            } catch (Exception e) {
                log.error("通知订阅者时发生错误", e);
            }
        }
    }
    
    /**
     * 获取新闻历史记录
     * 
     * @return 新闻历史记录
     */
    public List<NewsItem> getNewsHistory() {
        return new ArrayList<>(newsHistory);
    }
    
    /**
     * 获取订阅者数量
     * 
     * @return 订阅者数量
     */
    public int getSubscriberCount() {
        return observers.size();
    }
    
    /**
     * 新闻项内部类
     */
    @Getter
    public static class NewsItem {
        private final String content;
        private final LocalDateTime publishTime;
        
        public NewsItem(String content, LocalDateTime publishTime) {
            this.content = content;
            this.publishTime = publishTime;
        }
        
        @Override
        public String toString() {
            return String.format("[%s] %s", 
                    publishTime.format(TIME_FORMATTER), content);
        }
    }
    
    @Override
    public String toString() {
        return String.format("NewsAgency{name='%s', subscribers=%d, newsCount=%d}", 
                name, observers.size(), newsHistory.size());
    }
}
