package com.learning.designpatterns.behavioral.state;

import lombok.extern.slf4j.Slf4j;

/**
 * 售出状态
 * 
 * 正在出货的状态
 * 
 * <AUTHOR>
 */
@Slf4j
public class SoldState implements VendingMachineState {
    
    private final VendingMachine vendingMachine;
    
    public SoldState(VendingMachine vendingMachine) {
        this.vendingMachine = vendingMachine;
    }
    
    @Override
    public void insertCoin() {
        log.warn("❌ 正在出货，请稍候");
    }
    
    @Override
    public void ejectCoin() {
        log.warn("❌ 商品已售出，无法退币");
    }
    
    @Override
    public void selectProduct() {
        log.warn("❌ 正在出货，请稍候");
    }
    
    @Override
    public void dispenseProduct() {
        // 模拟出货过程
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        vendingMachine.releaseProduct();
        
        // 检查库存并切换状态
        if (vendingMachine.getInventory() > 0) {
            log.info("✅ 出货完成，等待下一位顾客");
            vendingMachine.setState(vendingMachine.getNoCoinState());
        } else {
            log.info("📦 商品售罄，请联系管理员补货");
            vendingMachine.setState(vendingMachine.getSoldOutState());
        }
    }
    
    @Override
    public String getStateName() {
        return "正在出货";
    }
}
