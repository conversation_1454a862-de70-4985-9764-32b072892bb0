package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 法师AI
 * 
 * <AUTHOR>
 */
@Slf4j
public class MageAI extends GameAI {
    
    @Override
    protected void analyzeSituation() {
        log.info("🔮 分析魔法环境");
        log.info("⚡ 检查法力值");
        log.info("📚 评估可用法术");
    }
    
    @Override
    protected void planStrategy() {
        log.info("🎯 制定魔法攻击策略");
        log.info("🔮 选择最佳法术组合");
        log.info("⚡ 计算法力消耗");
    }
    
    @Override
    protected void executeAction() {
        log.info("🔮 释放魔法攻击");
        log.info("⚡ 使用闪电术");
        log.info("🛡️ 施展魔法护盾");
    }
    
    @Override
    protected String getAIType() {
        return "法师AI";
    }
}
