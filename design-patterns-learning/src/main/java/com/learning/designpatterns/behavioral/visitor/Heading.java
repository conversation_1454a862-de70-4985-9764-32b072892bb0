package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;

/**
 * 标题类
 * 
 * <AUTHOR>
 */
@Getter
public class Heading implements DocumentElement {
    
    private final String text;
    private final int level;
    
    public Heading(String text, int level) {
        this.text = text;
        this.level = level;
    }
    
    @Override
    public void accept(DocumentElementVisitor visitor) {
        visitor.visit(this);
    }
}
