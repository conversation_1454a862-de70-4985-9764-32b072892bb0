package com.learning.designpatterns.behavioral.visitor;

import lombok.extern.slf4j.Slf4j;

/**
 * 绘制访问者
 * 
 * <AUTHOR>
 */
@Slf4j
public class DrawingVisitor implements ShapeVisitor {
    
    @Override
    public void visit(Circle circle) {
        log.info("🎨 绘制圆形: 半径 {}", circle.getRadius());
    }
    
    @Override
    public void visit(Rectangle rectangle) {
        log.info("🎨 绘制矩形: {}x{}", rectangle.getWidth(), rectangle.getHeight());
    }
    
    @Override
    public void visit(Triangle triangle) {
        log.info("🎨 绘制三角形: 边长 {}, {}, {}", triangle.getA(), triangle.getB(), triangle.getC());
    }
}
