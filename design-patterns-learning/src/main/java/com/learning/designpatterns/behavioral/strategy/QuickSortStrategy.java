package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;

/**
 * 快速排序策略
 * 
 * 实现快速排序算法
 * 时间复杂度：平均O(n log n)，最坏O(n²)，空间复杂度：O(log n)
 * 
 * <AUTHOR>
 */
@Slf4j
public class QuickSortStrategy implements SortStrategy {
    
    private int comparisons = 0;
    private int swaps = 0;
    
    @Override
    public void sort(int[] data) {
        log.info("⚡ 开始快速排序");
        log.info("原始数据: {}", Arrays.toString(data));
        
        long startTime = System.nanoTime();
        comparisons = 0;
        swaps = 0;
        
        quickSort(data, 0, data.length - 1);
        
        long endTime = System.nanoTime();
        double duration = (endTime - startTime) / 1_000_000.0; // 转换为毫秒
        
        log.info("✅ 快速排序完成");
        log.info("排序结果: {}", Arrays.toString(data));
        log.info("📊 统计信息 - 比较次数: {}, 交换次数: {}, 耗时: {:.2f}ms", 
                comparisons, swaps, duration);
    }
    
    /**
     * 快速排序递归实现
     * 
     * @param data 数组
     * @param low 起始索引
     * @param high 结束索引
     */
    private void quickSort(int[] data, int low, int high) {
        if (low < high) {
            // 分区操作，获取基准元素的正确位置
            int pivotIndex = partition(data, low, high);
            
            log.info("分区完成，基准位置: {}, 当前状态: {}", pivotIndex, Arrays.toString(data));
            
            // 递归排序基准元素左边的子数组
            quickSort(data, low, pivotIndex - 1);
            
            // 递归排序基准元素右边的子数组
            quickSort(data, pivotIndex + 1, high);
        }
    }
    
    /**
     * 分区操作
     * 
     * @param data 数组
     * @param low 起始索引
     * @param high 结束索引
     * @return 基准元素的最终位置
     */
    private int partition(int[] data, int low, int high) {
        // 选择最后一个元素作为基准
        int pivot = data[high];
        log.info("🎯 选择基准元素: {} (位置: {})", pivot, high);
        
        int i = low - 1; // 小于基准元素的区域的边界
        
        for (int j = low; j < high; j++) {
            comparisons++;
            
            // 如果当前元素小于或等于基准元素
            if (data[j] <= pivot) {
                i++;
                swap(data, i, j);
            }
        }
        
        // 将基准元素放到正确位置
        swap(data, i + 1, high);
        
        return i + 1;
    }
    
    /**
     * 交换数组中两个元素
     * 
     * @param data 数组
     * @param i 第一个元素索引
     * @param j 第二个元素索引
     */
    private void swap(int[] data, int i, int j) {
        if (i != j) {
            int temp = data[i];
            data[i] = data[j];
            data[j] = temp;
            swaps++;
        }
    }
    
    @Override
    public String getAlgorithmName() {
        return "快速排序";
    }
    
    @Override
    public String getTimeComplexity() {
        return "平均O(n log n), 最坏O(n²)";
    }
}
