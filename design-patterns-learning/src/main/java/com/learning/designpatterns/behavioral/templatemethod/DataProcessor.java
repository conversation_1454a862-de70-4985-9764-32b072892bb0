package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据处理抽象类
 * 
 * 定义了数据处理的模板方法
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class DataProcessor {
    
    /**
     * 模板方法：定义数据处理的完整流程
     */
    public final void processData(String source) {
        log.info("📊 开始处理 {} 格式的数据: {}", getDataFormat(), source);
        
        // 1. 读取数据
        String rawData = readData(source);
        
        // 2. 验证数据
        if (validateData(rawData)) {
            // 3. 解析数据
            Object parsedData = parseData(rawData);
            
            // 4. 转换数据
            Object transformedData = transformData(parsedData);
            
            // 5. 验证处理结果
            if (validateProcessedData(transformedData)) {
                // 6. 保存数据
                saveData(transformedData);
                
                // 7. 生成报告（钩子方法）
                if (shouldGenerateReport()) {
                    generateReport(transformedData);
                }
                
                log.info("✅ {} 数据处理完成", getDataFormat());
            } else {
                log.error("❌ 处理后的数据验证失败");
            }
        } else {
            log.error("❌ 原始数据验证失败");
        }
    }
    
    /**
     * 读取数据 - 抽象方法
     */
    protected abstract String readData(String source);
    
    /**
     * 验证原始数据
     */
    protected boolean validateData(String rawData) {
        log.info("🔍 验证原始数据格式");
        return rawData != null && !rawData.trim().isEmpty();
    }
    
    /**
     * 解析数据 - 抽象方法
     */
    protected abstract Object parseData(String rawData);
    
    /**
     * 转换数据 - 抽象方法
     */
    protected abstract Object transformData(Object parsedData);
    
    /**
     * 验证处理后的数据
     */
    protected boolean validateProcessedData(Object processedData) {
        log.info("✅ 验证处理后的数据");
        return processedData != null;
    }
    
    /**
     * 保存数据 - 抽象方法
     */
    protected abstract void saveData(Object data);
    
    /**
     * 钩子方法：是否生成报告
     */
    protected boolean shouldGenerateReport() {
        return true; // 默认生成报告
    }
    
    /**
     * 生成报告
     */
    protected void generateReport(Object data) {
        log.info("📋 生成数据处理报告");
    }
    
    /**
     * 获取数据格式名称 - 抽象方法
     */
    protected abstract String getDataFormat();
}
