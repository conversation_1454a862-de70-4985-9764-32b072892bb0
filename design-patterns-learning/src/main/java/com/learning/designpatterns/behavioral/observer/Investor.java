package com.learning.designpatterns.behavioral.observer;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 投资者观察者类
 * 
 * 实现了Observer接口，当关注的股票价格发生变化时，
 * 投资者会收到通知并做出相应的投资决策
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class Investor implements Observer {
    
    /**
     * 投资者姓名
     */
    private final String name;
    
    /**
     * 买入阈值（价格低于此值时考虑买入）
     */
    private final double buyThreshold;
    
    /**
     * 卖出阈值（价格高于此值时考虑卖出）
     */
    private final double sellThreshold;
    
    /**
     * 构造函数
     * 
     * @param name 投资者姓名
     */
    public Investor(String name) {
        this.name = name;
        // 设置默认的买入和卖出阈值
        this.buyThreshold = 145.0;
        this.sellThreshold = 160.0;
        log.info("投资者 {} 已准备就绪，买入阈值: ${}，卖出阈值: ${}", name, buyThreshold, sellThreshold);
    }
    
    /**
     * 构造函数（自定义阈值）
     * 
     * @param name 投资者姓名
     * @param buyThreshold 买入阈值
     * @param sellThreshold 卖出阈值
     */
    public Investor(String name, double buyThreshold, double sellThreshold) {
        this.name = name;
        this.buyThreshold = buyThreshold;
        this.sellThreshold = sellThreshold;
        log.info("投资者 {} 已准备就绪，买入阈值: ${}，卖出阈值: ${}", name, buyThreshold, sellThreshold);
    }
    
    @Override
    public void update(Subject subject) {
        if (subject instanceof StockPrice stockPrice) {
            double currentPrice = stockPrice.getPrice();
            String symbol = stockPrice.getSymbol();
            
            log.info("投资者 {} 收到股票 {} 价格更新通知: ${}", name, symbol, currentPrice);
            
            // 根据价格做出投资决策
            makeInvestmentDecision(symbol, currentPrice);
        }
    }
    
    /**
     * 做出投资决策
     * 
     * @param symbol 股票代码
     * @param currentPrice 当前价格
     */
    private void makeInvestmentDecision(String symbol, double currentPrice) {
        if (currentPrice <= buyThreshold) {
            log.info("🟢 投资者 {} 决定买入股票 {} (当前价格: ${} <= 买入阈值: ${})", 
                    name, symbol, currentPrice, buyThreshold);
            executeBuyOrder(symbol, currentPrice);
        } else if (currentPrice >= sellThreshold) {
            log.info("🔴 投资者 {} 决定卖出股票 {} (当前价格: ${} >= 卖出阈值: ${})", 
                    name, symbol, currentPrice, sellThreshold);
            executeSellOrder(symbol, currentPrice);
        } else {
            log.info("⚪ 投资者 {} 对股票 {} 持观望态度 (价格: ${})", name, symbol, currentPrice);
        }
    }
    
    /**
     * 执行买入订单
     * 
     * @param symbol 股票代码
     * @param price 买入价格
     */
    private void executeBuyOrder(String symbol, double price) {
        // 模拟买入操作
        log.info("📈 投资者 {} 正在以 ${} 的价格买入股票 {}", name, price, symbol);
        
        // 实际项目中，这里会调用交易系统的API
        // tradingSystem.placeBuyOrder(symbol, quantity, price);
    }
    
    /**
     * 执行卖出订单
     * 
     * @param symbol 股票代码
     * @param price 卖出价格
     */
    private void executeSellOrder(String symbol, double price) {
        // 模拟卖出操作
        log.info("📉 投资者 {} 正在以 ${} 的价格卖出股票 {}", name, price, symbol);
        
        // 实际项目中，这里会调用交易系统的API
        // tradingSystem.placeSellOrder(symbol, quantity, price);
    }
    
    @Override
    public String toString() {
        return String.format("Investor{name='%s', buyThreshold=%.2f, sellThreshold=%.2f}", 
                name, buyThreshold, sellThreshold);
    }
}
