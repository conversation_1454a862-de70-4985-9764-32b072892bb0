package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 奶茶类
 * 
 * 实现奶茶的具体制作步骤
 * 
 * <AUTHOR>
 */
@Slf4j
public class MilkTea extends Beverage {
    
    @Override
    protected void brew() {
        log.info("🧋 将红茶茶叶用热水冲泡");
        try {
            Thread.sleep(700); // 模拟冲泡时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("🧋 红茶基底制作完成");
    }
    
    @Override
    protected void addCondiments() {
        log.info("🥛 加入香浓牛奶");
        log.info("🧋 添加珍珠和椰果");
        log.info("🍯 调整甜度，制作完美奶茶");
    }
    
    @Override
    protected String getBeverageName() {
        return "奶茶";
    }
}
