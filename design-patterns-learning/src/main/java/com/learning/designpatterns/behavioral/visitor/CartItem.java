package com.learning.designpatterns.behavioral.visitor;

/**
 * 购物车商品接口
 * 
 * 定义了接受访问者的方法
 * 
 * <AUTHOR>
 */
public interface CartItem {
    
    /**
     * 接受访问者
     * 
     * @param visitor 访问者
     */
    void accept(CartItemVisitor visitor);
    
    /**
     * 获取商品名称
     * 
     * @return 商品名称
     */
    String getName();
    
    /**
     * 获取商品价格
     * 
     * @return 商品价格
     */
    double getPrice();
}
