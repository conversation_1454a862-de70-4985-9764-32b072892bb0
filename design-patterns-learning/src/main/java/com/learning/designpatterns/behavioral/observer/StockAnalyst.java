package com.learning.designpatterns.behavioral.observer;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 股票分析师观察者类
 * 
 * 实现了Observer接口，当关注的股票价格发生变化时，
 * 分析师会收到通知并进行技术分析
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class StockAnalyst implements Observer {
    
    /**
     * 分析师姓名
     */
    private final String name;
    
    /**
     * 上一次记录的价格（用于计算变化）
     */
    private double lastPrice = 0.0;
    
    /**
     * 构造函数
     * 
     * @param name 分析师姓名
     */
    public StockAnalyst(String name) {
        this.name = name;
        log.info("股票分析师 {} 开始监控股票市场", name);
    }
    
    @Override
    public void update(Subject subject) {
        if (subject instanceof StockPrice stockPrice) {
            double currentPrice = stockPrice.getPrice();
            String symbol = stockPrice.getSymbol();
            
            log.info("分析师 {} 收到股票 {} 价格更新通知: ${}", name, symbol, currentPrice);
            
            // 进行技术分析
            performTechnicalAnalysis(symbol, currentPrice);
            
            // 更新上一次价格
            this.lastPrice = currentPrice;
        }
    }
    
    /**
     * 执行技术分析
     * 
     * @param symbol 股票代码
     * @param currentPrice 当前价格
     */
    private void performTechnicalAnalysis(String symbol, double currentPrice) {
        if (lastPrice == 0.0) {
            log.info("📊 分析师 {} 开始跟踪股票 {} 的价格走势 (基准价格: ${})", 
                    name, symbol, currentPrice);
            return;
        }
        
        double priceChange = currentPrice - lastPrice;
        double changePercentage = (priceChange / lastPrice) * 100;
        
        // 分析价格趋势
        analyzePriceTrend(symbol, currentPrice, priceChange, changePercentage);
        
        // 生成投资建议
        generateInvestmentAdvice(symbol, currentPrice, changePercentage);
    }
    
    /**
     * 分析价格趋势
     * 
     * @param symbol 股票代码
     * @param currentPrice 当前价格
     * @param priceChange 价格变化
     * @param changePercentage 变化百分比
     */
    private void analyzePriceTrend(String symbol, double currentPrice, double priceChange, double changePercentage) {
        String trend;
        String emoji;
        
        if (priceChange > 0) {
            trend = "上涨";
            emoji = "📈";
        } else if (priceChange < 0) {
            trend = "下跌";
            emoji = "📉";
        } else {
            trend = "持平";
            emoji = "➡️";
        }
        
        log.info("{} 分析师 {} 技术分析: 股票 {} {} ${:.2f} ({:.2f}%)", 
                emoji, name, symbol, trend, Math.abs(priceChange), changePercentage);
    }
    
    /**
     * 生成投资建议
     * 
     * @param symbol 股票代码
     * @param currentPrice 当前价格
     * @param changePercentage 变化百分比
     */
    private void generateInvestmentAdvice(String symbol, double currentPrice, double changePercentage) {
        String advice;
        String riskLevel;
        
        if (changePercentage > 5) {
            advice = "强烈买入";
            riskLevel = "低风险";
        } else if (changePercentage > 2) {
            advice = "买入";
            riskLevel = "中低风险";
        } else if (changePercentage > -2) {
            advice = "持有";
            riskLevel = "中等风险";
        } else if (changePercentage > -5) {
            advice = "卖出";
            riskLevel = "中高风险";
        } else {
            advice = "强烈卖出";
            riskLevel = "高风险";
        }
        
        log.info("💡 分析师 {} 投资建议: 股票 {} - {} (风险等级: {})", 
                name, symbol, advice, riskLevel);
        
        // 如果是极端变化，发出警告
        if (Math.abs(changePercentage) > 10) {
            log.warn("⚠️ 分析师 {} 风险警告: 股票 {} 价格波动异常 ({:.2f}%)，请谨慎投资！", 
                    name, symbol, changePercentage);
        }
    }
    
    @Override
    public String toString() {
        return String.format("StockAnalyst{name='%s', lastPrice=%.2f}", name, lastPrice);
    }
}
