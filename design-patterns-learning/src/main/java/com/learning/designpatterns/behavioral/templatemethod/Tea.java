package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 茶类
 * 
 * 实现茶的具体制作步骤
 * 
 * <AUTHOR>
 */
@Slf4j
public class Tea extends Beverage {
    
    @Override
    protected void brew() {
        log.info("🍵 将茶叶放入热水中浸泡");
        try {
            Thread.sleep(600); // 模拟浸泡时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("🍵 茶叶充分浸泡，茶汤清香");
    }
    
    @Override
    protected void addCondiments() {
        log.info("🍋 添加柠檬片");
        log.info("🍯 可选择添加蜂蜜调味");
    }
    
    @Override
    protected String getBeverageName() {
        return "茶";
    }
}
