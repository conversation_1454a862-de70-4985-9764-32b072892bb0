package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 最短路径策略
 * 
 * 优先选择距离最短的路径，通常选择直线距离最近的道路
 * 
 * <AUTHOR>
 */
@Slf4j
public class ShortestRouteStrategy implements RouteStrategy {
    
    @Override
    public RouteInfo planRoute(String start, String destination) {
        log.info("📏 计算最短路径...");
        
        // 模拟路径计算
        simulateCalculation();
        
        // 最短路径距离最短但可能时间较长，费用中等
        double distance = calculateDistance(start, destination) * 0.85; // 距离最短
        double duration = calculateBaseDuration(start, destination) * 1.1; // 时间稍长
        double cost = calculateBaseCost(start, destination) * 0.9; // 费用中等
        
        String routeName = String.format("%s -> %s (最短路线)", start, destination);
        String description = "选择距离最短的路径，可能经过城市道路，时间稍长";
        
        log.info("📐 最短路径规划完成");
        
        return new RouteInfo(routeName, distance, duration, cost, description);
    }
    
    @Override
    public String getStrategyName() {
        return "最短路径";
    }
    
    /**
     * 模拟路径计算过程
     */
    private void simulateCalculation() {
        try {
            log.info("🗺️ 分析地图数据...");
            Thread.sleep(180);
            
            log.info("📐 计算直线距离...");
            Thread.sleep(220);
            
            log.info("🛤️ 寻找最短道路...");
            Thread.sleep(280);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("路径计算被中断", e);
        }
    }
    
    /**
     * 计算基础距离
     */
    private double calculateDistance(String start, String destination) {
        // 模拟距离计算
        return 1200 + Math.random() * 100; // 1200-1300km
    }
    
    /**
     * 计算基础时间
     */
    private double calculateBaseDuration(String start, String destination) {
        // 模拟时间计算
        return 12 + Math.random() * 2; // 12-14小时
    }
    
    /**
     * 计算基础费用
     */
    private double calculateBaseCost(String start, String destination) {
        // 模拟费用计算
        return 800 + Math.random() * 200; // 800-1000元
    }
}
