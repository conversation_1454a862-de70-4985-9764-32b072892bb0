package com.learning.designpatterns.behavioral.state;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单类
 * 
 * 使用状态模式管理订单的不同状态
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class Order {
    
    // 状态对象
    private final OrderState pendingPaymentState;
    
    // 当前状态
    private OrderState currentState;
    
    // 订单信息
    private final String orderId;
    
    public Order(String orderId) {
        this.orderId = orderId;
        
        // 初始化状态
        this.pendingPaymentState = new PendingPaymentState(this);
        
        // 设置初始状态
        this.currentState = pendingPaymentState;
        log.info("📋 订单创建: {}, 状态: {}", orderId, currentState.getStateName());
    }
    
    /**
     * 支付
     */
    public void pay() {
        currentState.pay();
    }
    
    /**
     * 发货
     */
    public void ship() {
        currentState.ship();
    }
    
    /**
     * 送达
     */
    public void deliver() {
        currentState.deliver();
    }
    
    /**
     * 完成
     */
    public void complete() {
        currentState.complete();
    }
    
    /**
     * 取消
     */
    public void cancel() {
        currentState.cancel();
    }
    
    /**
     * 设置状态
     */
    public void setState(OrderState state) {
        this.currentState = state;
        log.info("🔄 订单 {} 状态变更为: {}", orderId, state.getStateName());
    }
    
    /**
     * 获取当前状态名称
     */
    public String getCurrentStateName() {
        return currentState.getStateName();
    }
    
    @Override
    public String toString() {
        return String.format("订单[ID: %s, 状态: %s]", orderId, getCurrentStateName());
    }
}
