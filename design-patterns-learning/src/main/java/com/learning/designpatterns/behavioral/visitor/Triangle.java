package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;

/**
 * 三角形类
 * 
 * <AUTHOR>
 */
@Getter
public class Triangle implements Shape {
    
    private final double a, b, c; // 三边长
    
    public Triangle(double a, double b, double c) {
        this.a = a;
        this.b = b;
        this.c = c;
    }
    
    @Override
    public void accept(ShapeVisitor visitor) {
        visitor.visit(this);
    }
}
