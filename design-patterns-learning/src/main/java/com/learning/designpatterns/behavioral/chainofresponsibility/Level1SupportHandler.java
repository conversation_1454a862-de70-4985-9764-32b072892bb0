package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 一级客服处理器
 * 
 * 处理低优先级的简单问题
 * 
 * <AUTHOR>
 */
@Slf4j
public class Level1SupportHandler extends SupportHandler {
    
    @Override
    protected boolean canHandle(SupportTicket ticket) {
        return ticket.getPriority() == TicketPriority.LOW;
    }
    
    @Override
    protected void resolve(SupportTicket ticket) {
        log.info("🎧 一级客服处理: {}", ticket);
        log.info("💡 提供标准解决方案");
        
        // 模拟处理时间
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("✅ 问题已解决，客户满意");
    }
    
    @Override
    protected String getHandlerName() {
        return "一级客服";
    }
}
