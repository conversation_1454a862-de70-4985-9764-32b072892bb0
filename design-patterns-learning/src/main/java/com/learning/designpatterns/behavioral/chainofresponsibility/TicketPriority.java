package com.learning.designpatterns.behavioral.chainofresponsibility;

/**
 * 工单优先级枚举
 * 
 * <AUTHOR>
 */
public enum TicketPriority {
    LOW(1, "低"),
    MEDIUM(2, "中"),
    HIGH(3, "高"),
    CRITICAL(4, "紧急");
    
    private final int level;
    private final String description;
    
    TicketPriority(int level, String description) {
        this.level = level;
        this.description = description;
    }
    
    public int getLevel() {
        return level;
    }
    
    @Override
    public String toString() {
        return description;
    }
}
