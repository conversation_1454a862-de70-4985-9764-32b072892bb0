package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;

/**
 * 段落类
 * 
 * <AUTHOR>
 */
@Getter
public class Paragraph implements DocumentElement {
    
    private final String content;
    
    public Paragraph(String content) {
        this.content = content;
    }
    
    @Override
    public void accept(DocumentElementVisitor visitor) {
        visitor.visit(this);
    }
}
