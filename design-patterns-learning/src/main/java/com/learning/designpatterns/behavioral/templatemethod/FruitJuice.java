package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 果汁类
 * 
 * 实现果汁的具体制作步骤
 * 
 * <AUTHOR>
 */
@Slf4j
public class FruitJuice extends Beverage {
    
    @Override
    protected void boilWater() {
        log.info("❄️ 准备冰水（果汁不需要热水）");
    }
    
    @Override
    protected void brew() {
        log.info("🥤 将新鲜水果榨汁");
        try {
            Thread.sleep(400); // 模拟榨汁时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("🍊 新鲜果汁制作完成");
    }
    
    @Override
    protected void addCondiments() {
        log.info("🧊 添加冰块");
        log.info("🍃 装饰薄荷叶");
    }
    
    @Override
    protected boolean customerWantsCondiments() {
        // 果汁通常都需要冰块和装饰
        return true;
    }
    
    @Override
    protected String getBeverageName() {
        return "果汁";
    }
}
