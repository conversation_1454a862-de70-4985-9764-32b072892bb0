package com.learning.designpatterns.behavioral.chainofresponsibility;

/**
 * 日志级别枚举
 * 
 * <AUTHOR>
 */
public enum LogLevel {
    INFO(1, "信息"),
    WARN(2, "警告"),
    ERROR(3, "错误"),
    FATAL(4, "致命错误");
    
    private final int priority;
    private final String description;
    
    LogLevel(int priority, String description) {
        this.priority = priority;
        this.description = description;
    }
    
    public int getPriority() {
        return priority;
    }
    
    @Override
    public String toString() {
        return description;
    }
}
