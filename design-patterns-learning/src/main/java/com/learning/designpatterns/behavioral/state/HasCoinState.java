package com.learning.designpatterns.behavioral.state;

import lombok.extern.slf4j.Slf4j;

/**
 * 有硬币状态
 * 
 * 用户已投币，等待选择商品的状态
 * 
 * <AUTHOR>
 */
@Slf4j
public class HasCoinState implements VendingMachineState {
    
    private final VendingMachine vendingMachine;
    
    public HasCoinState(VendingMachine vendingMachine) {
        this.vendingMachine = vendingMachine;
    }
    
    @Override
    public void insertCoin() {
        log.warn("❌ 已经投币，请选择商品");
    }
    
    @Override
    public void ejectCoin() {
        log.info("💰 退币成功");
        vendingMachine.setState(vendingMachine.getNoCoinState());
    }
    
    @Override
    public void selectProduct() {
        log.info("🎯 商品选择成功，正在出货...");
        vendingMachine.setState(vendingMachine.getSoldState());
    }
    
    @Override
    public void dispenseProduct() {
        log.warn("❌ 请先选择商品");
    }
    
    @Override
    public String getStateName() {
        return "已投币";
    }
}
