package com.learning.designpatterns.behavioral.observer;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 新闻订阅者观察者类
 * 
 * 实现了Observer接口，当订阅的新闻机构发布新闻时，
 * 订阅者会收到通知
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class NewsSubscriber implements Observer {
    
    /**
     * 订阅者用户名
     */
    private final String username;
    
    /**
     * 接收到的新闻列表
     */
    private final List<ReceivedNews> receivedNews;
    
    /**
     * 订阅偏好（关键词）
     */
    private final List<String> preferences;
    
    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 构造函数
     * 
     * @param username 订阅者用户名
     */
    public NewsSubscriber(String username) {
        this.username = username;
        this.receivedNews = new ArrayList<>();
        this.preferences = new ArrayList<>();
        log.info("用户 {} 已注册新闻订阅服务", username);
    }
    
    /**
     * 构造函数（带偏好设置）
     * 
     * @param username 订阅者用户名
     * @param preferences 订阅偏好关键词
     */
    public NewsSubscriber(String username, List<String> preferences) {
        this.username = username;
        this.receivedNews = new ArrayList<>();
        this.preferences = new ArrayList<>(preferences);
        log.info("用户 {} 已注册新闻订阅服务，偏好: {}", username, preferences);
    }
    
    @Override
    public void update(Subject subject) {
        if (subject instanceof NewsAgency newsAgency) {
            String news = newsAgency.getLatestNews();
            LocalDateTime publishTime = newsAgency.getPublishTime();
            String agencyName = newsAgency.getName();
            
            log.info("📱 用户 {} 收到来自 {} 的新闻推送", username, agencyName);
            
            // 检查新闻是否符合用户偏好
            if (isNewsRelevant(news)) {
                receiveNews(news, agencyName, publishTime);
            } else {
                log.info("⏭️ 用户 {} 跳过不感兴趣的新闻", username);
            }
        }
    }
    
    /**
     * 检查新闻是否符合用户偏好
     * 
     * @param news 新闻内容
     * @return 是否相关
     */
    private boolean isNewsRelevant(String news) {
        // 如果没有设置偏好，接收所有新闻
        if (preferences.isEmpty()) {
            return true;
        }
        
        // 检查新闻是否包含偏好关键词
        String newsLower = news.toLowerCase();
        for (String preference : preferences) {
            if (newsLower.contains(preference.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 接收新闻
     * 
     * @param news 新闻内容
     * @param agencyName 新闻机构名称
     * @param publishTime 发布时间
     */
    private void receiveNews(String news, String agencyName, LocalDateTime publishTime) {
        ReceivedNews receivedNewsItem = new ReceivedNews(news, agencyName, publishTime, LocalDateTime.now());
        receivedNews.add(receivedNewsItem);
        
        log.info("✅ 用户 {} 成功接收新闻: {}", username, news);
        
        // 模拟用户阅读行为
        simulateUserReaction(news);
    }
    
    /**
     * 模拟用户对新闻的反应
     * 
     * @param news 新闻内容
     */
    private void simulateUserReaction(String news) {
        // 根据新闻内容模拟不同的用户反应
        if (news.contains("突破") || news.contains("重大")) {
            log.info("😮 用户 {} 对新闻表示惊讶", username);
        } else if (news.contains("技术") || news.contains("科技")) {
            log.info("🤔 用户 {} 对技术新闻很感兴趣", username);
        } else if (news.contains("应用") || news.contains("商业化")) {
            log.info("💼 用户 {} 关注商业应用前景", username);
        } else {
            log.info("👍 用户 {} 点赞了这条新闻", username);
        }
    }
    
    /**
     * 添加订阅偏好
     * 
     * @param preference 偏好关键词
     */
    public void addPreference(String preference) {
        if (!preferences.contains(preference)) {
            preferences.add(preference);
            log.info("用户 {} 添加了新的订阅偏好: {}", username, preference);
        }
    }
    
    /**
     * 移除订阅偏好
     * 
     * @param preference 偏好关键词
     */
    public void removePreference(String preference) {
        if (preferences.remove(preference)) {
            log.info("用户 {} 移除了订阅偏好: {}", username, preference);
        }
    }
    
    /**
     * 获取接收到的新闻数量
     * 
     * @return 新闻数量
     */
    public int getReceivedNewsCount() {
        return receivedNews.size();
    }
    
    /**
     * 接收到的新闻内部类
     */
    @Getter
    public static class ReceivedNews {
        private final String content;
        private final String agencyName;
        private final LocalDateTime publishTime;
        private final LocalDateTime receiveTime;
        
        public ReceivedNews(String content, String agencyName, LocalDateTime publishTime, LocalDateTime receiveTime) {
            this.content = content;
            this.agencyName = agencyName;
            this.publishTime = publishTime;
            this.receiveTime = receiveTime;
        }
        
        @Override
        public String toString() {
            return String.format("[%s] %s (来源: %s, 发布: %s)", 
                    receiveTime.format(TIME_FORMATTER), 
                    content, 
                    agencyName, 
                    publishTime.format(TIME_FORMATTER));
        }
    }
    
    @Override
    public String toString() {
        return String.format("NewsSubscriber{username='%s', receivedNews=%d, preferences=%s}", 
                username, receivedNews.size(), preferences);
    }
}
