package com.learning.designpatterns.behavioral.visitor;

import lombok.extern.slf4j.Slf4j;

/**
 * Markdown导出访问者
 * 
 * <AUTHOR>
 */
@Slf4j
public class MarkdownExportVisitor implements DocumentElementVisitor {
    
    @Override
    public void visit(Paragraph paragraph) {
        log.info("Markdown: {}", paragraph.getContent());
    }
    
    @Override
    public void visit(Heading heading) {
        String prefix = "#".repeat(heading.getLevel());
        log.info("Markdown: {} {}", prefix, heading.getText());
    }
    
    @Override
    public void visit(Image image) {
        log.info("Markdown: ![{}]({})", image.getAlt(), image.getSrc());
    }
    
    @Override
    public void visit(Table table) {
        log.info("Markdown: | {} | ({}行表格)", String.join(" | ", table.getHeaders()), table.getRows().size());
    }
}
