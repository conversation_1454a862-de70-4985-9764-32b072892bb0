package com.learning.designpatterns.behavioral.strategy;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * 购物车类
 * 
 * 策略模式的上下文类，使用不同的促销策略计算总价
 * 
 * <AUTHOR>
 */
@Slf4j
public class ShoppingCart {
    
    private final List<Item> items;
    private PromotionStrategy promotionStrategy;
    
    /**
     * 构造函数
     */
    public ShoppingCart() {
        this.items = new ArrayList<>();
        this.promotionStrategy = new NoPromotionStrategy(); // 默认无促销
    }
    
    /**
     * 添加商品
     * 
     * @param name 商品名称
     * @param price 商品价格
     * @param quantity 数量
     */
    public void addItem(String name, double price, int quantity) {
        items.add(new Item(name, price, quantity));
        log.info("🛒 添加商品: {} x{} = ¥{}", name, quantity, price * quantity);
    }
    
    /**
     * 移除商品
     * 
     * @param name 商品名称
     */
    public void removeItem(String name) {
        items.removeIf(item -> item.getName().equals(name));
        log.info("🗑️ 移除商品: {}", name);
    }
    
    /**
     * 设置促销策略
     * 
     * @param promotionStrategy 促销策略
     */
    public void setPromotionStrategy(PromotionStrategy promotionStrategy) {
        this.promotionStrategy = promotionStrategy;
        log.info("🎯 应用促销策略: {}", promotionStrategy.getStrategyName());
        log.info("📝 促销说明: {}", promotionStrategy.getDescription());
    }
    
    /**
     * 计算总价
     * 
     * @return 促销后的总价
     */
    public double calculateTotal() {
        double originalTotal = items.stream()
                .mapToDouble(item -> item.getPrice() * item.getQuantity())
                .sum();
        
        double finalTotal = promotionStrategy.calculatePrice(items, originalTotal);
        double discount = originalTotal - finalTotal;
        
        log.info("💰 原价: ¥{:.2f}", originalTotal);
        if (discount > 0) {
            log.info("🎉 优惠金额: ¥{:.2f}", discount);
            log.info("💳 实付金额: ¥{:.2f}", finalTotal);
        } else {
            log.info("💳 实付金额: ¥{:.2f} (无优惠)", finalTotal);
        }
        
        return finalTotal;
    }
    
    /**
     * 显示购物车内容
     */
    public void showCart() {
        log.info("🛒 购物车内容:");
        if (items.isEmpty()) {
            log.info("   购物车为空");
        } else {
            for (Item item : items) {
                log.info("   • {} x{} = ¥{:.2f}", 
                        item.getName(), item.getQuantity(), 
                        item.getPrice() * item.getQuantity());
            }
        }
    }
    
    /**
     * 获取商品列表
     * 
     * @return 商品列表
     */
    public List<Item> getItems() {
        return new ArrayList<>(items);
    }
    
    /**
     * 商品内部类
     */
    @Getter
    public static class Item {
        private final String name;
        private final double price;
        private final int quantity;
        
        public Item(String name, double price, int quantity) {
            this.name = name;
            this.price = price;
            this.quantity = quantity;
        }
        
        @Override
        public String toString() {
            return String.format("%s(¥%.2f x%d)", name, price, quantity);
        }
    }
}
