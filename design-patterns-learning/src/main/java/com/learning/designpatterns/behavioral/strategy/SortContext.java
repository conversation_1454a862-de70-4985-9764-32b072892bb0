package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 排序上下文类
 * 
 * 策略模式的上下文类，维护对策略对象的引用
 * 
 * <AUTHOR>
 */
@Slf4j
public class SortContext {
    
    private SortStrategy sortStrategy;
    
    /**
     * 设置排序策略
     * 
     * @param sortStrategy 排序策略
     */
    public void setSortStrategy(SortStrategy sortStrategy) {
        this.sortStrategy = sortStrategy;
        log.info("🔧 切换排序策略: {} ({})", 
                sortStrategy.getAlgorithmName(), 
                sortStrategy.getTimeComplexity());
    }
    
    /**
     * 执行排序
     * 
     * @param data 待排序数据
     */
    public void sort(int[] data) {
        if (sortStrategy == null) {
            log.error("❌ 未设置排序策略");
            return;
        }
        
        log.info("📊 使用 {} 进行排序", sortStrategy.getAlgorithmName());
        sortStrategy.sort(data);
    }
}
