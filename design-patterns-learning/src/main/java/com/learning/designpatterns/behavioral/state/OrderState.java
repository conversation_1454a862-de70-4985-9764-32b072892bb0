package com.learning.designpatterns.behavioral.state;

/**
 * 订单状态接口
 * 
 * 定义了订单在不同状态下的行为
 * 
 * <AUTHOR>
 */
public interface OrderState {
    
    /**
     * 支付
     */
    void pay();
    
    /**
     * 发货
     */
    void ship();
    
    /**
     * 送达
     */
    void deliver();
    
    /**
     * 完成
     */
    void complete();
    
    /**
     * 取消
     */
    void cancel();
    
    /**
     * 获取状态名称
     * 
     * @return 状态名称
     */
    String getStateName();
}
