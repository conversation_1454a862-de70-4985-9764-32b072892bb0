package com.learning.designpatterns.behavioral.chainofresponsibility;

/**
 * 日志处理器抽象类
 * 
 * <AUTHOR>
 */
public abstract class LogHandler {
    
    protected LogHandler nextHandler;
    
    /**
     * 设置下一个处理器
     * 
     * @param nextHandler 下一个处理器
     */
    public void setNextHandler(LogHandler nextHandler) {
        this.nextHandler = nextHandler;
    }
    
    /**
     * 处理日志消息
     * 
     * @param message 日志消息
     */
    public void handleLog(LogMessage message) {
        if (canHandle(message)) {
            writeLog(message);
        }
        
        // 继续传递给下一个处理器
        if (nextHandler != null) {
            nextHandler.handleLog(message);
        }
    }
    
    /**
     * 判断是否能处理该日志级别
     * 
     * @param message 日志消息
     * @return 是否能处理
     */
    protected abstract boolean canHandle(LogMessage message);
    
    /**
     * 写入日志
     * 
     * @param message 日志消息
     */
    protected abstract void writeLog(LogMessage message);
}
