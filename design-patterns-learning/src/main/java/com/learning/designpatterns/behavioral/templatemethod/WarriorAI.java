package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 战士AI
 * 
 * <AUTHOR>
 */
@Slf4j
public class WarriorAI extends GameAI {
    
    @Override
    protected void analyzeSituation() {
        log.info("⚔️ 分析战斗情况");
        log.info("🛡️ 评估防御能力");
        log.info("💪 检查攻击力");
    }
    
    @Override
    protected void planStrategy() {
        log.info("🎯 制定近战策略");
        log.info("⚔️ 选择最佳攻击目标");
        log.info("🛡️ 规划防御路线");
    }
    
    @Override
    protected void executeAction() {
        log.info("⚔️ 发动近战攻击");
        log.info("🗡️ 使用剑术技能");
        log.info("🛡️ 保持防御姿态");
    }
    
    @Override
    protected String getAIType() {
        return "战士AI";
    }
}
