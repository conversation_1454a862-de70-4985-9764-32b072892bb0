package com.learning.designpatterns.behavioral.state;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 自动售货机类
 * 
 * 使用状态模式管理售货机的不同状态
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class VendingMachine {
    
    // 状态对象
    private final VendingMachineState noCoinState;
    private final VendingMachineState hasCoinState;
    private final VendingMachineState soldState;
    private final VendingMachineState soldOutState;
    
    // 当前状态
    private VendingMachineState currentState;
    
    // 商品库存
    private int inventory;
    
    public VendingMachine(int inventory) {
        this.inventory = inventory;
        
        // 初始化所有状态
        this.noCoinState = new NoCoinState(this);
        this.hasCoinState = new HasCoinState(this);
        this.soldState = new SoldState(this);
        this.soldOutState = new SoldOutState(this);
        
        // 设置初始状态
        if (inventory > 0) {
            this.currentState = noCoinState;
            log.info("🏪 售货机初始化完成，当前状态: 等待投币");
        } else {
            this.currentState = soldOutState;
            log.info("🏪 售货机初始化完成，当前状态: 商品售罄");
        }
    }
    
    /**
     * 投币
     */
    public void insertCoin() {
        currentState.insertCoin();
    }
    
    /**
     * 退币
     */
    public void ejectCoin() {
        currentState.ejectCoin();
    }
    
    /**
     * 选择商品
     */
    public void selectProduct() {
        currentState.selectProduct();
    }
    
    /**
     * 出货
     */
    public void dispenseProduct() {
        currentState.dispenseProduct();
    }
    
    /**
     * 补充库存
     */
    public void refill(int count) {
        log.info("📦 补充库存: {} 个商品", count);
        this.inventory += count;
        
        // 如果当前是售罄状态，切换到等待投币状态
        if (currentState == soldOutState && inventory > 0) {
            setState(noCoinState);
            log.info("✅ 库存补充完成，售货机恢复正常");
        }
    }
    
    /**
     * 设置状态
     */
    public void setState(VendingMachineState state) {
        this.currentState = state;
        log.info("🔄 状态切换到: {}", state.getStateName());
    }
    
    /**
     * 减少库存
     */
    public void releaseProduct() {
        if (inventory > 0) {
            inventory--;
            log.info("📦 出货成功，剩余库存: {}", inventory);
        }
    }
    
    /**
     * 获取当前状态名称
     */
    public String getCurrentStateName() {
        return currentState.getStateName();
    }
    
    @Override
    public String toString() {
        return String.format("售货机[状态: %s, 库存: %d]", getCurrentStateName(), inventory);
    }
}
