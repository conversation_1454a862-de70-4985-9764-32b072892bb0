package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 组长审批处理器
 * 
 * 可以审批1000元以下的申请
 * 
 * <AUTHOR>
 */
@Slf4j
public class TeamLeaderHandler extends ApprovalHandler {
    
    private static final double APPROVAL_LIMIT = 1000;
    
    @Override
    protected boolean canHandle(ApprovalRequest request) {
        return request.getAmount() <= APPROVAL_LIMIT;
    }
    
    @Override
    protected void approve(ApprovalRequest request) {
        log.info("👨‍💼 组长审批通过: {}", request);
        log.info("✅ 审批金额: ¥{} (权限: ≤¥{})", request.getAmount(), APPROVAL_LIMIT);
        
        // 模拟审批处理时间
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("📝 组长已签字确认，申请处理完成");
    }
    
    @Override
    protected String getHandlerName() {
        return "组长";
    }
}
