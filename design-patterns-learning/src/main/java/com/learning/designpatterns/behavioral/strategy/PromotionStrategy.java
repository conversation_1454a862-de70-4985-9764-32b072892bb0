package com.learning.designpatterns.behavioral.strategy;

import java.util.List;

/**
 * 促销策略接口
 * 
 * 定义了促销计算的统一接口
 * 
 * <AUTHOR>
 */
public interface PromotionStrategy {
    
    /**
     * 计算促销后的价格
     * 
     * @param items 商品列表
     * @param originalTotal 原始总价
     * @return 促销后的价格
     */
    double calculatePrice(List<ShoppingCart.Item> items, double originalTotal);
    
    /**
     * 获取促销策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 获取促销描述
     * 
     * @return 促销描述
     */
    String getDescription();
}
