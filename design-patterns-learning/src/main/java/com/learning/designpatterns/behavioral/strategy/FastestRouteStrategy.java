package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 最快路径策略
 * 
 * 优先选择用时最短的路径，通常选择高速公路
 * 
 * <AUTHOR>
 */
@Slf4j
public class FastestRouteStrategy implements RouteStrategy {
    
    @Override
    public RouteInfo planRoute(String start, String destination) {
        log.info("⚡ 计算最快路径...");
        
        // 模拟路径计算
        simulateCalculation();
        
        // 最快路径通常距离较长但时间较短，费用较高（高速费）
        double distance = calculateDistance(start, destination) * 1.2; // 高速路径稍长
        double duration = calculateBaseDuration(start, destination) * 0.7; // 时间最短
        double cost = calculateBaseCost(start, destination) * 1.5; // 费用较高（高速费）
        
        String routeName = String.format("%s -> %s (高速路线)", start, destination);
        String description = "优先选择高速公路，时间最短但过路费较高";
        
        log.info("🏎️ 最快路径规划完成");
        
        return new RouteInfo(routeName, distance, duration, cost, description);
    }
    
    @Override
    public String getStrategyName() {
        return "最快路径";
    }
    
    /**
     * 模拟路径计算过程
     */
    private void simulateCalculation() {
        try {
            log.info("🔍 分析交通状况...");
            Thread.sleep(200);
            
            log.info("🛣️ 搜索高速公路网络...");
            Thread.sleep(300);
            
            log.info("⚡ 优化行驶时间...");
            Thread.sleep(250);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("路径计算被中断", e);
        }
    }
    
    /**
     * 计算基础距离
     */
    private double calculateDistance(String start, String destination) {
        // 模拟距离计算（实际项目中会调用地图API）
        return 1200 + Math.random() * 100; // 1200-1300km
    }
    
    /**
     * 计算基础时间
     */
    private double calculateBaseDuration(String start, String destination) {
        // 模拟时间计算
        return 12 + Math.random() * 2; // 12-14小时
    }
    
    /**
     * 计算基础费用
     */
    private double calculateBaseCost(String start, String destination) {
        // 模拟费用计算
        return 800 + Math.random() * 200; // 800-1000元
    }
}
