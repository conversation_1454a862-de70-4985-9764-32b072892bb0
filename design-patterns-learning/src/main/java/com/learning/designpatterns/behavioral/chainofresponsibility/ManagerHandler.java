package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 经理审批处理器
 * 
 * 可以审批10000元以下的申请
 * 
 * <AUTHOR>
 */
@Slf4j
public class ManagerHandler extends ApprovalHandler {
    
    private static final double APPROVAL_LIMIT = 10000;
    
    @Override
    protected boolean canHandle(ApprovalRequest request) {
        return request.getAmount() <= APPROVAL_LIMIT;
    }
    
    @Override
    protected void approve(ApprovalRequest request) {
        log.info("👔 经理审批通过: {}", request);
        log.info("✅ 审批金额: ¥{} (权限: ≤¥{})", request.getAmount(), APPROVAL_LIMIT);
        
        // 模拟审批处理时间
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("📝 经理已签字确认，申请处理完成");
    }
    
    @Override
    protected String getHandlerName() {
        return "经理";
    }
}
