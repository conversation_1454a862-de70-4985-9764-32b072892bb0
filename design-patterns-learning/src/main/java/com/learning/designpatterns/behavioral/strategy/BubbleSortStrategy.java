package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;

/**
 * 冒泡排序策略
 * 
 * 实现冒泡排序算法
 * 时间复杂度：O(n²)，空间复杂度：O(1)
 * 
 * <AUTHOR>
 */
@Slf4j
public class BubbleSortStrategy implements SortStrategy {
    
    @Override
    public void sort(int[] data) {
        log.info("🫧 开始冒泡排序");
        log.info("原始数据: {}", Arrays.toString(data));
        
        long startTime = System.nanoTime();
        int comparisons = 0;
        int swaps = 0;
        
        int n = data.length;
        
        for (int i = 0; i < n - 1; i++) {
            boolean swapped = false;
            
            for (int j = 0; j < n - i - 1; j++) {
                comparisons++;
                
                if (data[j] > data[j + 1]) {
                    // 交换元素
                    int temp = data[j];
                    data[j] = data[j + 1];
                    data[j + 1] = temp;
                    swapped = true;
                    swaps++;
                }
            }
            
            // 如果没有发生交换，说明数组已经有序
            if (!swapped) {
                log.info("🎯 第{}轮后数组已有序，提前结束", i + 1);
                break;
            }
            
            log.info("第{}轮排序后: {}", i + 1, Arrays.toString(data));
        }
        
        long endTime = System.nanoTime();
        double duration = (endTime - startTime) / 1_000_000.0; // 转换为毫秒
        
        log.info("✅ 冒泡排序完成");
        log.info("排序结果: {}", Arrays.toString(data));
        log.info("📊 统计信息 - 比较次数: {}, 交换次数: {}, 耗时: {:.2f}ms", 
                comparisons, swaps, duration);
    }
    
    @Override
    public String getAlgorithmName() {
        return "冒泡排序";
    }
    
    @Override
    public String getTimeComplexity() {
        return "O(n²)";
    }
}
