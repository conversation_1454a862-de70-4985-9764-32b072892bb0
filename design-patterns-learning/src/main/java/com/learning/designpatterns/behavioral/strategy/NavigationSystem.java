package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 导航系统
 * 
 * 策略模式的上下文类，使用不同的路径规划策略
 * 
 * <AUTHOR>
 */
@Slf4j
public class NavigationSystem {
    
    private RouteStrategy routeStrategy;
    
    /**
     * 设置路径规划策略
     * 
     * @param routeStrategy 路径规划策略
     */
    public void setRouteStrategy(RouteStrategy routeStrategy) {
        this.routeStrategy = routeStrategy;
        log.info("🗺️ 切换导航策略: {}", routeStrategy.getStrategyName());
    }
    
    /**
     * 规划路径
     * 
     * @param start 起点
     * @param destination 终点
     * @return 路径信息
     */
    public RouteStrategy.RouteInfo planRoute(String start, String destination) {
        if (routeStrategy == null) {
            log.error("❌ 未设置路径规划策略");
            return null;
        }
        
        log.info("🚗 开始规划路径: {} -> {}", start, destination);
        
        RouteStrategy.RouteInfo routeInfo = routeStrategy.planRoute(start, destination);
        
        if (routeInfo != null) {
            log.info("✅ 路径规划完成: {}", routeInfo);
            displayRouteDetails(routeInfo);
        } else {
            log.error("❌ 路径规划失败");
        }
        
        return routeInfo;
    }
    
    /**
     * 显示路径详细信息
     * 
     * @param routeInfo 路径信息
     */
    private void displayRouteDetails(RouteStrategy.RouteInfo routeInfo) {
        log.info("📋 路径详情:");
        log.info("   🛣️ 路线: {}", routeInfo.getRouteName());
        log.info("   📏 距离: {:.1f} 公里", routeInfo.getDistance());
        log.info("   ⏱️ 预计时间: {:.1f} 小时", routeInfo.getDuration());
        log.info("   💰 预计费用: ¥{:.0f}", routeInfo.getCost());
        log.info("   📝 说明: {}", routeInfo.getDescription());
    }
}
