package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.Getter;

/**
 * 客服支持工单
 * 
 * <AUTHOR>
 */
@Getter
public class SupportTicket {
    
    private final String customer;
    private final String description;
    private final TicketPriority priority;
    
    public SupportTicket(String customer, String description, TicketPriority priority) {
        this.customer = customer;
        this.description = description;
        this.priority = priority;
    }
    
    @Override
    public String toString() {
        return String.format("客户: %s, 优先级: %s, 问题: %s", customer, priority, description);
    }
}
