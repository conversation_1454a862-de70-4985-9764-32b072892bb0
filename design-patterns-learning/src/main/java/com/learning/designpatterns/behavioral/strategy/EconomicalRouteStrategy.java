package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 经济路径策略
 * 
 * 优先选择费用最低的路径，避免高速公路和收费站
 * 
 * <AUTHOR>
 */
@Slf4j
public class EconomicalRouteStrategy implements RouteStrategy {
    
    @Override
    public RouteInfo planRoute(String start, String destination) {
        log.info("💰 计算最经济路径...");
        
        // 模拟路径计算
        simulateCalculation();
        
        // 经济路径费用最低但距离和时间可能较长
        double distance = calculateDistance(start, destination) * 1.15; // 距离稍长
        double duration = calculateBaseDuration(start, destination) * 1.3; // 时间较长
        double cost = calculateBaseCost(start, destination) * 0.6; // 费用最低
        
        String routeName = String.format("%s -> %s (经济路线)", start, destination);
        String description = "避开高速公路和收费站，费用最低但耗时较长";
        
        log.info("💸 经济路径规划完成");
        
        return new RouteInfo(routeName, distance, duration, cost, description);
    }
    
    @Override
    public String getStrategyName() {
        return "经济路径";
    }
    
    /**
     * 模拟路径计算过程
     */
    private void simulateCalculation() {
        try {
            log.info("💳 分析收费站分布...");
            Thread.sleep(250);
            
            log.info("🚫 避开高速公路...");
            Thread.sleep(200);
            
            log.info("🛣️ 寻找免费道路...");
            Thread.sleep(300);
            
            log.info("⛽ 优化油耗路线...");
            Thread.sleep(180);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("路径计算被中断", e);
        }
    }
    
    /**
     * 计算基础距离
     */
    private double calculateDistance(String start, String destination) {
        // 模拟距离计算
        return 1200 + Math.random() * 100; // 1200-1300km
    }
    
    /**
     * 计算基础时间
     */
    private double calculateBaseDuration(String start, String destination) {
        // 模拟时间计算
        return 12 + Math.random() * 2; // 12-14小时
    }
    
    /**
     * 计算基础费用
     */
    private double calculateBaseCost(String start, String destination) {
        // 模拟费用计算
        return 800 + Math.random() * 200; // 800-1000元
    }
}
