package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件日志处理器
 * 
 * 处理WARN级别及以上的日志，写入文件
 * 
 * <AUTHOR>
 */
@Slf4j
public class FileLogHandler extends LogHandler {
    
    @Override
    protected boolean canHandle(LogMessage message) {
        // 只处理WARN级别及以上的日志
        return message.getLevel().getPriority() >= LogLevel.WARN.getPriority();
    }
    
    @Override
    protected void writeLog(LogMessage message) {
        String filename = getLogFilename(message.getLevel());
        log.info("📁 写入文件 {}: {}", filename, message.getFormattedMessage());
        
        // 模拟文件写入操作
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private String getLogFilename(LogLevel level) {
        return switch (level) {
            case WARN -> "warning.log";
            case ERROR -> "error.log";
            case FATAL -> "fatal.log";
            default -> "application.log";
        };
    }
}
