package com.learning.designpatterns.behavioral.state;

/**
 * 售货机状态接口
 * 
 * 定义了售货机在不同状态下的行为
 * 
 * <AUTHOR>
 */
public interface VendingMachineState {
    
    /**
     * 投币
     */
    void insertCoin();
    
    /**
     * 退币
     */
    void ejectCoin();
    
    /**
     * 选择商品
     */
    void selectProduct();
    
    /**
     * 出货
     */
    void dispenseProduct();
    
    /**
     * 获取状态名称
     * 
     * @return 状态名称
     */
    String getStateName();
}
