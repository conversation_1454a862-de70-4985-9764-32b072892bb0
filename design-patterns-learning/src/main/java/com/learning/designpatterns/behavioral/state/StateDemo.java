package com.learning.designpatterns.behavioral.state;

import lombok.extern.slf4j.Slf4j;

/**
 * 状态模式演示类
 * 
 * 状态模式允许对象在内部状态改变时改变它的行为，对象看起来好像修改了它的类。
 * 
 * 应用场景：
 * - 自动售货机状态管理
 * - 订单状态流转
 * - 游戏角色状态
 * - 音乐播放器状态
 * - 网络连接状态
 * 
 * <AUTHOR>
 */
@Slf4j
public class StateDemo {
    
    public static void main(String[] args) {
        log.info("=== 状态模式演示 ===");
        
        // 演示自动售货机
        demonstrateVendingMachine();

        // 演示其他状态模式应用
        demonstrateOtherStateApplications();
    }
    
    private static void demonstrateVendingMachine() {
        log.info("\n--- 自动售货机状态演示 ---");
        
        VendingMachine machine = new VendingMachine(3); // 3个商品库存
        
        log.info("🏪 自动售货机初始化完成，库存: 3个商品");
        
        // 第一次购买
        log.info("\n💰 第一次购买流程:");
        machine.insertCoin();
        machine.selectProduct();
        machine.dispenseProduct();
        
        // 第二次购买
        log.info("\n💰 第二次购买流程:");
        machine.insertCoin();
        machine.selectProduct();
        machine.dispenseProduct();
        
        // 第三次购买
        log.info("\n💰 第三次购买流程:");
        machine.insertCoin();
        machine.selectProduct();
        machine.dispenseProduct();
        
        // 第四次购买（库存不足）
        log.info("\n💰 第四次购买流程（库存不足）:");
        machine.insertCoin();
        machine.selectProduct();
        machine.dispenseProduct();
        
        // 补充库存
        log.info("\n📦 补充库存:");
        machine.refill(5);
        
        // 补充库存后购买
        log.info("\n💰 补充库存后购买:");
        machine.insertCoin();
        machine.selectProduct();
        machine.dispenseProduct();
    }
    
    private static void demonstrateOtherStateApplications() {
        log.info("\n--- 其他状态模式应用场景 ---");

        log.info("📦 订单状态管理:");
        log.info("   • 待支付 -> 已支付 -> 已发货 -> 已送达 -> 已完成");
        log.info("   • 任何状态都可以取消（除已完成）");
        log.info("   • 每个状态有不同的可执行操作");

        log.info("\n🎵 音乐播放器状态:");
        log.info("   • 停止 -> 播放 -> 暂停 -> 播放");
        log.info("   • 不同状态下按钮行为不同");
        log.info("   • 状态决定了可用的操作");

        log.info("\n🎮 游戏角色状态:");
        log.info("   • 正常 -> 受伤 -> 死亡 -> 复活");
        log.info("   • 不同状态下技能和移动能力不同");
        log.info("   • 状态影响角色的所有行为");

        log.info("\n🌐 网络连接状态:");
        log.info("   • 断开 -> 连接中 -> 已连接 -> 断开");
        log.info("   • 每个状态有不同的重连策略");
        log.info("   • 状态决定了网络操作的可用性");

        log.info("\n💡 状态模式的优势:");
        log.info("   ✅ 将状态相关的行为封装到状态类中");
        log.info("   ✅ 消除了大量的条件判断语句");
        log.info("   ✅ 状态转换逻辑清晰明确");
        log.info("   ✅ 易于添加新的状态和行为");
    }
}
