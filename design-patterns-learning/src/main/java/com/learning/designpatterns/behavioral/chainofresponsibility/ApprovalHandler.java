package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 审批处理器抽象类
 * 
 * 定义了责任链的基本结构
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class ApprovalHandler {
    
    protected ApprovalHandler nextHandler;
    
    /**
     * 设置下一个处理器
     * 
     * @param nextHandler 下一个处理器
     */
    public void setNextHandler(ApprovalHandler nextHandler) {
        this.nextHandler = nextHandler;
    }
    
    /**
     * 处理审批请求
     * 
     * @param request 审批请求
     */
    public void handleRequest(ApprovalRequest request) {
        if (canHandle(request)) {
            approve(request);
        } else if (nextHandler != null) {
            log.info("🔄 {} 无权限处理，转交给上级", getHandlerName());
            nextHandler.handleRequest(request);
        } else {
            log.warn("❌ 申请金额过大，无人有权限审批: ¥{}", request.getAmount());
        }
    }
    
    /**
     * 判断是否能处理该请求
     * 
     * @param request 审批请求
     * @return 是否能处理
     */
    protected abstract boolean canHandle(ApprovalRequest request);
    
    /**
     * 审批请求
     * 
     * @param request 审批请求
     */
    protected abstract void approve(ApprovalRequest request);
    
    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    protected abstract String getHandlerName();
}
