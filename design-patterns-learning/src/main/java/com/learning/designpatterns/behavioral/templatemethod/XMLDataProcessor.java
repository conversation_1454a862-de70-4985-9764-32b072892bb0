package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * XML数据处理器
 * 
 * <AUTHOR>
 */
@Slf4j
public class XMLDataProcessor extends DataProcessor {
    
    @Override
    protected String readData(String source) {
        log.info("📄 从XML文件读取数据: {}", source);
        return "<users><user><name>John</name><age>25</age></user></users>";
    }
    
    @Override
    protected Object parseData(String rawData) {
        log.info("🔧 解析XML格式数据");
        return "解析后的XML数据";
    }
    
    @Override
    protected Object transformData(Object parsedData) {
        log.info("🔄 转换XML数据为标准格式");
        return "转换后的XML数据";
    }
    
    @Override
    protected void saveData(Object data) {
        log.info("💾 将处理后的XML数据保存到关系型数据库");
    }
    
    @Override
    protected String getDataFormat() {
        return "XML";
    }
}
