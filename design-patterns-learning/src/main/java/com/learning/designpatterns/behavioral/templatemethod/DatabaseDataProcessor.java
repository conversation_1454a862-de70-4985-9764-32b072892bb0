package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库数据处理器
 * 
 * <AUTHOR>
 */
@Slf4j
public class DatabaseDataProcessor extends DataProcessor {
    
    @Override
    protected String readData(String source) {
        log.info("🗄️ 从数据库表读取数据: {}", source);
        return "数据库查询结果";
    }
    
    @Override
    protected Object parseData(String rawData) {
        log.info("🔧 解析数据库查询结果");
        return "解析后的数据库数据";
    }
    
    @Override
    protected Object transformData(Object parsedData) {
        log.info("🔄 转换数据库数据");
        return "转换后的数据库数据";
    }
    
    @Override
    protected void saveData(Object data) {
        log.info("💾 将处理后的数据保存到数据仓库");
    }
    
    @Override
    protected boolean shouldGenerateReport() {
        return false; // 数据库处理不需要额外报告
    }
    
    @Override
    protected String getDataFormat() {
        return "数据库";
    }
}
