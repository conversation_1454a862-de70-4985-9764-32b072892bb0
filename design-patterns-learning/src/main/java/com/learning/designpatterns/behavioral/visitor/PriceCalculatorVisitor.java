package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 价格计算访问者
 * 
 * 计算不同类型商品的价格和折扣
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class PriceCalculatorVisitor implements CartItemVisitor {
    
    private double totalPrice = 0.0;
    private double totalDiscount = 0.0;
    
    @Override
    public void visit(Book book) {
        double discount = calculateBookDiscount(book);
        double finalPrice = book.getPrice() - discount;
        
        log.info("📚 {} - 原价: ¥{}, 折扣: ¥{}, 实付: ¥{}", 
                book.getName(), book.getPrice(), discount, finalPrice);
        
        totalPrice += book.getPrice();
        totalDiscount += discount;
    }
    
    @Override
    public void visit(Electronics electronics) {
        double discount = calculateElectronicsDiscount(electronics);
        double finalPrice = electronics.getPrice() - discount;
        
        log.info("📱 {} - 原价: ¥{}, 折扣: ¥{}, 实付: ¥{}", 
                electronics.getName(), electronics.getPrice(), discount, finalPrice);
        
        totalPrice += electronics.getPrice();
        totalDiscount += discount;
    }
    
    @Override
    public void visit(Clothing clothing) {
        double discount = calculateClothingDiscount(clothing);
        double finalPrice = clothing.getPrice() - discount;
        
        log.info("👕 {} - 原价: ¥{}, 折扣: ¥{}, 实付: ¥{}", 
                clothing.getName(), clothing.getPrice(), discount, finalPrice);
        
        totalPrice += clothing.getPrice();
        totalDiscount += discount;
    }
    
    /**
     * 计算图书折扣
     */
    private double calculateBookDiscount(Book book) {
        // 图书类商品9折优惠
        return book.getPrice() * 0.1;
    }
    
    /**
     * 计算电子产品折扣
     */
    private double calculateElectronicsDiscount(Electronics electronics) {
        // 电子产品满5000减500
        if (electronics.getPrice() >= 5000) {
            return 500.0;
        }
        return 0.0;
    }
    
    /**
     * 计算服装折扣
     */
    private double calculateClothingDiscount(Clothing clothing) {
        // 服装类商品8.5折优惠
        return clothing.getPrice() * 0.15;
    }
    
    /**
     * 获取最终价格
     */
    public double getFinalPrice() {
        return totalPrice - totalDiscount;
    }
}
