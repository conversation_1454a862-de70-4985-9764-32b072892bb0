package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;
import java.util.List;

/**
 * 表格类
 * 
 * <AUTHOR>
 */
@Getter
public class Table implements DocumentElement {
    
    private final List<String> headers;
    private final List<List<String>> rows;
    
    public Table(List<String> headers, List<List<String>> rows) {
        this.headers = headers;
        this.rows = rows;
    }
    
    @Override
    public void accept(DocumentElementVisitor visitor) {
        visitor.visit(this);
    }
}
