package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.util.Random;

/**
 * 库存检查访问者
 * 
 * 检查不同类型商品的库存状态
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class InventoryCheckVisitor implements CartItemVisitor {
    
    private int inStockCount = 0;
    private int outOfStockCount = 0;
    private final Random random = new Random();
    
    @Override
    public void visit(Book book) {
        boolean inStock = checkBookInventory(book);
        String status = inStock ? "有库存" : "缺货";
        log.info("📚 {} - 库存状态: {}", book.getName(), status);
        
        if (inStock) {
            inStockCount++;
        } else {
            outOfStockCount++;
        }
    }
    
    @Override
    public void visit(Electronics electronics) {
        boolean inStock = checkElectronicsInventory(electronics);
        String status = inStock ? "有库存" : "缺货";
        log.info("📱 {} - 库存状态: {}", electronics.getName(), status);
        
        if (inStock) {
            inStockCount++;
        } else {
            outOfStockCount++;
        }
    }
    
    @Override
    public void visit(Clothing clothing) {
        boolean inStock = checkClothingInventory(clothing);
        String status = inStock ? "有库存" : "缺货";
        log.info("👕 {} - 库存状态: {}", clothing.getName(), status);
        
        if (inStock) {
            inStockCount++;
        } else {
            outOfStockCount++;
        }
    }
    
    /**
     * 检查图书库存
     */
    private boolean checkBookInventory(Book book) {
        // 模拟库存检查，图书库存充足概率80%
        return random.nextDouble() < 0.8;
    }
    
    /**
     * 检查电子产品库存
     */
    private boolean checkElectronicsInventory(Electronics electronics) {
        // 模拟库存检查，电子产品库存充足概率60%
        return random.nextDouble() < 0.6;
    }
    
    /**
     * 检查服装库存
     */
    private boolean checkClothingInventory(Clothing clothing) {
        // 模拟库存检查，服装库存充足概率70%
        return random.nextDouble() < 0.7;
    }
    
    /**
     * 检查是否所有商品都有库存
     */
    public boolean isAllInStock() {
        return outOfStockCount == 0;
    }
}
