package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.Getter;

/**
 * 审批请求类
 * 
 * <AUTHOR>
 */
@Getter
public class ApprovalRequest {
    
    private final String applicant;
    private final double amount;
    private final String description;
    private final RequestType type;
    
    public ApprovalRequest(String applicant, double amount, String description, RequestType type) {
        this.applicant = applicant;
        this.amount = amount;
        this.description = description;
        this.type = type;
    }
    
    @Override
    public String toString() {
        return String.format("申请人: %s, 金额: ¥%.0f, 类型: %s, 描述: %s", 
                applicant, amount, type, description);
    }
}
