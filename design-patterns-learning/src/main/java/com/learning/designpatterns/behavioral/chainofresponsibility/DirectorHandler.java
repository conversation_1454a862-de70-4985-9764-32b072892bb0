package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 总监审批处理器
 * 
 * 可以审批100000元以下的申请
 * 
 * <AUTHOR>
 */
@Slf4j
public class DirectorHandler extends ApprovalHandler {
    
    private static final double APPROVAL_LIMIT = 100000;
    
    @Override
    protected boolean canHandle(ApprovalRequest request) {
        return request.getAmount() <= APPROVAL_LIMIT;
    }
    
    @Override
    protected void approve(ApprovalRequest request) {
        log.info("🎩 总监审批通过: {}", request);
        log.info("✅ 审批金额: ¥{} (权限: ≤¥{})", request.getAmount(), APPROVAL_LIMIT);
        
        // 模拟审批处理时间
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("📝 总监已签字确认，申请处理完成");
    }
    
    @Override
    protected String getHandlerName() {
        return "总监";
    }
}
