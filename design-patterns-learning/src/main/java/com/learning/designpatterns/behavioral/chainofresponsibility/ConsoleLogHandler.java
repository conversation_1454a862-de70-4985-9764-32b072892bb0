package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 控制台日志处理器
 * 
 * 处理所有级别的日志，输出到控制台
 * 
 * <AUTHOR>
 */
@Slf4j
public class ConsoleLogHandler extends LogHandler {
    
    @Override
    protected boolean canHandle(LogMessage message) {
        // 控制台处理所有级别的日志
        return true;
    }
    
    @Override
    protected void writeLog(LogMessage message) {
        String icon = getLogIcon(message.getLevel());
        log.info("🖥️ 控制台输出: {} {}", icon, message.getFormattedMessage());
    }
    
    private String getLogIcon(LogLevel level) {
        return switch (level) {
            case INFO -> "ℹ️";
            case WARN -> "⚠️";
            case ERROR -> "❌";
            case FATAL -> "💀";
        };
    }
}
