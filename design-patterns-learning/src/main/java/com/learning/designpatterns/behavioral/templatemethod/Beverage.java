package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * 饮料制作抽象类
 * 
 * 定义了饮料制作的模板方法
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class Beverage {
    
    /**
     * 模板方法：定义饮料制作的完整流程
     * 这个方法是final的，子类不能重写
     */
    public final void prepareBeverage() {
        log.info("🍹 开始制作 {}", getBeverageName());
        
        // 1. 煮水
        boilWater();
        
        // 2. 冲泡（抽象方法，由子类实现）
        brew();
        
        // 3. 倒入杯子
        pourInCup();
        
        // 4. 添加调料（钩子方法，子类可以选择是否重写）
        if (customerWantsCondiments()) {
            addCondiments();
        }
        
        // 5. 完成制作
        finishPreparation();
        
        log.info("✅ {} 制作完成！", getBeverageName());
    }
    
    /**
     * 煮水 - 所有饮料都需要煮水
     */
    protected void boilWater() {
        log.info("💧 正在煮水...");
        try {
            Thread.sleep(500); // 模拟煮水时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("🔥 水已煮开");
    }
    
    /**
     * 冲泡 - 抽象方法，由子类实现具体的冲泡方式
     */
    protected abstract void brew();
    
    /**
     * 倒入杯子 - 所有饮料都需要倒入杯子
     */
    protected void pourInCup() {
        log.info("🥤 将饮料倒入杯子");
    }
    
    /**
     * 添加调料 - 抽象方法，由子类实现具体的调料
     */
    protected abstract void addCondiments();
    
    /**
     * 钩子方法：客户是否需要调料
     * 默认返回true，子类可以重写
     */
    protected boolean customerWantsCondiments() {
        return true;
    }
    
    /**
     * 完成制作
     */
    protected void finishPreparation() {
        log.info("🎉 饮料制作工艺完成");
    }
    
    /**
     * 获取饮料名称 - 抽象方法
     */
    protected abstract String getBeverageName();
}
