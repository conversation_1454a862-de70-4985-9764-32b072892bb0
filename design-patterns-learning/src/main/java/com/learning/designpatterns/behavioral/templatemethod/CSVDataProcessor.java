package com.learning.designpatterns.behavioral.templatemethod;

import lombok.extern.slf4j.Slf4j;

/**
 * CSV数据处理器
 * 
 * 实现CSV格式数据的具体处理步骤
 * 
 * <AUTHOR>
 */
@Slf4j
public class CSVDataProcessor extends DataProcessor {
    
    @Override
    protected String readData(String source) {
        log.info("📄 从CSV文件读取数据: {}", source);
        // 模拟读取CSV文件
        return "name,age,email\nJohn,25,<EMAIL>\nJane,30,<EMAIL>";
    }
    
    @Override
    protected Object parseData(String rawData) {
        log.info("🔧 解析CSV格式数据");
        // 模拟CSV解析
        String[] lines = rawData.split("\n");
        log.info("📊 解析出 {} 行数据", lines.length);
        return lines;
    }
    
    @Override
    protected Object transformData(Object parsedData) {
        log.info("🔄 转换CSV数据为标准格式");
        String[] lines = (String[]) parsedData;
        // 模拟数据转换
        StringBuilder transformed = new StringBuilder();
        for (int i = 1; i < lines.length; i++) { // 跳过标题行
            String[] fields = lines[i].split(",");
            transformed.append(String.format("用户: %s, 年龄: %s, 邮箱: %s\n", 
                    fields[0], fields[1], fields[2]));
        }
        return transformed.toString();
    }
    
    @Override
    protected void saveData(Object data) {
        log.info("💾 将处理后的CSV数据保存到数据库");
        log.info("📝 保存内容预览: {}", data.toString().substring(0, 
                Math.min(50, data.toString().length())) + "...");
    }
    
    @Override
    protected String getDataFormat() {
        return "CSV";
    }
}
