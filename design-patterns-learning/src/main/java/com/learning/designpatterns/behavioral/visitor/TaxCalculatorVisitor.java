package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 税费计算访问者
 * 
 * 计算不同类型商品的税费
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class TaxCalculatorVisitor implements CartItemVisitor {
    
    private double productTax = 0.0;
    private double serviceTax = 0.0;
    
    @Override
    public void visit(Book book) {
        // 图书免税
        double tax = 0.0;
        log.info("📚 {} - 税费: ¥{} (图书免税)", book.getName(), tax);
        // 图书不产生商品税或服务税
    }
    
    @Override
    public void visit(Electronics electronics) {
        // 电子产品13%增值税
        double tax = electronics.getPrice() * 0.13;
        log.info("📱 {} - 税费: ¥{:.2f} (13%增值税)", electronics.getName(), tax);
        productTax += tax;
    }
    
    @Override
    public void visit(Clothing clothing) {
        // 服装10%消费税
        double tax = clothing.getPrice() * 0.10;
        log.info("👕 {} - 税费: ¥{:.2f} (10%消费税)", clothing.getName(), tax);
        productTax += tax;
    }
    
    /**
     * 获取总税费
     */
    public double getTotalTax() {
        return productTax + serviceTax;
    }
}
