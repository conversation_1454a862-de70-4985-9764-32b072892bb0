package com.learning.designpatterns.behavioral.strategy;

/**
 * 路径规划策略接口
 * 
 * 定义了路径规划的统一接口
 * 
 * <AUTHOR>
 */
public interface RouteStrategy {
    
    /**
     * 规划路径
     * 
     * @param start 起点
     * @param destination 终点
     * @return 路径信息
     */
    RouteInfo planRoute(String start, String destination);
    
    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 路径信息内部类
     */
    class RouteInfo {
        private final String routeName;
        private final double distance;
        private final double duration;
        private final double cost;
        private final String description;
        
        public RouteInfo(String routeName, double distance, double duration, double cost, String description) {
            this.routeName = routeName;
            this.distance = distance;
            this.duration = duration;
            this.cost = cost;
            this.description = description;
        }
        
        // Getters
        public String getRouteName() { return routeName; }
        public double getDistance() { return distance; }
        public double getDuration() { return duration; }
        public double getCost() { return cost; }
        public String getDescription() { return description; }
        
        @Override
        public String toString() {
            return String.format("%s: %.1fkm, %.1f小时, ¥%.0f - %s", 
                    routeName, distance, duration, cost, description);
        }
    }
}
