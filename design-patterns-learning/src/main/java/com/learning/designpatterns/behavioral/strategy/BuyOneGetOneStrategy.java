package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 买一送一促销策略
 * 
 * 数量大于1的商品享受买一送一优惠
 * 
 * <AUTHOR>
 */
@Slf4j
public class BuyOneGetOneStrategy implements PromotionStrategy {
    
    @Override
    public double calculatePrice(List<ShoppingCart.Item> items, double originalTotal) {
        double finalTotal = 0;
        double totalDiscount = 0;
        
        log.info("🎁 买一送一计算:");
        
        for (ShoppingCart.Item item : items) {
            int quantity = item.getQuantity();
            double price = item.getPrice();
            
            if (quantity > 1) {
                // 买一送一：只需要支付一半的数量（向上取整）
                int payableQuantity = (quantity + 1) / 2;
                double itemTotal = payableQuantity * price;
                double itemDiscount = (quantity - payableQuantity) * price;
                
                finalTotal += itemTotal;
                totalDiscount += itemDiscount;
                
                log.info("   • {} x{} -> 实付x{}, 优惠 ¥{:.2f}", 
                        item.getName(), quantity, payableQuantity, itemDiscount);
            } else {
                // 数量为1，无优惠
                double itemTotal = quantity * price;
                finalTotal += itemTotal;
                log.info("   • {} x{} -> 无优惠", item.getName(), quantity);
            }
        }
        
        if (totalDiscount > 0) {
            log.info("🎉 买一送一总优惠: ¥{:.2f}", totalDiscount);
        } else {
            log.info("❌ 无符合买一送一条件的商品");
        }
        
        return finalTotal;
    }
    
    @Override
    public String getStrategyName() {
        return "买一送一";
    }
    
    @Override
    public String getDescription() {
        return "数量大于1的商品享受买一送一优惠";
    }
}
