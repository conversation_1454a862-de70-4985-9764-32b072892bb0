package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 客服支持处理器抽象类
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class SupportHandler {
    
    protected SupportHandler nextHandler;
    
    /**
     * 设置下一个处理器
     * 
     * @param nextHandler 下一个处理器
     */
    public void setNextHandler(SupportHandler nextHandler) {
        this.nextHandler = nextHandler;
    }
    
    /**
     * 处理支持工单
     * 
     * @param ticket 支持工单
     */
    public void handleTicket(SupportTicket ticket) {
        if (canHandle(ticket)) {
            resolve(ticket);
        } else if (nextHandler != null) {
            log.info("🔄 {} 无法处理，升级到上级", getHandlerName());
            nextHandler.handleTicket(ticket);
        } else {
            log.warn("❌ 问题过于复杂，需要专家介入: {}", ticket.getDescription());
        }
    }
    
    /**
     * 判断是否能处理该工单
     * 
     * @param ticket 支持工单
     * @return 是否能处理
     */
    protected abstract boolean canHandle(SupportTicket ticket);
    
    /**
     * 解决工单
     * 
     * @param ticket 支持工单
     */
    protected abstract void resolve(SupportTicket ticket);
    
    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    protected abstract String getHandlerName();
}
