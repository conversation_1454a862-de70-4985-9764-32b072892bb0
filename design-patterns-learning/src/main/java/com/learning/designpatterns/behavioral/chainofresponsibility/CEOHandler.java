package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * CEO审批处理器
 * 
 * 可以审批1000000元以下的申请
 * 
 * <AUTHOR>
 */
@Slf4j
public class CEOHandler extends ApprovalHandler {
    
    private static final double APPROVAL_LIMIT = 1000000;
    
    @Override
    protected boolean canHandle(ApprovalRequest request) {
        return request.getAmount() <= APPROVAL_LIMIT;
    }
    
    @Override
    protected void approve(ApprovalRequest request) {
        log.info("👑 CEO审批通过: {}", request);
        log.info("✅ 审批金额: ¥{} (权限: ≤¥{})", request.getAmount(), APPROVAL_LIMIT);
        
        // 模拟审批处理时间
        try {
            Thread.sleep(800);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("📝 CEO已签字确认，申请处理完成");
        log.info("🎉 重大决策已通过，将立即执行");
    }
    
    @Override
    protected String getHandlerName() {
        return "CEO";
    }
}
