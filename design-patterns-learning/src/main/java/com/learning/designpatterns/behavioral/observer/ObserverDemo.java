package com.learning.designpatterns.behavioral.observer;

import lombok.extern.slf4j.Slf4j;

/**
 * 观察者模式演示类
 * 
 * 观察者模式定义了对象之间的一对多依赖关系，当一个对象的状态发生改变时，
 * 所有依赖于它的对象都会得到通知并自动更新。
 * 
 * 应用场景：
 * - 股票价格监控系统
 * - 新闻订阅系统
 * - 事件监听机制
 * - MVC架构中的模型-视图关系
 * - 消息推送系统
 * 
 * <AUTHOR>
 */
@Slf4j
public class ObserverDemo {
    
    public static void main(String[] args) {
        log.info("=== 观察者模式演示 ===");
        
        // 演示股票价格监控系统
        demonstrateStockPriceMonitoring();
        
        // 演示新闻订阅系统
        demonstrateNewsSubscription();
    }
    
    private static void demonstrateStockPriceMonitoring() {
        log.info("\n--- 股票价格监控系统演示 ---");
        
        // 创建股票主题
        StockPrice appleStock = new StockPrice("AAPL", 150.0);
        
        // 创建观察者（投资者）
        Investor investor1 = new Investor("张三");
        Investor investor2 = new Investor("李四");
        StockAnalyst analyst = new StockAnalyst("王分析师");
        
        // 注册观察者
        appleStock.addObserver(investor1);
        appleStock.addObserver(investor2);
        appleStock.addObserver(analyst);
        
        // 股票价格变化
        appleStock.setPrice(155.0);
        appleStock.setPrice(148.0);
        
        // 移除一个观察者
        appleStock.removeObserver(investor1);
        log.info("张三取消了对 {} 的关注", appleStock.getSymbol());
        
        // 再次变化价格
        appleStock.setPrice(160.0);
    }
    
    private static void demonstrateNewsSubscription() {
        log.info("\n--- 新闻订阅系统演示 ---");
        
        // 创建新闻发布者
        NewsAgency newsAgency = new NewsAgency("科技日报");
        
        // 创建订阅者
        NewsSubscriber subscriber1 = new NewsSubscriber("用户A");
        NewsSubscriber subscriber2 = new NewsSubscriber("用户B");
        NewsSubscriber subscriber3 = new NewsSubscriber("用户C");
        
        // 订阅新闻
        newsAgency.addObserver(subscriber1);
        newsAgency.addObserver(subscriber2);
        newsAgency.addObserver(subscriber3);
        
        // 发布新闻
        newsAgency.publishNews("人工智能技术取得重大突破");
        newsAgency.publishNews("新型电池技术延长手机续航至一周");
        
        // 用户B取消订阅
        newsAgency.removeObserver(subscriber2);
        log.info("用户B 取消了对 {} 的订阅", newsAgency.getName());
        
        // 再次发布新闻
        newsAgency.publishNews("量子计算机实现商业化应用");
    }
}
