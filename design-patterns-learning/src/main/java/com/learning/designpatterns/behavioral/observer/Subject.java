package com.learning.designpatterns.behavioral.observer;

/**
 * 主题接口（被观察者）
 * 
 * 定义了主题的基本行为，包括添加、移除和通知观察者
 * 
 * <AUTHOR>
 */
public interface Subject {
    
    /**
     * 添加观察者
     * 
     * @param observer 要添加的观察者
     */
    void addObserver(Observer observer);
    
    /**
     * 移除观察者
     * 
     * @param observer 要移除的观察者
     */
    void removeObserver(Observer observer);
    
    /**
     * 通知所有观察者
     * 
     * 当主题状态发生变化时，调用此方法通知所有注册的观察者
     */
    void notifyObservers();
}
