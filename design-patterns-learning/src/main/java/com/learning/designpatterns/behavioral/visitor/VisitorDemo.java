package com.learning.designpatterns.behavioral.visitor;

import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;
import java.util.List;

/**
 * 访问者模式演示类
 * 
 * 访问者模式表示一个作用于某对象结构中的各元素的操作。
 * 它使你可以在不改变各元素的类的前提下定义作用于这些元素的新操作。
 * 
 * 应用场景：
 * - 编译器的语法树遍历
 * - 文档结构的不同格式导出
 * - 购物车中不同商品的价格计算
 * - 文件系统的不同操作（搜索、备份、统计）
 * - 图形对象的渲染和计算
 * 
 * <AUTHOR>
 */
@Slf4j
public class VisitorDemo {
    
    public static void main(String[] args) {
        log.info("=== 访问者模式演示 ===");
        
        // 演示购物车系统
        demonstrateShoppingCart();
        
        // 演示文档处理系统
        demonstrateDocumentProcessing();
        
        // 演示图形计算系统
        demonstrateShapeCalculation();
    }
    
    private static void demonstrateShoppingCart() {
        log.info("\n--- 购物车访问者模式演示 ---");
        
        // 创建购物车商品
        List<CartItem> cartItems = Arrays.asList(
            new Book("Java编程思想", 89.0, "Bruce Eckel", "978-0131872486"),
            new Electronics("iPhone 15", 6999.0, "Apple", 24),
            new Clothing("Nike运动鞋", 599.0, "Nike", "42", "黑色"),
            new Book("设计模式", 65.0, "GoF", "978-0201633610"),
            new Electronics("MacBook Pro", 12999.0, "Apple", 12),
            new Clothing("Adidas T恤", 199.0, "Adidas", "L", "白色")
        );
        
        log.info("🛒 购物车包含 {} 件商品", cartItems.size());
        
        // 价格计算访问者
        log.info("\n💰 计算购物车总价:");
        PriceCalculatorVisitor priceCalculator = new PriceCalculatorVisitor();
        for (CartItem item : cartItems) {
            item.accept(priceCalculator);
        }
        log.info("📊 购物车统计:");
        log.info("   总价: ¥{}", priceCalculator.getTotalPrice());
        log.info("   折扣: ¥{}", priceCalculator.getTotalDiscount());
        log.info("   实付: ¥{}", priceCalculator.getFinalPrice());
        
        // 税费计算访问者
        log.info("\n🧾 计算税费:");
        TaxCalculatorVisitor taxCalculator = new TaxCalculatorVisitor();
        for (CartItem item : cartItems) {
            item.accept(taxCalculator);
        }
        log.info("📊 税费统计:");
        log.info("   商品税: ¥{}", taxCalculator.getProductTax());
        log.info("   服务税: ¥{}", taxCalculator.getServiceTax());
        log.info("   总税费: ¥{}", taxCalculator.getTotalTax());
        
        // 库存检查访问者
        log.info("\n📦 检查库存:");
        InventoryCheckVisitor inventoryChecker = new InventoryCheckVisitor();
        for (CartItem item : cartItems) {
            item.accept(inventoryChecker);
        }
        log.info("📊 库存统计:");
        log.info("   有库存商品: {} 件", inventoryChecker.getInStockCount());
        log.info("   缺货商品: {} 件", inventoryChecker.getOutOfStockCount());
        log.info("   库存充足: {}", inventoryChecker.isAllInStock() ? "是" : "否");
    }
    
    private static void demonstrateDocumentProcessing() {
        log.info("\n--- 文档处理访问者模式演示 ---");
        
        // 创建文档结构
        List<DocumentElement> document = Arrays.asList(
            new Paragraph("这是一个关于设计模式的文档。访问者模式是一种行为型设计模式。"),
            new Heading("访问者模式介绍", 1),
            new Paragraph("访问者模式允许在不修改现有类的情况下添加新的操作。"),
            new Image("visitor-pattern-diagram.png", "访问者模式类图"),
            new Heading("实现要点", 2),
            new Paragraph("1. 定义访问者接口\n2. 实现具体访问者\n3. 元素接受访问者"),
            new Table(Arrays.asList("模式名称", "类型", "难度"), 
                     Arrays.asList(
                         Arrays.asList("访问者模式", "行为型", "中等"),
                         Arrays.asList("观察者模式", "行为型", "简单"),
                         Arrays.asList("策略模式", "行为型", "简单")
                     ))
        );
        
        log.info("📄 文档包含 {} 个元素", document.size());
        
        // HTML导出访问者
        log.info("\n🌐 导出为HTML格式:");
        HTMLExportVisitor htmlExporter = new HTMLExportVisitor();
        for (DocumentElement element : document) {
            element.accept(htmlExporter);
        }
        log.info("✅ HTML导出完成");
        
        // Markdown导出访问者
        log.info("\n📝 导出为Markdown格式:");
        MarkdownExportVisitor markdownExporter = new MarkdownExportVisitor();
        for (DocumentElement element : document) {
            element.accept(markdownExporter);
        }
        log.info("✅ Markdown导出完成");
        
        // 统计访问者
        log.info("\n📊 文档统计:");
        DocumentStatisticsVisitor statisticsVisitor = new DocumentStatisticsVisitor();
        for (DocumentElement element : document) {
            element.accept(statisticsVisitor);
        }
        log.info("📈 统计结果:");
        log.info("   段落数: {}", statisticsVisitor.getParagraphCount());
        log.info("   标题数: {}", statisticsVisitor.getHeadingCount());
        log.info("   图片数: {}", statisticsVisitor.getImageCount());
        log.info("   表格数: {}", statisticsVisitor.getTableCount());
        log.info("   总字数: {}", statisticsVisitor.getTotalWords());
    }
    
    private static void demonstrateShapeCalculation() {
        log.info("\n--- 图形计算访问者模式演示 ---");
        
        // 创建图形对象
        List<Shape> shapes = Arrays.asList(
            new Circle(5.0),
            new Rectangle(4.0, 6.0),
            new Triangle(3.0, 4.0, 5.0),
            new Circle(3.0),
            new Rectangle(8.0, 3.0)
        );
        
        log.info("🔷 图形集合包含 {} 个图形", shapes.size());
        
        // 面积计算访问者
        log.info("\n📐 计算图形面积:");
        AreaCalculatorVisitor areaCalculator = new AreaCalculatorVisitor();
        for (Shape shape : shapes) {
            shape.accept(areaCalculator);
        }
        log.info("📊 面积统计:");
        log.info("   总面积: {:.2f}", areaCalculator.getTotalArea());
        log.info("   平均面积: {:.2f}", areaCalculator.getAverageArea());
        
        // 周长计算访问者
        log.info("\n📏 计算图形周长:");
        PerimeterCalculatorVisitor perimeterCalculator = new PerimeterCalculatorVisitor();
        for (Shape shape : shapes) {
            shape.accept(perimeterCalculator);
        }
        log.info("📊 周长统计:");
        log.info("   总周长: {:.2f}", perimeterCalculator.getTotalPerimeter());
        log.info("   平均周长: {:.2f}", perimeterCalculator.getAveragePerimeter());
        
        // 绘制访问者
        log.info("\n🎨 绘制图形:");
        DrawingVisitor drawingVisitor = new DrawingVisitor();
        for (Shape shape : shapes) {
            shape.accept(drawingVisitor);
        }
        log.info("✅ 所有图形绘制完成");
        
        log.info("\n💡 访问者模式的优势:");
        log.info("   ✅ 易于添加新的操作");
        log.info("   ✅ 相关操作集中在访问者中");
        log.info("   ✅ 可以跨越类层次结构");
        log.info("   ✅ 符合开闭原则");
    }
}
