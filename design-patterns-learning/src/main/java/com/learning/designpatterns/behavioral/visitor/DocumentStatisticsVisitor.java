package com.learning.designpatterns.behavioral.visitor;

import lombok.Getter;

/**
 * 文档统计访问者
 * 
 * <AUTHOR>
 */
@Getter
public class DocumentStatisticsVisitor implements DocumentElementVisitor {
    
    private int paragraphCount = 0;
    private int headingCount = 0;
    private int imageCount = 0;
    private int tableCount = 0;
    private int totalWords = 0;
    
    @Override
    public void visit(Paragraph paragraph) {
        paragraphCount++;
        totalWords += countWords(paragraph.getContent());
    }
    
    @Override
    public void visit(Heading heading) {
        headingCount++;
        totalWords += countWords(heading.getText());
    }
    
    @Override
    public void visit(Image image) {
        imageCount++;
    }
    
    @Override
    public void visit(Table table) {
        tableCount++;
        // 简化统计，只计算表头的字数
        for (String header : table.getHeaders()) {
            totalWords += countWords(header);
        }
    }
    
    private int countWords(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 0;
        }
        return text.trim().split("\\s+").length;
    }
}
