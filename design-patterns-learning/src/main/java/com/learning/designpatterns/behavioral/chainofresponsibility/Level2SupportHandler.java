package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 二级客服处理器
 * 
 * 处理中等优先级的技术问题
 * 
 * <AUTHOR>
 */
@Slf4j
public class Level2SupportHandler extends SupportHandler {
    
    @Override
    protected boolean canHandle(SupportTicket ticket) {
        return ticket.getPriority() == TicketPriority.MEDIUM;
    }
    
    @Override
    protected void resolve(SupportTicket ticket) {
        log.info("🔧 二级客服处理: {}", ticket);
        log.info("🛠️ 进行技术诊断和修复");
        
        // 模拟处理时间
        try {
            Thread.sleep(400);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("✅ 技术问题已修复");
    }
    
    @Override
    protected String getHandlerName() {
        return "二级客服";
    }
}
