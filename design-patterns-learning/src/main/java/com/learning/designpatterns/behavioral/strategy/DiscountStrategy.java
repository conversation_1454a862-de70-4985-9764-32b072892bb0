package com.learning.designpatterns.behavioral.strategy;

import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 折扣促销策略
 * 
 * 按比例打折
 * 
 * <AUTHOR>
 */
@Slf4j
public class DiscountStrategy implements PromotionStrategy {
    
    private final double discountRate;
    
    /**
     * 构造函数
     * 
     * @param discountRate 折扣率（0.1表示9折）
     */
    public DiscountStrategy(double discountRate) {
        this.discountRate = discountRate;
    }
    
    @Override
    public double calculatePrice(List<ShoppingCart.Item> items, double originalTotal) {
        double discount = originalTotal * discountRate;
        double finalPrice = originalTotal - discount;
        
        log.info("🏷️ 折扣计算: 原价 ¥{:.2f} × {:.0f}% = 优惠 ¥{:.2f}", 
                originalTotal, discountRate * 100, discount);
        
        return finalPrice;
    }
    
    @Override
    public String getStrategyName() {
        return String.format("%.0f折优惠", (1 - discountRate) * 10);
    }
    
    @Override
    public String getDescription() {
        return String.format("全场%.0f折，立减%.0f%%", (1 - discountRate) * 10, discountRate * 100);
    }
}
