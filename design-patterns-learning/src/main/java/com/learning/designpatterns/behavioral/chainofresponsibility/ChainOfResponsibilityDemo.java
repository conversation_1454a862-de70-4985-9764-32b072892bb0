package com.learning.designpatterns.behavioral.chainofresponsibility;

import lombok.extern.slf4j.Slf4j;

/**
 * 责任链模式演示类
 * 
 * 责任链模式为请求创建了一个接收者对象的链。这种模式给予请求的类型，
 * 对请求的发送者和接收者进行解耦。
 * 
 * 应用场景：
 * - 审批流程（请假、报销、采购等）
 * - 异常处理链
 * - 日志级别处理
 * - 过滤器链（Web过滤器、安全过滤器）
 * - 中间件处理
 * 
 * <AUTHOR>
 */
@Slf4j
public class ChainOfResponsibilityDemo {
    
    public static void main(String[] args) {
        log.info("=== 责任链模式演示 ===");
        
        // 演示审批流程
        demonstrateApprovalChain();
        
        // 演示日志处理链
        demonstrateLoggerChain();
        
        // 演示客服支持链
        demonstrateSupportChain();
    }
    
    private static void demonstrateApprovalChain() {
        log.info("\n--- 审批流程责任链演示 ---");
        
        // 构建审批责任链
        ApprovalHandler teamLeader = new TeamLeaderHandler();
        ApprovalHandler manager = new ManagerHandler();
        ApprovalHandler director = new DirectorHandler();
        ApprovalHandler ceo = new CEOHandler();
        
        // 设置责任链
        teamLeader.setNextHandler(manager);
        manager.setNextHandler(director);
        director.setNextHandler(ceo);
        
        // 创建不同金额的申请
        log.info("🏢 构建审批责任链: 组长 -> 经理 -> 总监 -> CEO");
        
        // 小额申请
        log.info("\n💰 处理小额申请:");
        ApprovalRequest smallRequest = new ApprovalRequest("张三", 800, "购买办公用品", RequestType.PURCHASE);
        teamLeader.handleRequest(smallRequest);
        
        // 中等金额申请
        log.info("\n💰 处理中等金额申请:");
        ApprovalRequest mediumRequest = new ApprovalRequest("李四", 8000, "购买办公设备", RequestType.PURCHASE);
        teamLeader.handleRequest(mediumRequest);
        
        // 大额申请
        log.info("\n💰 处理大额申请:");
        ApprovalRequest largeRequest = new ApprovalRequest("王五", 80000, "系统升级项目", RequestType.PROJECT);
        teamLeader.handleRequest(largeRequest);
        
        // 超大额申请
        log.info("\n💰 处理超大额申请:");
        ApprovalRequest hugeRequest = new ApprovalRequest("赵六", 500000, "新产品研发", RequestType.PROJECT);
        teamLeader.handleRequest(hugeRequest);
    }
    
    private static void demonstrateLoggerChain() {
        log.info("\n--- 日志处理责任链演示 ---");
        
        // 构建日志处理责任链
        LogHandler consoleLogger = new ConsoleLogHandler();
        LogHandler fileLogger = new FileLogHandler();
        LogHandler emailLogger = new EmailLogHandler();
        
        // 设置责任链
        consoleLogger.setNextHandler(fileLogger);
        fileLogger.setNextHandler(emailLogger);
        
        log.info("📝 构建日志处理链: 控制台 -> 文件 -> 邮件");
        
        // 不同级别的日志
        log.info("\n📊 处理不同级别的日志:");
        
        LogMessage infoMsg = new LogMessage(LogLevel.INFO, "系统启动成功");
        consoleLogger.handleLog(infoMsg);
        
        LogMessage warnMsg = new LogMessage(LogLevel.WARN, "内存使用率较高");
        consoleLogger.handleLog(warnMsg);
        
        LogMessage errorMsg = new LogMessage(LogLevel.ERROR, "数据库连接失败");
        consoleLogger.handleLog(errorMsg);
        
        LogMessage fatalMsg = new LogMessage(LogLevel.FATAL, "系统崩溃");
        consoleLogger.handleLog(fatalMsg);
    }
    
    private static void demonstrateSupportChain() {
        log.info("\n--- 客服支持责任链演示 ---");
        
        // 构建客服支持责任链
        SupportHandler level1 = new Level1SupportHandler();
        SupportHandler level2 = new Level2SupportHandler();
        SupportHandler level3 = new Level3SupportHandler();
        
        // 设置责任链
        level1.setNextHandler(level2);
        level2.setNextHandler(level3);
        
        log.info("🎧 构建客服支持链: 一级客服 -> 二级客服 -> 三级客服");
        
        // 不同复杂度的问题
        log.info("\n❓ 处理不同复杂度的客服问题:");
        
        SupportTicket simpleTicket = new SupportTicket("用户A", "如何重置密码？", TicketPriority.LOW);
        level1.handleTicket(simpleTicket);
        
        SupportTicket mediumTicket = new SupportTicket("用户B", "支付功能异常", TicketPriority.MEDIUM);
        level1.handleTicket(mediumTicket);
        
        SupportTicket complexTicket = new SupportTicket("用户C", "数据同步问题", TicketPriority.HIGH);
        level1.handleTicket(complexTicket);
        
        SupportTicket criticalTicket = new SupportTicket("用户D", "系统安全漏洞", TicketPriority.CRITICAL);
        level1.handleTicket(criticalTicket);
    }
}
