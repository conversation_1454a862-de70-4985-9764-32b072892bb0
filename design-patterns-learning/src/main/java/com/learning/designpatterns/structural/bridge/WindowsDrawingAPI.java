package com.learning.designpatterns.structural.bridge;

import lombok.extern.slf4j.Slf4j;

/**
 * Windows绘制API实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class WindowsDrawingAPI implements DrawingAPI {
    
    @Override
    public void drawCircle(double x, double y, double radius) {
        // 模拟Windows GDI+的具体绘制实现
        log.info("🪟 Windows GDI+ API:");
        log.info("   CreateGraphics()");
        log.info("   SetSmoothingMode(SmoothingModeAntiAlias)");
        log.info("   DrawEllipse(pen, {}, {}, {}, {})", x-radius, y-radius, radius*2, radius*2);
        log.info("   ✅ 圆形绘制完成 - 使用硬件加速");
    }

    @Override
    public void drawRectangle(double x, double y, double width, double height) {
        // 模拟Windows GDI+的具体绘制实现
        log.info("🪟 Windows GDI+ API:");
        log.info("   CreateGraphics()");
        log.info("   SetCompositingQuality(CompositingQualityHighQuality)");
        log.info("   DrawRectangle(pen, {}, {}, {}, {})", x, y, width, height);
        log.info("   ✅ 矩形绘制完成 - 使用DirectX加速");
    }

    @Override
    public void drawTriangle(double x1, double y1, double x2, double y2, double x3, double y3) {
        // 模拟Windows GDI+的具体绘制实现
        log.info("🪟 Windows GDI+ API:");
        log.info("   CreateGraphics()");
        log.info("   Point[] points = {{({}, {})}, {({}, {})}, {({}, {})}}}", x1, y1, x2, y2, x3, y3);
        log.info("   DrawPolygon(pen, points)");
        log.info("   ✅ 三角形绘制完成 - 使用矢量渲染");
    }

    @Override
    public void drawLine(double x1, double y1, double x2, double y2) {
        // 模拟Windows GDI+的具体绘制实现
        log.info("🪟 Windows GDI+ API:");
        log.info("   CreateGraphics()");
        log.info("   DrawLine(pen, {}, {}, {}, {})", x1, y1, x2, y2);
        log.info("   ✅ 线条绘制完成 - 使用抗锯齿");
    }
    
    @Override
    public String getAPIName() {
        return "Windows GDI+";
    }
}
