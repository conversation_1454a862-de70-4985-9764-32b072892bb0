package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 装饰器模式演示类
 * 
 * 装饰器模式允许向一个现有的对象添加新的功能，同时又不改变其结构。
 * 这种类型的设计模式属于结构型模式，它是作为现有的类的一个包装。
 * 
 * 应用场景：
 * - 咖啡店点餐系统（基础咖啡+各种配料）
 * - 文本编辑器（基础文本+加粗、斜体、下划线等格式）
 * - 数据流处理（基础流+压缩、加密、缓冲等功能）
 * - 图形界面组件（基础组件+边框、滚动条等装饰）
 * - Web请求处理（基础处理+日志、缓存、认证等中间件）
 * 
 * <AUTHOR>
 */
@Slf4j
public class DecoratorDemo {
    
    public static void main(String[] args) {
        log.info("=== 装饰器模式演示 ===");
        
        // 演示咖啡装饰器
        demonstrateCoffeeDecorator();
        
        // 演示文本装饰器
        demonstrateTextDecorator();
        
        // 演示数据流装饰器
        demonstrateDataStreamDecorator();
    }
    
    private static void demonstrateCoffeeDecorator() {
        log.info("\n--- 咖啡装饰器演示 ---");
        
        // 基础咖啡
        Coffee coffee = new SimpleCoffee();
        log.info("☕ 基础咖啡: {} - ¥{:.2f}", coffee.getDescription(), coffee.getCost());
        
        // 添加牛奶
        coffee = new MilkDecorator(coffee);
        log.info("🥛 加牛奶: {} - ¥{:.2f}", coffee.getDescription(), coffee.getCost());
        
        // 添加糖
        coffee = new SugarDecorator(coffee);
        log.info("🍯 加糖: {} - ¥{:.2f}", coffee.getDescription(), coffee.getCost());
        
        // 添加巧克力
        coffee = new ChocolateDecorator(coffee);
        log.info("🍫 加巧克力: {} - ¥{:.2f}", coffee.getDescription(), coffee.getCost());
        
        // 再添加一份牛奶
        coffee = new MilkDecorator(coffee);
        log.info("🥛 再加牛奶: {} - ¥{:.2f}", coffee.getDescription(), coffee.getCost());
        
        log.info("✅ 最终咖啡: {} - 总价: ¥{:.2f}", coffee.getDescription(), coffee.getCost());
        
        // 创建另一种组合
        log.info("\n--- 另一种咖啡组合 ---");
        Coffee espresso = new SimpleCoffee();
        espresso = new ChocolateDecorator(espresso);
        espresso = new ChocolateDecorator(espresso); // 双倍巧克力
        espresso = new SugarDecorator(espresso);
        
        log.info("☕ 双倍巧克力咖啡: {} - ¥{:.2f}", espresso.getDescription(), espresso.getCost());
    }
    
    private static void demonstrateTextDecorator() {
        log.info("\n--- 文本装饰器演示 ---");
        
        // 基础文本
        Text text = new PlainText("Hello World");
        log.info("📝 基础文本: {}", text.render());
        
        // 添加加粗
        text = new BoldDecorator(text);
        log.info("🔤 加粗文本: {}", text.render());
        
        // 添加斜体
        text = new ItalicDecorator(text);
        log.info("📐 斜体文本: {}", text.render());
        
        // 添加下划线
        text = new UnderlineDecorator(text);
        log.info("📏 下划线文本: {}", text.render());
        
        // 添加颜色
        text = new ColorDecorator(text, "红色");
        log.info("🎨 彩色文本: {}", text.render());
        
        log.info("✅ 最终文本效果: {}", text.render());
        
        // 创建另一种组合
        log.info("\n--- 另一种文本组合 ---");
        Text title = new PlainText("重要标题");
        title = new BoldDecorator(title);
        title = new ColorDecorator(title, "蓝色");
        title = new UnderlineDecorator(title);
        
        log.info("📰 标题效果: {}", title.render());
    }
    
    private static void demonstrateDataStreamDecorator() {
        log.info("\n--- 数据流装饰器演示 ---");
        
        String originalData = "这是一段重要的数据，需要安全传输和存储。";
        
        // 基础数据流
        DataStream stream = new BasicDataStream(originalData);
        log.info("📄 原始数据: {}", stream.read());
        
        // 添加压缩
        stream = new CompressionDecorator(stream);
        log.info("🗜️ 压缩后: {}", stream.read());
        
        // 添加加密
        stream = new EncryptionDecorator(stream);
        log.info("🔐 加密后: {}", stream.read());
        
        // 添加Base64编码
        stream = new Base64Decorator(stream);
        log.info("📊 Base64编码后: {}", stream.read());
        
        log.info("✅ 最终处理结果: {}", stream.read());
        
        // 演示写入操作
        log.info("\n--- 数据写入演示 ---");
        String newData = "新的数据内容";
        stream.write(newData);
        log.info("📝 写入新数据后: {}", stream.read());
    }
}
