package com.learning.designpatterns.structural.adapter;

import lombok.extern.slf4j.Slf4j;

/**
 * 适配器模式演示类
 * 
 * 适配器模式允许接口不兼容的类一起工作，它把一个类的接口变换成客户端所期待的另一种接口。
 * 
 * 应用场景：
 * - 系统集成：新旧系统接口不兼容
 * - 第三方库集成：第三方库接口与系统不匹配
 * - 数据格式转换：XML到JSON，不同数据库驱动
 * - 媒体播放器：支持不同格式的音频/视频文件
 * - 电源适配器：不同电压标准的转换
 * 
 * <AUTHOR>
 */
@Slf4j
public class AdapterDemo {
    
    public static void main(String[] args) {
        log.info("=== 适配器模式演示 ===");
        
        // 演示媒体播放器适配器
        demonstrateMediaPlayerAdapter();
        
        // 演示数据格式适配器
        demonstrateDataFormatAdapter();
        
        // 演示支付系统适配器
        demonstratePaymentAdapter();
    }
    
    private static void demonstrateMediaPlayerAdapter() {
        log.info("\n--- 媒体播放器适配器演示 ---");
        
        // 创建音频播放器
        AudioPlayer audioPlayer = new AudioPlayer();
        
        // 播放不同格式的音频文件
        audioPlayer.play("mp3", "song.mp3");
        audioPlayer.play("mp4", "movie.mp4");
        audioPlayer.play("vlc", "video.vlc");
        audioPlayer.play("avi", "movie.avi");
        audioPlayer.play("mkv", "video.mkv"); // 不支持的格式
    }
    
    private static void demonstrateDataFormatAdapter() {
        log.info("\n--- 数据格式适配器演示 ---");
        
        // 原始XML数据处理器
        XmlDataProcessor xmlProcessor = new XmlDataProcessor();
        
        // 使用适配器让XML处理器支持JSON
        JsonDataProcessor jsonProcessor = new XmlToJsonAdapter(xmlProcessor);
        
        String jsonData = "{\"name\":\"张三\",\"age\":25,\"city\":\"北京\"}";
        jsonProcessor.processJson(jsonData);
        
        // 直接处理XML数据
        String xmlData = "<person><name>李四</name><age>30</age><city>上海</city></person>";
        xmlProcessor.processXml(xmlData);
    }
    
    private static void demonstratePaymentAdapter() {
        log.info("\n--- 支付系统适配器演示 ---");
        
        // 现代支付系统
        ModernPaymentSystem modernSystem = new ModernPaymentSystem();
        
        // 使用适配器集成旧的支付系统
        PaymentProcessor legacyAdapter = new LegacyPaymentAdapter();
        
        // 统一的支付接口
        PaymentGateway gateway = new PaymentGateway();
        
        // 添加不同的支付处理器
        gateway.addProcessor("modern", modernSystem);
        gateway.addProcessor("legacy", legacyAdapter);
        
        // 使用不同的支付系统处理支付
        gateway.processPayment("modern", 100.0, "USD");
        gateway.processPayment("legacy", 200.0, "CNY");
    }
}
