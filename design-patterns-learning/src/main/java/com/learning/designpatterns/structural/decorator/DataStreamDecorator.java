package com.learning.designpatterns.structural.decorator;

/**
 * 数据流装饰器抽象类
 * 
 * 装饰器模式的抽象装饰器类，实现DataStream接口
 * 并持有一个DataStream对象的引用
 * 
 * <AUTHOR>
 */
public abstract class DataStreamDecorator implements DataStream {
    
    /**
     * 被装饰的数据流对象
     */
    protected DataStream dataStream;
    
    /**
     * 构造函数
     * 
     * @param dataStream 被装饰的数据流对象
     */
    public DataStreamDecorator(DataStream dataStream) {
        this.dataStream = dataStream;
    }
    
    @Override
    public String read() {
        return dataStream.read();
    }
    
    @Override
    public void write(String data) {
        dataStream.write(data);
    }
}
