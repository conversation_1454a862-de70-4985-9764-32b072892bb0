package com.learning.designpatterns.structural.adapter;

import lombok.extern.slf4j.Slf4j;

/**
 * 遗留支付系统
 * 
 * 这是一个旧的支付系统，接口与现代系统不兼容
 * 需要通过适配器来集成到新系统中
 * 
 * <AUTHOR>
 */
@Slf4j
public class LegacyPaymentSystem {
    
    /**
     * 旧系统的支付方法
     * 注意：方法名和参数与现代系统不同
     * 
     * @param amountInCents 以分为单位的金额
     * @param currencyCode 货币代码
     * @return 支付结果代码（0=成功，其他=失败）
     */
    public int makePayment(int amountInCents, String currencyCode) {
        log.info("🏛️ 遗留支付系统处理支付请求");
        log.info("💰 金额: {} 分 ({})", amountInCents, currencyCode);
        
        try {
            // 模拟旧系统的处理流程
            log.info("📞 拨号连接到银行主机...");
            Thread.sleep(800); // 旧系统比较慢
            
            log.info("📋 填写支付表单...");
            Thread.sleep(600);
            
            log.info("🖨️ 打印支付凭证...");
            Thread.sleep(400);
            
            log.info("📠 传真确认到银行...");
            Thread.sleep(500);
            
            // 模拟支付结果（85%成功率）
            boolean success = Math.random() > 0.15;
            
            if (success) {
                log.info("✅ 遗留支付系统支付成功！");
                log.info("📄 支付凭证已打印");
                return 0; // 成功返回0
            } else {
                log.warn("❌ 遗留支付系统支付失败：银行拒绝交易");
                return 1001; // 失败返回错误代码
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("遗留支付系统处理被中断", e);
            return 9999; // 系统错误
        }
    }
    
    /**
     * 获取系统版本信息
     */
    public String getSystemVersion() {
        return "Legacy Payment System v1.0 (1995)";
    }
}
