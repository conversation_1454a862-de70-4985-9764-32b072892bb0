package com.learning.designpatterns.structural.flyweight;

import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;

/**
 * 字符享元工厂
 * 
 * 创建并管理享元对象，确保合理地共享
 * 
 * <AUTHOR>
 */
@Slf4j
public class CharacterFlyweightFactory {
    
    private static CharacterFlyweightFactory instance;
    private final Map<Character, CharacterFlyweight> flyweights;
    
    private CharacterFlyweightFactory() {
        this.flyweights = new HashMap<>();
        log.info("🏭 字符享元工厂初始化");
    }
    
    /**
     * 获取工厂单例
     * 
     * @return 工厂实例
     */
    public static synchronized CharacterFlyweightFactory getInstance() {
        if (instance == null) {
            instance = new CharacterFlyweightFactory();
        }
        return instance;
    }
    
    /**
     * 获取字符享元对象
     * 
     * @param character 字符
     * @return 字符享元对象
     */
    public CharacterFlyweight getFlyweight(char character) {
        CharacterFlyweight flyweight = flyweights.get(character);
        
        if (flyweight == null) {
            // 如果不存在，创建新的享元对象
            flyweight = new ConcreteCharacterFlyweight(character);
            flyweights.put(character, flyweight);
            log.debug("🆕 创建新的字符享元: '{}', 当前享元池大小: {}", character, flyweights.size());
        } else {
            log.debug("♻️ 复用现有字符享元: '{}'", character);
        }
        
        return flyweight;
    }
    
    /**
     * 获取已创建的享元对象数量
     * 
     * @return 享元对象数量
     */
    public int getCreatedFlyweightsCount() {
        return flyweights.size();
    }
    
    /**
     * 获取享元池的详细信息
     */
    public void printFlyweightPoolInfo() {
        log.info("📊 享元池信息:");
        log.info("   享元对象总数: {}", flyweights.size());
        log.info("   包含的字符: {}", flyweights.keySet());
    }
    
    /**
     * 清空享元池（主要用于测试）
     */
    public void clear() {
        flyweights.clear();
        log.info("🗑️ 享元池已清空");
    }
}
