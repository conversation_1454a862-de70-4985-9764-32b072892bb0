package com.learning.designpatterns.structural.proxy;

import lombok.extern.slf4j.Slf4j;

/**
 * 代理模式演示类
 * 
 * 代理模式为其他对象提供一种代理以控制对这个对象的访问。
 * 代理对象在客户端和目标对象之间起到中介的作用。
 * 
 * 应用场景：
 * - 虚拟代理：延迟加载大对象
 * - 保护代理：控制访问权限
 * - 远程代理：访问远程对象
 * - 缓存代理：缓存昂贵操作的结果
 * - 日志代理：记录方法调用
 * 
 * <AUTHOR>
 */
@Slf4j
public class ProxyDemo {
    
    public static void main(String[] args) {
        log.info("=== 代理模式演示 ===");
        
        // 演示虚拟代理
        demonstrateVirtualProxy();
        
        // 演示保护代理
        demonstrateProtectionProxy();
        
        // 演示缓存代理
        demonstrateCacheProxy();
    }
    
    private static void demonstrateVirtualProxy() {
        log.info("\n--- 虚拟代理演示 ---");
        
        // 创建图片代理，实际图片延迟加载
        Image image1 = new ImageProxy("高清风景图.jpg", "1920x1080", 5.2);
        Image image2 = new ImageProxy("4K电影海报.jpg", "3840x2160", 12.8);
        
        log.info("📁 创建图片代理完成，实际图片尚未加载");
        
        // 显示图片信息（不需要加载实际图片）
        log.info("🖼️ 图片1信息: {}", image1.getImageInfo());
        log.info("🖼️ 图片2信息: {}", image2.getImageInfo());
        
        // 第一次显示图片（触发实际加载）
        log.info("\n📺 第一次显示图片1:");
        image1.display();
        
        log.info("\n📺 第一次显示图片2:");
        image2.display();
        
        // 第二次显示图片（使用已加载的图片）
        log.info("\n📺 第二次显示图片1:");
        image1.display();
        
        log.info("\n📺 第二次显示图片2:");
        image2.display();
    }
    
    private static void demonstrateProtectionProxy() {
        log.info("\n--- 保护代理演示 ---");
        
        // 创建不同权限的用户
        User admin = new User("管理员", "admin", UserRole.ADMIN);
        User user = new User("普通用户", "user", UserRole.USER);
        User guest = new User("访客", "guest", UserRole.GUEST);
        
        // 创建文档保护代理
        Document document = new DocumentProtectionProxy("机密文档.pdf");
        
        // 不同用户尝试访问文档
        log.info("👤 管理员尝试访问:");
        document.read(admin);
        document.write(admin, "管理员添加的内容");
        document.delete(admin);
        
        log.info("\n👤 普通用户尝试访问:");
        document.read(user);
        document.write(user, "用户添加的内容");
        document.delete(user);
        
        log.info("\n👤 访客尝试访问:");
        document.read(guest);
        document.write(guest, "访客尝试添加的内容");
        document.delete(guest);
    }
    
    private static void demonstrateCacheProxy() {
        log.info("\n--- 缓存代理演示 ---");
        
        // 创建数据库缓存代理
        DatabaseService dbService = new DatabaseCacheProxy();
        
        // 第一次查询（从数据库获取）
        log.info("🔍 第一次查询用户ID=1:");
        String user1 = dbService.getUserById(1);
        log.info("查询结果: {}", user1);
        
        log.info("\n🔍 第一次查询用户ID=2:");
        String user2 = dbService.getUserById(2);
        log.info("查询结果: {}", user2);
        
        // 第二次查询相同数据（从缓存获取）
        log.info("\n🔍 第二次查询用户ID=1:");
        String user1Cache = dbService.getUserById(1);
        log.info("查询结果: {}", user1Cache);
        
        log.info("\n🔍 第二次查询用户ID=2:");
        String user2Cache = dbService.getUserById(2);
        log.info("查询结果: {}", user2Cache);
        
        // 查询新数据
        log.info("\n🔍 查询用户ID=3:");
        String user3 = dbService.getUserById(3);
        log.info("查询结果: {}", user3);
        
        // 显示缓存统计
        if (dbService instanceof DatabaseCacheProxy) {
            ((DatabaseCacheProxy) dbService).showCacheStats();
        }
    }
}
