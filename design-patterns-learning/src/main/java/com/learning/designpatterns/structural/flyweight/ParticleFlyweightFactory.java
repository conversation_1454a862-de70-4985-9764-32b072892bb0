package com.learning.designpatterns.structural.flyweight;

import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;

/**
 * 粒子享元工厂
 * 
 * <AUTHOR>
 */
@Slf4j
public class ParticleFlyweightFactory {
    
    private static ParticleFlyweightFactory instance;
    private final Map<ParticleType, ParticleFlyweight> flyweights;
    
    private ParticleFlyweightFactory() {
        this.flyweights = new HashMap<>();
        log.info("🏭 粒子享元工厂初始化");
    }
    
    public static synchronized ParticleFlyweightFactory getInstance() {
        if (instance == null) {
            instance = new ParticleFlyweightFactory();
        }
        return instance;
    }
    
    public ParticleFlyweight getFlyweight(ParticleType type) {
        ParticleFlyweight flyweight = flyweights.get(type);
        
        if (flyweight == null) {
            flyweight = new ConcreteParticleFlyweight(type);
            flyweights.put(type, flyweight);
            log.debug("🆕 创建新的粒子享元: {}", type.getName());
        }
        
        return flyweight;
    }
    
    public int getCreatedFlyweightsCount() {
        return flyweights.size();
    }
}
