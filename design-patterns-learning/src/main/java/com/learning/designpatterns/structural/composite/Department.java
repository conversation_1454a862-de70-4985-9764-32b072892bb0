package com.learning.designpatterns.structural.composite;

import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门类（组合节点）
 * 
 * 表示组织架构中的部门，可以包含员工和子部门
 * 
 * <AUTHOR>
 */
@Slf4j
public class Department extends OrganizationComponent {
    
    private final List<OrganizationComponent> members;
    
    public Department(String name) {
        super(name);
        this.members = new ArrayList<>();
    }
    
    @Override
    public void add(OrganizationComponent component) {
        members.add(component);
        log.debug("添加 {} 到部门 {}", component.getName(), this.name);
    }
    
    @Override
    public void remove(OrganizationComponent component) {
        members.remove(component);
        log.debug("从部门 {} 移除 {}", this.name, component.getName());
    }
    
    @Override
    public int getEmployeeCount() {
        int totalEmployees = 0;
        for (OrganizationComponent member : members) {
            totalEmployees += member.getEmployeeCount();
        }
        return totalEmployees;
    }
    
    @Override
    public double getTotalSalary() {
        double totalSalary = 0;
        for (OrganizationComponent member : members) {
            totalSalary += member.getTotalSalary();
        }
        return totalSalary;
    }
    
    @Override
    public void display(int depth) {
        log.info("{}🏢 {} ({} 人)", getIndent(depth), name, getEmployeeCount());
        
        // 递归显示所有成员
        for (OrganizationComponent member : members) {
            member.display(depth + 1);
        }
    }
    
    @Override
    public OrganizationComponent findEmployee(String name) {
        // 递归搜索所有成员
        for (OrganizationComponent member : members) {
            OrganizationComponent found = member.findEmployee(name);
            if (found != null) {
                return found;
            }
        }
        return null;
    }
    
    /**
     * 获取部门成员列表
     */
    public List<OrganizationComponent> getMembers() {
        return new ArrayList<>(members);
    }
    
    /**
     * 获取直接下属员工
     */
    public List<Employee> getDirectEmployees() {
        List<Employee> employees = new ArrayList<>();
        for (OrganizationComponent member : members) {
            if (member instanceof Employee) {
                employees.add((Employee) member);
            }
        }
        return employees;
    }
    
    /**
     * 获取子部门
     */
    public List<Department> getSubDepartments() {
        List<Department> departments = new ArrayList<>();
        for (OrganizationComponent member : members) {
            if (member instanceof Department) {
                departments.add((Department) member);
            }
        }
        return departments;
    }
    
    /**
     * 计算平均薪资
     */
    public double getAverageSalary() {
        int employeeCount = getEmployeeCount();
        return employeeCount > 0 ? getTotalSalary() / employeeCount : 0;
    }
    
    /**
     * 获取管理层员工
     */
    public List<Employee> getManagers() {
        List<Employee> managers = new ArrayList<>();
        for (OrganizationComponent member : members) {
            if (member instanceof Employee) {
                Employee emp = (Employee) member;
                if (emp.isManager()) {
                    managers.add(emp);
                }
            } else if (member instanceof Department) {
                Department dept = (Department) member;
                managers.addAll(dept.getManagers());
            }
        }
        return managers;
    }
}
