package com.learning.designpatterns.structural.flyweight;

import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;

/**
 * 图标享元工厂
 * 
 * <AUTHOR>
 */
@Slf4j
public class IconFlyweightFactory {
    
    private static IconFlyweightFactory instance;
    private final Map<String, IconFlyweight> flyweights;
    
    private IconFlyweightFactory() {
        this.flyweights = new HashMap<>();
        log.info("🏭 图标享元工厂初始化");
    }
    
    public static synchronized IconFlyweightFactory getInstance() {
        if (instance == null) {
            instance = new IconFlyweightFactory();
        }
        return instance;
    }
    
    public IconFlyweight getFlyweight(String iconType, String size) {
        String key = iconType + "_" + size;
        IconFlyweight flyweight = flyweights.get(key);
        
        if (flyweight == null) {
            flyweight = new ConcreteIconFlyweight(iconType, size);
            flyweights.put(key, flyweight);
            log.debug("🆕 创建新的图标享元: {} ({})", iconType, size);
        }
        
        return flyweight;
    }
    
    public int getCreatedFlyweightsCount() {
        return flyweights.size();
    }
}
