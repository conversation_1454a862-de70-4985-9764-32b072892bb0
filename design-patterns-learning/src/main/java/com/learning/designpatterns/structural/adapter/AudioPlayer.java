package com.learning.designpatterns.structural.adapter;

import lombok.extern.slf4j.Slf4j;

/**
 * 音频播放器
 * 
 * 实现MediaPlayer接口，原生支持MP3格式，
 * 通过适配器支持其他格式
 * 
 * <AUTHOR>
 */
@Slf4j
public class AudioPlayer implements MediaPlayer {
    
    private MediaAdapter mediaAdapter;
    
    @Override
    public void play(String audioType, String fileName) {
        log.info("🎵 音频播放器收到播放请求: {} - {}", audioType, fileName);
        
        // 原生支持MP3格式
        if ("mp3".equalsIgnoreCase(audioType)) {
            playMp3(fileName);
        }
        // 通过适配器支持其他格式
        else if ("vlc".equalsIgnoreCase(audioType) || "mp4".equalsIgnoreCase(audioType)) {
            mediaAdapter = new MediaAdapter(audioType);
            mediaAdapter.play(audioType, fileName);
        }
        // 不支持的格式
        else {
            log.error("❌ 不支持的音频格式: {}。支持的格式: mp3, mp4, vlc", audioType);
        }
    }
    
    /**
     * 播放MP3文件的原生实现
     * 
     * @param fileName 文件名
     */
    private void playMp3(String fileName) {
        log.info("🎶 原生MP3播放器正在播放: {}", fileName);
        
        try {
            Thread.sleep(400);
            log.info("🎼 高品质音频，比特率: 320kbps");
            Thread.sleep(600);
            log.info("✅ MP3播放完成: {}", fileName);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("MP3播放被中断", e);
        }
    }
}
