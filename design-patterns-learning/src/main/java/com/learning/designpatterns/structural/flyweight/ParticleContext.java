package com.learning.designpatterns.structural.flyweight;

import lombok.Getter;
import lombok.Setter;

/**
 * 粒子上下文类 - 存储外部状态
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class ParticleContext {
    
    private double x;
    private double y;
    private double velocityX;
    private double velocityY;
    private double life;
    
    public ParticleContext(double x, double y, double velocityX, double velocityY) {
        this.x = x;
        this.y = y;
        this.velocityX = velocityX;
        this.velocityY = velocityY;
        this.life = 3.0; // 3秒生命周期
    }
    
    public boolean isAlive() {
        return life > 0;
    }
}
