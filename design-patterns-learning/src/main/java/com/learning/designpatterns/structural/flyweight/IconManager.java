package com.learning.designpatterns.structural.flyweight;

import lombok.extern.slf4j.Slf4j;

/**
 * 图标管理器
 * 
 * <AUTHOR>
 */
@Slf4j
public class IconManager {
    
    private final IconFlyweightFactory factory;
    private int displayCount = 0;
    
    public IconManager() {
        this.factory = IconFlyweightFactory.getInstance();
        log.info("🖼️ 图标管理器初始化");
    }
    
    public void displayIcon(String iconType, String size, int x, int y) {
        IconFlyweight icon = factory.getFlyweight(iconType, size);
        icon.display(x, y);
        displayCount++;
        
        // 每显示10个图标输出一次统计
        if (displayCount % 20 == 0) {
            log.info("📊 已显示 {} 个图标，享元对象数: {}", 
                    displayCount, factory.getCreatedFlyweightsCount());
        }
    }
    
    public int getDisplayCount() {
        return displayCount;
    }
}
