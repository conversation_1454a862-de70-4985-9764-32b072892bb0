package com.learning.designpatterns.structural.bridge;

import java.util.List;
import java.util.Map;

/**
 * 数据库驱动接口（实现接口）
 * 
 * 定义了数据库操作的基本接口
 * 
 * <AUTHOR>
 */
public interface DatabaseDriver {
    
    /**
     * 连接数据库
     * 
     * @param connectionString 连接字符串
     * @return 是否连接成功
     */
    boolean connect(String connectionString);
    
    /**
     * 执行查询
     * 
     * @param sql SQL语句
     * @return 查询结果
     */
    List<Map<String, Object>> executeQuery(String sql);
    
    /**
     * 执行更新
     * 
     * @param sql SQL语句
     * @return 影响的行数
     */
    int executeUpdate(String sql);
    
    /**
     * 开始事务
     */
    void beginTransaction();
    
    /**
     * 提交事务
     */
    void commitTransaction();
    
    /**
     * 回滚事务
     */
    void rollbackTransaction();
    
    /**
     * 关闭连接
     */
    void close();
    
    /**
     * 获取驱动名称
     * 
     * @return 驱动名称
     */
    String getDriverName();
}
