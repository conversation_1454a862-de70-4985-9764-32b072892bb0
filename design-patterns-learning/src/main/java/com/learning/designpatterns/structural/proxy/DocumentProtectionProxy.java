package com.learning.designpatterns.structural.proxy;

import lombok.extern.slf4j.Slf4j;

/**
 * 文档保护代理
 * 
 * 控制对文档的访问权限
 * 
 * <AUTHOR>
 */
@Slf4j
public class DocumentProtectionProxy implements Document {
    
    private final String filename;
    private RealDocument realDocument;
    
    public DocumentProtectionProxy(String filename) {
        this.filename = filename;
        log.info("🛡️ 创建文档保护代理: {}", filename);
    }
    
    @Override
    public void read(User user) {
        if (hasReadPermission(user)) {
            log.info("✅ 权限验证通过，允许读取");
            getRealDocument().read(user);
        } else {
            log.warn("❌ 权限不足，拒绝读取访问");
        }
    }
    
    @Override
    public void write(User user, String content) {
        if (hasWritePermission(user)) {
            log.info("✅ 权限验证通过，允许写入");
            getRealDocument().write(user, content);
        } else {
            log.warn("❌ 权限不足，拒绝写入访问");
        }
    }
    
    @Override
    public void delete(User user) {
        if (hasDeletePermission(user)) {
            log.info("✅ 权限验证通过，允许删除");
            getRealDocument().delete(user);
        } else {
            log.warn("❌ 权限不足，拒绝删除访问");
        }
    }
    
    /**
     * 检查读取权限
     */
    private boolean hasReadPermission(User user) {
        // 所有用户都可以读取
        return true;
    }
    
    /**
     * 检查写入权限
     */
    private boolean hasWritePermission(User user) {
        // 只有管理员和普通用户可以写入
        return user.getRole() == UserRole.ADMIN || user.getRole() == UserRole.USER;
    }
    
    /**
     * 检查删除权限
     */
    private boolean hasDeletePermission(User user) {
        // 只有管理员可以删除
        return user.getRole() == UserRole.ADMIN;
    }
    
    /**
     * 延迟创建真实文档对象
     */
    private RealDocument getRealDocument() {
        if (realDocument == null) {
            realDocument = new RealDocument(filename);
        }
        return realDocument;
    }
}
