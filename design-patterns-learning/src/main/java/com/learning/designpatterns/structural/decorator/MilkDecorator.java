package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 牛奶装饰器
 * 
 * 为咖啡添加牛奶的装饰器
 * 
 * <AUTHOR>
 */
@Slf4j
public class MilkDecorator extends CoffeeDecorator {
    
    public MilkDecorator(Coffee coffee) {
        super(coffee);
        log.info("🥛 添加牛奶");
    }
    
    @Override
    public String getDescription() {
        return coffee.getDescription() + " + 牛奶";
    }
    
    @Override
    public double getCost() {
        return coffee.getCost() + 2.0; // 牛奶加2元
    }
}
