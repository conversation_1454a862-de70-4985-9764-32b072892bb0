package com.learning.designpatterns.structural.bridge;

/**
 * 绘制API接口（实现接口）
 * 
 * 定义了绘制操作的基本接口
 * 
 * <AUTHOR>
 */
public interface DrawingAPI {
    
    /**
     * 绘制圆形
     * 
     * @param x 圆心X坐标
     * @param y 圆心Y坐标
     * @param radius 半径
     */
    void drawCircle(double x, double y, double radius);
    
    /**
     * 绘制矩形
     * 
     * @param x 左上角X坐标
     * @param y 左上角Y坐标
     * @param width 宽度
     * @param height 高度
     */
    void drawRectangle(double x, double y, double width, double height);
    
    /**
     * 绘制三角形
     * 
     * @param x1 第一个点X坐标
     * @param y1 第一个点Y坐标
     * @param x2 第二个点X坐标
     * @param y2 第二个点Y坐标
     * @param x3 第三个点X坐标
     * @param y3 第三个点Y坐标
     */
    void drawTriangle(double x1, double y1, double x2, double y2, double x3, double y3);
    
    /**
     * 绘制线条
     * 
     * @param x1 起点X坐标
     * @param y1 起点Y坐标
     * @param x2 终点X坐标
     * @param y2 终点Y坐标
     */
    void drawLine(double x1, double y1, double x2, double y2);
    
    /**
     * 获取API名称
     * 
     * @return API名称
     */
    String getAPIName();
}
