package com.learning.designpatterns.structural.composite;

import lombok.extern.slf4j.Slf4j;

/**
 * 控件类（叶子节点）
 * 
 * 表示UI中的基本控件
 * 
 * <AUTHOR>
 */
@Slf4j
public class Control extends UIComponent {
    
    public Control(String name, String type) {
        super(name, type);
    }
    
    @Override
    public void render() {
        log.info("🎨 渲染控件: {} ({})", name, type);
        
        // 模拟渲染过程
        try {
            Thread.sleep(50); // 模拟渲染时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    @Override
    public void display(int depth) {
        String icon = getControlIcon(type);
        log.info("{}{} {} ({})", getIndent(depth), icon, name, type);
    }
    
    @Override
    public int getComponentCount() {
        return 1; // 控件本身算一个组件
    }
    
    /**
     * 根据控件类型获取图标
     */
    private String getControlIcon(String type) {
        return switch (type.toLowerCase()) {
            case "button" -> "🔘";
            case "textbox" -> "📝";
            case "image" -> "🖼️";
            case "table" -> "📊";
            case "menu" -> "📋";
            case "statusbar" -> "📊";
            default -> "🔧";
        };
    }
    
    /**
     * 模拟控件事件处理
     */
    public void handleEvent(String eventType) {
        log.info("🎯 控件 {} 处理事件: {}", name, eventType);
    }
    
    /**
     * 设置控件属性
     */
    public void setProperty(String property, Object value) {
        log.info("⚙️ 设置控件 {} 的属性 {}: {}", name, property, value);
    }
}
