package com.learning.designpatterns.structural.adapter;

import lombok.extern.slf4j.Slf4j;

/**
 * 媒体适配器
 * 
 * 实现MediaPlayer接口，内部使用AdvancedMediaPlayer来播放高级格式
 * 这是适配器模式的核心实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class MediaAdapter implements MediaPlayer {
    
    private AdvancedMediaPlayer advancedMusicPlayer;
    
    /**
     * 构造函数，根据音频类型创建相应的高级播放器
     * 
     * @param audioType 音频类型
     */
    public MediaAdapter(String audioType) {
        log.info("🔧 创建媒体适配器，音频类型: {}", audioType);
        
        if ("vlc".equalsIgnoreCase(audioType)) {
            advancedMusicPlayer = new VlcPlayer();
        } else if ("mp4".equalsIgnoreCase(audioType)) {
            advancedMusicPlayer = new Mp4Player();
        } else {
            log.warn("⚠️ 不支持的音频类型: {}", audioType);
        }
    }
    
    @Override
    public void play(String audioType, String fileName) {
        if ("vlc".equalsIgnoreCase(audioType)) {
            advancedMusicPlayer.playVlc(fileName);
        } else if ("mp4".equalsIgnoreCase(audioType)) {
            advancedMusicPlayer.playMp4(fileName);
        } else {
            log.error("❌ 媒体适配器不支持的格式: {}", audioType);
        }
    }
}
