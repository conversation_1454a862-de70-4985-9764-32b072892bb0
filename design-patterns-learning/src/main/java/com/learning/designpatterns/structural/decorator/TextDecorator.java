package com.learning.designpatterns.structural.decorator;

/**
 * 文本装饰器抽象类
 * 
 * 装饰器模式的抽象装饰器类，实现Text接口
 * 并持有一个Text对象的引用
 * 
 * <AUTHOR>
 */
public abstract class TextDecorator implements Text {
    
    /**
     * 被装饰的文本对象
     */
    protected Text text;
    
    /**
     * 构造函数
     * 
     * @param text 被装饰的文本对象
     */
    public TextDecorator(Text text) {
        this.text = text;
    }
    
    @Override
    public String render() {
        return text.render();
    }
}
