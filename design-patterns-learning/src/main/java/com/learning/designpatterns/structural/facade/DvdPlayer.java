package com.learning.designpatterns.structural.facade;

import lombok.extern.slf4j.Slf4j;

/**
 * DVD播放器
 * 
 * <AUTHOR>
 */
@Slf4j
public class DvdPlayer {
    
    public void on() {
        log.info("📀 DVD播放器开启");
    }
    
    public void off() {
        log.info("📴 DVD播放器关闭");
    }
    
    public void play(String movie) {
        log.info("▶️ 播放电影: {}", movie);
    }
    
    public void stop() {
        log.info("⏹️ 停止播放");
    }
    
    public void eject() {
        log.info("⏏️ 弹出光盘");
    }
}
