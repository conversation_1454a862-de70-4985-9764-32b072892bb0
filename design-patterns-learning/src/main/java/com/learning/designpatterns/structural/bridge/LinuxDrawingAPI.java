package com.learning.designpatterns.structural.bridge;

import lombok.extern.slf4j.Slf4j;

/**
 * Linux绘制API实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class LinuxDrawingAPI implements DrawingAPI {
    
    @Override
    public void drawCircle(double x, double y, double radius) {
        // 模拟Linux X11的具体绘制实现
        log.info("🐧 Linux X11 API:");
        log.info("   Display *display = XOpenDisplay(NULL)");
        log.info("   XSetForeground(display, gc, BlackPixel(display, screen))");
        log.info("   XDrawArc(display, window, gc, {}, {}, {}, {}, 0, 360*64)",
                (int)(x-radius), (int)(y-radius), (int)(radius*2), (int)(radius*2));
        log.info("   XFlush(display)");
        log.info("   ✅ 圆形绘制完成 - 使用X11协议");
    }

    @Override
    public void drawRectangle(double x, double y, double width, double height) {
        // 模拟Linux X11的具体绘制实现
        log.info("🐧 Linux X11 API:");
        log.info("   Display *display = XOpenDisplay(NULL)");
        log.info("   XSetForeground(display, gc, BlackPixel(display, screen))");
        log.info("   XDrawRectangle(display, window, gc, {}, {}, {}, {})",
                (int)x, (int)y, (int)width, (int)height);
        log.info("   XFlush(display)");
        log.info("   ✅ 矩形绘制完成 - 使用X11协议");
    }

    @Override
    public void drawTriangle(double x1, double y1, double x2, double y2, double x3, double y3) {
        // 模拟Linux X11的具体绘制实现
        log.info("🐧 Linux X11 API:");
        log.info("   Display *display = XOpenDisplay(NULL)");
        log.info("   XPoint points[] = {{({}, {})}, {({}, {})}, {({}, {})}, {({}, {})}}}",
                (int)x1, (int)y1, (int)x2, (int)y2, (int)x3, (int)y3, (int)x1, (int)y1);
        log.info("   XDrawLines(display, window, gc, points, 4, CoordModeOrigin)");
        log.info("   XFlush(display)");
        log.info("   ✅ 三角形绘制完成 - 使用X11协议");
    }

    @Override
    public void drawLine(double x1, double y1, double x2, double y2) {
        // 模拟Linux X11的具体绘制实现
        log.info("🐧 Linux X11 API:");
        log.info("   Display *display = XOpenDisplay(NULL)");
        log.info("   XDrawLine(display, window, gc, {}, {}, {}, {})",
                (int)x1, (int)y1, (int)x2, (int)y2);
        log.info("   XFlush(display)");
        log.info("   ✅ 线条绘制完成 - 使用X11协议");
    }
    
    @Override
    public String getAPIName() {
        return "Linux X11";
    }
}
