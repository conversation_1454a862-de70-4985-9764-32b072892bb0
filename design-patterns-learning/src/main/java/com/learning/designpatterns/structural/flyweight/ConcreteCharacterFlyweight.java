package com.learning.designpatterns.structural.flyweight;

import lombok.extern.slf4j.Slf4j;

/**
 * 具体字符享元类
 * 
 * 实现享元接口，为内部状态增加存储空间
 * 
 * <AUTHOR>
 */
@Slf4j
public class ConcreteCharacterFlyweight implements CharacterFlyweight {
    
    private final char character; // 内部状态 - 字符本身
    
    public ConcreteCharacterFlyweight(char character) {
        this.character = character;
        log.debug("🔤 创建字符享元对象: '{}'", character);
    }
    
    @Override
    public void render(String font, int size, String color, int x, int y) {
        // 使用外部状态进行渲染
        String displayChar = character == ' ' ? "␣" : String.valueOf(character);
        log.info("🎨 渲染字符 '{}' - 字体:{}, 大小:{}, 颜色:{}, 位置:({}, {})", 
                displayChar, font, size, color, x, y);
    }
    
    @Override
    public char getCharacter() {
        return character;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ConcreteCharacterFlyweight that = (ConcreteCharacterFlyweight) obj;
        return character == that.character;
    }
    
    @Override
    public int hashCode() {
        return Character.hashCode(character);
    }
    
    @Override
    public String toString() {
        return String.format("CharacterFlyweight['%c']", character);
    }
}
