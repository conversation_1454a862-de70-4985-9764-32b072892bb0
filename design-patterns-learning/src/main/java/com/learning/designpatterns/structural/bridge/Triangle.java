package com.learning.designpatterns.structural.bridge;

import lombok.extern.slf4j.Slf4j;

/**
 * 三角形类
 * 
 * 具体的形状实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class Triangle extends Shape {
    
    private double x1, y1, x2, y2, x3, y3;
    
    public Triangle(DrawingAPI drawingAPI, double x1, double y1, double x2, double y2, double x3, double y3) {
        super(drawingAPI);
        this.x1 = x1;
        this.y1 = y1;
        this.x2 = x2;
        this.y2 = y2;
        this.x3 = x3;
        this.y3 = y3;
    }
    
    @Override
    public void draw() {
        log.info("🔺 绘制三角形 - 使用 {}", drawingAPI.getAPIName());
        drawingAPI.drawTriangle(x1, y1, x2, y2, x3, y3);
    }
    
    @Override
    public void resize(double factor) {
        // 以第一个点为基准点进行缩放
        x2 = x1 + (x2 - x1) * factor;
        y2 = y1 + (y2 - y1) * factor;
        x3 = x1 + (x3 - x1) * factor;
        y3 = y1 + (y3 - y1) * factor;
        log.info("🔄 三角形大小调整，缩放因子: {}", factor);
    }
    
    @Override
    public String getShapeInfo() {
        return String.format("三角形[顶点: (%.1f,%.1f), (%.1f,%.1f), (%.1f,%.1f)]", 
                x1, y1, x2, y2, x3, y3);
    }
    
    /**
     * 移动三角形
     * 
     * @param deltaX X方向偏移
     * @param deltaY Y方向偏移
     */
    public void move(double deltaX, double deltaY) {
        x1 += deltaX;
        y1 += deltaY;
        x2 += deltaX;
        y2 += deltaY;
        x3 += deltaX;
        y3 += deltaY;
        log.info("📍 三角形移动偏移: ({}, {})", deltaX, deltaY);
    }
    
    /**
     * 获取三角形面积（使用海伦公式）
     * 
     * @return 面积
     */
    public double getArea() {
        double a = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
        double b = Math.sqrt(Math.pow(x3 - x2, 2) + Math.pow(y3 - y2, 2));
        double c = Math.sqrt(Math.pow(x1 - x3, 2) + Math.pow(y1 - y3, 2));
        double s = (a + b + c) / 2;
        return Math.sqrt(s * (s - a) * (s - b) * (s - c));
    }
}
