package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 下划线装饰器
 * 
 * 为文本添加下划线效果的装饰器
 * 
 * <AUTHOR>
 */
@Slf4j
public class UnderlineDecorator extends TextDecorator {
    
    public UnderlineDecorator(Text text) {
        super(text);
        log.info("📏 添加下划线效果");
    }
    
    @Override
    public String render() {
        return "<u>" + text.render() + "</u>";
    }
}
