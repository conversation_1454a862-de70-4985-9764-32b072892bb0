package com.learning.designpatterns.structural.flyweight;

/**
 * 图标享元接口
 * 
 * <AUTHOR>
 */
public interface IconFlyweight {
    
    /**
     * 显示图标
     * 
     * @param x X坐标 (外部状态)
     * @param y Y坐标 (外部状态)
     */
    void display(int x, int y);
    
    /**
     * 获取图标类型
     * 
     * @return 图标类型
     */
    String getIconType();
    
    /**
     * 获取图标大小
     * 
     * @return 图标大小
     */
    String getSize();
}
