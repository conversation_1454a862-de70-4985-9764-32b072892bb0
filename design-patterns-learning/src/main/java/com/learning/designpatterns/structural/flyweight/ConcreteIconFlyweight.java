package com.learning.designpatterns.structural.flyweight;

import lombok.extern.slf4j.Slf4j;

/**
 * 具体图标享元类
 * 
 * <AUTHOR>
 */
@Slf4j
public class ConcreteIconFlyweight implements IconFlyweight {
    
    private final String iconType; // 内部状态
    private final String size;     // 内部状态
    
    public ConcreteIconFlyweight(String iconType, String size) {
        this.iconType = iconType;
        this.size = size;
        log.debug("🖼️ 创建图标享元对象: {} ({})", iconType, size);
        
        // 模拟加载图标资源的耗时操作
        try {
            Thread.sleep(10); // 模拟从磁盘或网络加载图标
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    @Override
    public void display(int x, int y) {
        String icon = getIconSymbol(iconType);
        log.info("🖼️ 显示图标 {} {} ({}) 位置:({}, {})", icon, iconType, size, x, y);
    }
    
    @Override
    public String getIconType() {
        return iconType;
    }
    
    @Override
    public String getSize() {
        return size;
    }
    
    private String getIconSymbol(String iconType) {
        return switch (iconType.toLowerCase()) {
            case "home" -> "🏠";
            case "user" -> "👤";
            case "settings" -> "⚙️";
            case "search" -> "🔍";
            case "heart" -> "❤️";
            case "star" -> "⭐";
            default -> "📄";
        };
    }
}
