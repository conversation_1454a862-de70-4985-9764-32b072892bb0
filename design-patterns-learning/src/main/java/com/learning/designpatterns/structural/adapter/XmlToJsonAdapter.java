package com.learning.designpatterns.structural.adapter;

import lombok.extern.slf4j.Slf4j;

/**
 * XML到JSON适配器
 * 
 * 将JSON接口适配到XML处理器，使得XML处理器能够处理JSON数据
 * 这是适配器模式的另一个实现示例
 * 
 * <AUTHOR>
 */
@Slf4j
public class XmlToJsonAdapter implements JsonDataProcessor {
    
    private final XmlDataProcessor xmlProcessor;
    
    /**
     * 构造函数
     * 
     * @param xmlProcessor XML数据处理器
     */
    public XmlToJsonAdapter(XmlDataProcessor xmlProcessor) {
        this.xmlProcessor = xmlProcessor;
        log.info("🔧 创建XML到JSON适配器");
    }
    
    @Override
    public void processJson(String jsonData) {
        log.info("🔄 适配器接收到JSON数据，准备转换为XML");
        log.info("原始JSON数据: {}", jsonData);
        
        // 将JSON转换为XML
        String xmlData = convertJsonToXml(jsonData);
        
        log.info("🔄 JSON转换为XML完成");
        log.info("转换后的XML数据: {}", xmlData);
        
        // 使用XML处理器处理转换后的数据
        xmlProcessor.processXml(xmlData);
    }
    
    /**
     * 将JSON数据转换为XML格式
     * 
     * @param jsonData JSON数据
     * @return XML数据
     */
    private String convertJsonToXml(String jsonData) {
        log.info("⚙️ 开始JSON到XML转换...");
        
        try {
            Thread.sleep(200); // 模拟转换延迟
            
            // 简单的JSON到XML转换逻辑（实际项目中会使用专业的转换库）
            String xmlData = jsonData
                .replace("{", "<person>")
                .replace("}", "</person>")
                .replace("\"name\":", "<name>")
                .replace("\"age\":", "</name><age>")
                .replace("\"city\":", "</age><city>")
                .replace("\"", "")
                .replace(",", "")
                .replace(":", "")
                .replace("25", "25</city>");
            
            // 清理和格式化
            xmlData = xmlData.replaceAll("\\s+", "");
            
            // 如果转换失败，提供默认的XML结构
            if (!xmlData.contains("<") || !xmlData.contains(">")) {
                xmlData = "<data>" + jsonData + "</data>";
            }
            
            return xmlData;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("JSON到XML转换被中断", e);
            return "<error>转换失败</error>";
        }
    }
}
