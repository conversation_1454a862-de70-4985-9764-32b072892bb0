package com.learning.designpatterns.structural.facade;

import lombok.extern.slf4j.Slf4j;

/**
 * 外观模式演示类
 * 
 * 外观模式为子系统中的一组接口提供一个一致的界面，
 * 外观模式定义了一个高层接口，这个接口使得这一子系统更加容易使用。
 * 
 * 应用场景：
 * - 简化复杂系统的接口
 * - 家庭影院系统控制
 * - 计算机启动过程
 * - 在线购物系统
 * - 银行业务处理
 * 
 * <AUTHOR>
 */
@Slf4j
public class FacadeDemo {
    
    public static void main(String[] args) {
        log.info("=== 外观模式演示 ===");
        
        // 演示家庭影院系统
        demonstrateHomeTheaterSystem();
        
        // 演示计算机系统
        demonstrateComputerSystem();
        
        // 演示在线购物系统
        demonstrateOnlineShoppingSystem();
    }
    
    private static void demonstrateHomeTheaterSystem() {
        log.info("\n--- 家庭影院系统演示 ---");
        
        // 不使用外观模式 - 客户端需要了解所有子系统
        log.info("❌ 不使用外观模式 - 复杂的操作:");
        
        Amplifier amplifier = new Amplifier();
        DvdPlayer dvdPlayer = new DvdPlayer();
        Projector projector = new Projector();
        Screen screen = new Screen();
        TheaterLights lights = new TheaterLights();
        PopcornPopper popper = new PopcornPopper();
        
        // 客户端需要手动控制每个设备
        log.info("🎬 手动启动观影模式:");
        popper.on();
        popper.pop();
        lights.dim(10);
        screen.down();
        projector.on();
        projector.setInput(dvdPlayer);
        amplifier.on();
        amplifier.setDvd(dvdPlayer);
        amplifier.setSurroundSound();
        amplifier.setVolume(5);
        dvdPlayer.on();
        dvdPlayer.play("阿凡达");
        
        log.info("\n✅ 使用外观模式 - 简化的操作:");
        
        // 使用外观模式
        HomeTheaterFacade homeTheater = new HomeTheaterFacade(
            amplifier, dvdPlayer, projector, screen, lights, popper);
        
        log.info("🎬 一键启动观影模式:");
        homeTheater.watchMovie("阿凡达");
        
        log.info("\n🔚 一键结束观影:");
        homeTheater.endMovie();
    }
    
    private static void demonstrateComputerSystem() {
        log.info("\n--- 计算机系统演示 ---");
        
        // 创建计算机外观
        ComputerFacade computer = new ComputerFacade();
        
        log.info("💻 启动计算机:");
        computer.start();
        
        log.info("\n🔄 重启计算机:");
        computer.restart();
        
        log.info("\n⚡ 关闭计算机:");
        computer.shutdown();
    }
    
    private static void demonstrateOnlineShoppingSystem() {
        log.info("\n--- 在线购物系统演示 ---");
        
        // 创建购物外观
        OnlineShoppingFacade shopping = new OnlineShoppingFacade();
        
        log.info("🛒 执行完整购物流程:");
        boolean success = shopping.purchaseProduct("iPhone 15", "张三", "1234567890123456");
        
        if (success) {
            log.info("✅ 购物成功完成！");
        } else {
            log.info("❌ 购物失败！");
        }
    }
}
