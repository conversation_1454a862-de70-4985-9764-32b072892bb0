package com.learning.designpatterns.structural.facade;

import lombok.extern.slf4j.Slf4j;

/**
 * 功放设备
 * 
 * <AUTHOR>
 */
@Slf4j
public class Amplifier {
    
    public void on() {
        log.info("🔊 功放开启");
    }
    
    public void off() {
        log.info("🔇 功放关闭");
    }
    
    public void setDvd(DvdPlayer dvd) {
        log.info("🔗 功放连接DVD播放器");
    }
    
    public void setSurroundSound() {
        log.info("🎵 设置环绕声模式");
    }
    
    public void setVolume(int level) {
        log.info("🔊 设置音量为: {}", level);
    }
}
