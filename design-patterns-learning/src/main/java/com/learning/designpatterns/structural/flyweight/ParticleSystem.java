package com.learning.designpatterns.structural.flyweight;

import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 粒子系统类
 * 
 * <AUTHOR>
 */
@Slf4j
public class ParticleSystem {
    
    private final List<Particle> particles;
    private final ParticleFlyweightFactory factory;
    
    public ParticleSystem() {
        this.particles = new ArrayList<>();
        this.factory = ParticleFlyweightFactory.getInstance();
        log.info("🎆 粒子系统初始化");
    }
    
    public void addParticle(ParticleType type, double x, double y, double velocityX, double velocityY) {
        ParticleFlyweight flyweight = factory.getFlyweight(type);
        ParticleContext context = new ParticleContext(x, y, velocityX, velocityY);
        particles.add(new Particle(flyweight, context));
    }
    
    public void update(double deltaTime) {
        Iterator<Particle> iterator = particles.iterator();
        int removedCount = 0;
        
        while (iterator.hasNext()) {
            Particle particle = iterator.next();
            particle.update(deltaTime);
            
            if (!particle.isAlive()) {
                iterator.remove();
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            log.debug("🗑️ 移除了 {} 个死亡粒子，剩余: {}", removedCount, particles.size());
        }
    }
    
    public void render(int maxCount) {
        log.info("🎨 渲染粒子 (显示前 {} 个):", maxCount);
        for (int i = 0; i < Math.min(particles.size(), maxCount); i++) {
            particles.get(i).render();
        }
    }
    
    public int getParticleCount() {
        return particles.size();
    }
    
    private static class Particle {
        private final ParticleFlyweight flyweight;
        private final ParticleContext context;
        
        public Particle(ParticleFlyweight flyweight, ParticleContext context) {
            this.flyweight = flyweight;
            this.context = context;
        }
        
        public void update(double deltaTime) {
            flyweight.update(context, deltaTime);
        }
        
        public void render() {
            flyweight.render(context.getX(), context.getY(), 
                           context.getVelocityX(), context.getVelocityY());
        }
        
        public boolean isAlive() {
            return context.isAlive();
        }
    }
}
