package com.learning.designpatterns.structural.composite;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件类（叶子节点）
 * 
 * 表示文件系统中的文件
 * 
 * <AUTHOR>
 */
@Slf4j
public class File extends FileSystemComponent {
    
    private final long size;
    
    public File(String name, long size) {
        super(name);
        this.size = size;
    }
    
    @Override
    public long getSize() {
        return size;
    }
    
    @Override
    public int getFileCount() {
        return 1; // 文件本身算一个文件
    }
    
    @Override
    public void display(int depth) {
        String sizeStr = formatSize(size);
        log.info("{}📄 {} ({})", getIndent(depth), name, sizeStr);
    }
    
    @Override
    public FileSystemComponent search(String name) {
        return this.name.equals(name) ? this : null;
    }
    
    /**
     * 格式化文件大小
     */
    private String formatSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }
    
    /**
     * 获取文件扩展名
     */
    public String getExtension() {
        int lastDot = name.lastIndexOf('.');
        return lastDot > 0 ? name.substring(lastDot + 1) : "";
    }
    
    /**
     * 判断是否为指定类型的文件
     */
    public boolean isType(String extension) {
        return getExtension().equalsIgnoreCase(extension);
    }
}
