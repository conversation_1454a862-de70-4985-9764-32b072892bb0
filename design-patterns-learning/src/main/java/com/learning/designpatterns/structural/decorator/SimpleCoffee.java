package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 简单咖啡实现
 * 
 * 基础的咖啡实现，作为装饰器的基础组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class SimpleCoffee implements Coffee {
    
    public SimpleCoffee() {
        log.info("☕ 制作基础咖啡");
    }
    
    @Override
    public String getDescription() {
        return "简单咖啡";
    }
    
    @Override
    public double getCost() {
        return 10.0; // 基础咖啡价格10元
    }
}
