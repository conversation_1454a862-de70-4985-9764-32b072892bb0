package com.learning.designpatterns.structural.proxy;

import lombok.extern.slf4j.Slf4j;

/**
 * 图片代理类
 * 
 * 虚拟代理，延迟加载真实图片对象
 * 
 * <AUTHOR>
 */
@Slf4j
public class ImageProxy implements Image {
    
    private final String filename;
    private final String resolution;
    private final double sizeInMB;
    private RealImage realImage;
    
    public ImageProxy(String filename, String resolution, double sizeInMB) {
        this.filename = filename;
        this.resolution = resolution;
        this.sizeInMB = sizeInMB;
        log.info("🔗 创建图片代理: {}", filename);
    }
    
    @Override
    public void display() {
        // 延迟加载：只有在真正需要显示时才创建真实图片对象
        if (realImage == null) {
            log.info("🔄 代理检测到首次显示请求，开始加载真实图片");
            realImage = new RealImage(filename, resolution, sizeInMB);
        } else {
            log.info("🚀 代理使用已加载的图片");
        }
        
        realImage.display();
    }
    
    @Override
    public String getImageInfo() {
        // 获取图片信息不需要加载真实图片
        return String.format("%s (%s, %.1fMB) [代理]", filename, resolution, sizeInMB);
    }
}
