package com.learning.designpatterns.structural.composite;

/**
 * 组织架构组件抽象类
 * 
 * 定义了组织架构中部门和员工的公共接口
 * 
 * <AUTHOR>
 */
public abstract class OrganizationComponent {
    
    protected String name;
    
    public OrganizationComponent(String name) {
        this.name = name;
    }
    
    /**
     * 获取名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取员工数量
     */
    public abstract int getEmployeeCount();
    
    /**
     * 获取总薪资
     */
    public abstract double getTotalSalary();
    
    /**
     * 显示组织结构
     */
    public abstract void display(int depth);
    
    /**
     * 添加下属（只有部门支持）
     */
    public void add(OrganizationComponent component) {
        throw new UnsupportedOperationException("不支持添加操作");
    }
    
    /**
     * 移除下属（只有部门支持）
     */
    public void remove(OrganizationComponent component) {
        throw new UnsupportedOperationException("不支持移除操作");
    }
    
    /**
     * 查找员工
     */
    public abstract OrganizationComponent findEmployee(String name);
    
    /**
     * 获取缩进字符串
     */
    protected String getIndent(int depth) {
        return "  ".repeat(depth);
    }
}
