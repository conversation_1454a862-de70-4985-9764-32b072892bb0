package com.learning.designpatterns.structural.flyweight;

/**
 * 粒子享元接口
 * 
 * <AUTHOR>
 */
public interface ParticleFlyweight {
    
    /**
     * 渲染粒子
     * 
     * @param x X坐标 (外部状态)
     * @param y Y坐标 (外部状态)
     * @param velocityX X方向速度 (外部状态)
     * @param velocityY Y方向速度 (外部状态)
     */
    void render(double x, double y, double velocityX, double velocityY);
    
    /**
     * 更新粒子
     * 
     * @param context 粒子上下文
     * @param deltaTime 时间间隔
     */
    void update(ParticleContext context, double deltaTime);
    
    /**
     * 获取粒子类型
     * 
     * @return 粒子类型
     */
    ParticleType getType();
}
