package com.learning.designpatterns.structural.composite;

import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * 目录类（组合节点）
 * 
 * 表示文件系统中的目录，可以包含文件和子目录
 * 
 * <AUTHOR>
 */
@Slf4j
public class Directory extends FileSystemComponent {
    
    private final List<FileSystemComponent> children;
    
    public Directory(String name) {
        super(name);
        this.children = new ArrayList<>();
    }
    
    @Override
    public void add(FileSystemComponent component) {
        children.add(component);
        log.debug("添加 {} 到目录 {}", component.getName(), this.name);
    }
    
    @Override
    public void remove(FileSystemComponent component) {
        children.remove(component);
        log.debug("从目录 {} 移除 {}", this.name, component.getName());
    }
    
    @Override
    public long getSize() {
        long totalSize = 0;
        for (FileSystemComponent child : children) {
            totalSize += child.getSize();
        }
        return totalSize;
    }
    
    @Override
    public int getFileCount() {
        int totalFiles = 0;
        for (FileSystemComponent child : children) {
            totalFiles += child.getFileCount();
        }
        return totalFiles;
    }
    
    @Override
    public void display(int depth) {
        log.info("{}📁 {} ({} 项)", getIndent(depth), name, children.size());
        
        // 递归显示所有子组件
        for (FileSystemComponent child : children) {
            child.display(depth + 1);
        }
    }
    
    @Override
    public FileSystemComponent search(String name) {
        // 首先检查当前目录名称
        if (this.name.equals(name)) {
            return this;
        }
        
        // 递归搜索子组件
        for (FileSystemComponent child : children) {
            FileSystemComponent found = child.search(name);
            if (found != null) {
                return found;
            }
        }
        
        return null;
    }
    
    /**
     * 获取子组件列表
     */
    public List<FileSystemComponent> getChildren() {
        return new ArrayList<>(children);
    }
    
    /**
     * 获取指定类型的文件
     */
    public List<File> getFilesByType(String extension) {
        List<File> result = new ArrayList<>();
        
        for (FileSystemComponent child : children) {
            if (child instanceof File) {
                File file = (File) child;
                if (file.isType(extension)) {
                    result.add(file);
                }
            } else if (child instanceof Directory) {
                Directory dir = (Directory) child;
                result.addAll(dir.getFilesByType(extension));
            }
        }
        
        return result;
    }
    
    /**
     * 判断目录是否为空
     */
    public boolean isEmpty() {
        return children.isEmpty();
    }
    
    /**
     * 清空目录
     */
    public void clear() {
        children.clear();
        log.info("目录 {} 已清空", name);
    }
}
