package com.learning.designpatterns.structural.proxy;

import lombok.Getter;

/**
 * 用户类
 * 
 * <AUTHOR>
 */
@Getter
public class User {
    
    private final String name;
    private final String username;
    private final UserRole role;
    
    public User(String name, String username, UserRole role) {
        this.name = name;
        this.username = username;
        this.role = role;
    }
    
    @Override
    public String toString() {
        return String.format("%s (%s, %s)", name, username, role);
    }
}
