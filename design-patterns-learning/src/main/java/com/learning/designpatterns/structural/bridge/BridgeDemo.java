package com.learning.designpatterns.structural.bridge;

import lombok.extern.slf4j.Slf4j;

/**
 * 桥接模式演示类
 * 
 * 桥接模式将抽象部分与它的实现部分分离，使它们都可以独立地变化。
 * 这种模式涉及到一个作为桥接的接口，使得实体类的功能独立于接口实现类。
 * 
 * 应用场景：
 * - 图形绘制系统（形状和绘制API分离）
 * - 数据库驱动（数据库操作和具体数据库分离）
 * - 消息发送系统（消息类型和发送方式分离）
 * - 设备控制系统（设备和控制方式分离）
 * - 跨平台应用（业务逻辑和平台实现分离）
 * 
 * <AUTHOR>
 */
@Slf4j
public class BridgeDemo {
    
    public static void main(String[] args) {
        log.info("=== 桥接模式演示 ===");
        
        // 演示图形绘制系统
        demonstrateGraphicsSystem();

        // 演示数据库访问系统
        demonstrateDatabaseSystem();

        // 演示其他桥接模式应用
        demonstrateOtherBridgeApplications();
    }
    
    private static void demonstrateGraphicsSystem() {
        log.info("\n--- 图形绘制系统桥接模式演示 ---");
        
        // 创建不同的绘制API实现
        DrawingAPI windowsAPI = new WindowsDrawingAPI();
        DrawingAPI linuxAPI = new LinuxDrawingAPI();
        DrawingAPI macAPI = new MacDrawingAPI();
        
        log.info("🎨 创建不同平台的绘制API");
        
        // 创建不同的形状，使用不同的绘制API
        log.info("\n🔵 在Windows平台绘制图形:");
        Shape circle1 = new Circle(windowsAPI, 5, 10, 20);
        Shape rectangle1 = new Rectangle(windowsAPI, 15, 25, 100, 50);
        
        circle1.draw();
        rectangle1.draw();
        circle1.resize(1.5);
        circle1.draw();
        
        log.info("\n🔵 在Linux平台绘制图形:");
        Shape circle2 = new Circle(linuxAPI, 8, 30, 40);
        Shape rectangle2 = new Rectangle(linuxAPI, 20, 30, 80, 60);
        
        circle2.draw();
        rectangle2.draw();
        rectangle2.resize(0.8);
        rectangle2.draw();
        
        log.info("\n🔵 在Mac平台绘制图形:");
        Shape circle3 = new Circle(macAPI, 12, 50, 60);
        Shape triangle = new Triangle(macAPI, 15, 70, 80, 90, 100, 110);
        
        circle3.draw();
        triangle.draw();
        triangle.resize(1.2);
        triangle.draw();
        
        // 演示运行时切换绘制API
        log.info("\n🔄 运行时切换绘制API:");
        circle1.setDrawingAPI(linuxAPI);
        circle1.draw();
        
        rectangle1.setDrawingAPI(macAPI);
        rectangle1.draw();
    }
    
    private static void demonstrateDatabaseSystem() {
        log.info("\n--- 数据库访问系统桥接模式演示 ---");

        // 创建不同的数据库驱动
        DatabaseDriver mysqlDriver = new MySQLDriver();

        log.info("🗄️ 创建数据库访问对象");

        // 使用MySQL驱动
        log.info("\n📊 使用MySQL数据库:");
        mysqlDriver.connect("**********************************************");

        // 执行查询
        mysqlDriver.executeQuery("SELECT * FROM users WHERE status = 'active'");

        // 执行事务
        mysqlDriver.beginTransaction();
        mysqlDriver.executeUpdate("UPDATE users SET last_login = NOW() WHERE id = 1");
        mysqlDriver.executeUpdate("INSERT INTO login_log (user_id, login_time) VALUES (1, NOW())");
        mysqlDriver.commitTransaction();

        mysqlDriver.close();

        log.info("\n💡 桥接模式的体现:");
        log.info("   • 抽象部分: 数据库操作逻辑（查询、更新、事务）");
        log.info("   • 实现部分: 具体数据库驱动（MySQL、Oracle、PostgreSQL）");
        log.info("   • 优势: 业务逻辑与数据库实现完全分离");
        log.info("   • 扩展性: 可以轻松支持新的数据库类型");
    }

    private static void demonstrateOtherBridgeApplications() {
        log.info("\n--- 其他桥接模式应用场景 ---");

        log.info("📱 消息发送系统:");
        log.info("   • 抽象部分: 文本消息、HTML消息、加密消息");
        log.info("   • 实现部分: 邮件发送、短信发送、推送通知");
        log.info("   • 优势: 消息类型和发送方式可以独立变化");

        log.info("\n🎮 设备控制系统:");
        log.info("   • 抽象部分: 电视、音响、智能灯等设备");
        log.info("   • 实现部分: 遥控器、语音控制、手机APP");
        log.info("   • 优势: 设备类型和控制方式可以独立扩展");

        log.info("\n🗄️ 数据库访问系统:");
        log.info("   • 抽象部分: 用户DAO、订单DAO、产品DAO");
        log.info("   • 实现部分: MySQL驱动、Oracle驱动、MongoDB驱动");
        log.info("   • 优势: 业务逻辑和数据库实现分离");

        log.info("\n🌐 跨平台应用:");
        log.info("   • 抽象部分: 窗口、按钮、菜单等UI组件");
        log.info("   • 实现部分: Windows API、Linux API、Mac API");
        log.info("   • 优势: UI逻辑和平台实现独立开发");

        log.info("\n💡 桥接模式的优势:");
        log.info("   ✅ 分离抽象接口及其实现部分");
        log.info("   ✅ 提高可扩展性");
        log.info("   ✅ 实现细节对客户透明");
        log.info("   ✅ 运行时切换实现");
    }
}
