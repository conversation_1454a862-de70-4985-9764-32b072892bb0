package com.learning.designpatterns.structural.flyweight;

/**
 * 粒子类型枚举
 * 
 * <AUTHOR>
 */
public enum ParticleType {
    FIRE("🔥", "火焰", "red"),
    WATER("💧", "水滴", "blue"),
    SMOKE("💨", "烟雾", "gray"),
    SPARK("✨", "火花", "yellow"),
    EXPLOSION("💥", "爆炸", "orange");
    
    private final String icon;
    private final String name;
    private final String color;
    
    ParticleType(String icon, String name, String color) {
        this.icon = icon;
        this.name = name;
        this.color = color;
    }
    
    public String getIcon() {
        return icon;
    }
    
    public String getName() {
        return name;
    }
    
    public String getColor() {
        return color;
    }
}
