package com.learning.designpatterns.structural.adapter;

import lombok.extern.slf4j.Slf4j;

/**
 * VLC播放器实现
 * 
 * 实现了AdvancedMediaPlayer接口，专门播放VLC格式文件
 * 
 * <AUTHOR>
 */
@Slf4j
public class VlcPlayer implements AdvancedMediaPlayer {
    
    @Override
    public void playVlc(String fileName) {
        log.info("🎬 VLC播放器正在播放VLC文件: {}", fileName);
        // 模拟VLC播放逻辑
        simulatePlayback(fileName, "VLC");
    }
    
    @Override
    public void playMp4(String fileName) {
        // VLC播放器不支持MP4格式的直接播放方法
        log.warn("⚠️ VLC播放器不支持通过playMp4方法播放: {}", fileName);
    }
    
    /**
     * 模拟播放过程
     */
    private void simulatePlayback(String fileName, String format) {
        log.info("📀 开始播放 {} 格式文件: {}", format, fileName);
        log.info("🔊 音量: 75%, 播放进度: 0%");
        
        try {
            Thread.sleep(500); // 模拟播放延迟
            log.info("▶️ 播放中... 进度: 50%");
            Thread.sleep(500);
            log.info("✅ 播放完成: {}", fileName);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("播放被中断", e);
        }
    }
}
