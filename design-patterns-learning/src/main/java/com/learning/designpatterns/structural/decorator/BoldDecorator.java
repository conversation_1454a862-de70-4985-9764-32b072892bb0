package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 加粗装饰器
 * 
 * 为文本添加加粗效果的装饰器
 * 
 * <AUTHOR>
 */
@Slf4j
public class BoldDecorator extends TextDecorator {
    
    public BoldDecorator(Text text) {
        super(text);
        log.info("🔤 添加加粗效果");
    }
    
    @Override
    public String render() {
        return "<b>" + text.render() + "</b>";
    }
}
