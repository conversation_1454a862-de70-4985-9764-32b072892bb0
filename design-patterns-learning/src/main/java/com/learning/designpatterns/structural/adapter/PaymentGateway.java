package com.learning.designpatterns.structural.adapter;

import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付网关
 * 
 * 统一管理不同的支付处理器，提供统一的支付接口
 * 演示如何通过适配器模式集成不同的支付系统
 * 
 * <AUTHOR>
 */
@Slf4j
public class PaymentGateway {
    
    private final Map<String, PaymentProcessor> processors;
    
    /**
     * 构造函数
     */
    public PaymentGateway() {
        this.processors = new HashMap<>();
        log.info("🏦 支付网关初始化完成");
    }
    
    /**
     * 添加支付处理器
     * 
     * @param name 处理器名称
     * @param processor 支付处理器
     */
    public void addProcessor(String name, PaymentProcessor processor) {
        processors.put(name, processor);
        log.info("➕ 添加支付处理器: {} - {}", name, processor.getProcessorName());
    }
    
    /**
     * 移除支付处理器
     * 
     * @param name 处理器名称
     */
    public void removeProcessor(String name) {
        PaymentProcessor removed = processors.remove(name);
        if (removed != null) {
            log.info("➖ 移除支付处理器: {} - {}", name, removed.getProcessorName());
        } else {
            log.warn("⚠️ 未找到要移除的支付处理器: {}", name);
        }
    }
    
    /**
     * 处理支付
     * 
     * @param processorName 处理器名称
     * @param amount 支付金额
     * @param currency 货币类型
     * @return 支付是否成功
     */
    public boolean processPayment(String processorName, double amount, String currency) {
        log.info("🏦 支付网关收到支付请求");
        log.info("📋 处理器: {}, 金额: {} {}", processorName, amount, currency);
        
        PaymentProcessor processor = processors.get(processorName);
        
        if (processor == null) {
            log.error("❌ 未找到支付处理器: {}", processorName);
            log.info("📝 可用的处理器: {}", processors.keySet());
            return false;
        }
        
        try {
            log.info("🔄 转发支付请求到: {}", processor.getProcessorName());
            boolean result = processor.processPayment(amount, currency);
            
            if (result) {
                log.info("✅ 支付网关：支付成功通过 {}", processor.getProcessorName());
            } else {
                log.warn("❌ 支付网关：支付失败通过 {}", processor.getProcessorName());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("💥 支付处理过程中发生异常", e);
            return false;
        }
    }
    
    /**
     * 获取所有可用的支付处理器
     * 
     * @return 处理器名称列表
     */
    public Map<String, String> getAvailableProcessors() {
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, PaymentProcessor> entry : processors.entrySet()) {
            result.put(entry.getKey(), entry.getValue().getProcessorName());
        }
        return result;
    }
    
    /**
     * 显示支付网关状态
     */
    public void showStatus() {
        log.info("🏦 支付网关状态报告");
        log.info("📊 已注册处理器数量: {}", processors.size());
        
        if (processors.isEmpty()) {
            log.info("📝 无可用支付处理器");
        } else {
            log.info("📝 可用支付处理器:");
            for (Map.Entry<String, PaymentProcessor> entry : processors.entrySet()) {
                log.info("   • {} -> {}", entry.getKey(), entry.getValue().getProcessorName());
            }
        }
    }
}
