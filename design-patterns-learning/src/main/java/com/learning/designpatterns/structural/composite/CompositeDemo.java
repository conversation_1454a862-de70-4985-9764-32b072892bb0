package com.learning.designpatterns.structural.composite;

import lombok.extern.slf4j.Slf4j;

/**
 * 组合模式演示类
 * 
 * 组合模式将对象组合成树形结构以表示"部分-整体"的层次结构。
 * 组合模式使得用户对单个对象和组合对象的使用具有一致性。
 * 
 * 应用场景：
 * - 文件系统（文件夹和文件）
 * - 组织架构（部门和员工）
 * - UI组件树（容器和控件）
 * - 菜单系统（菜单和菜单项）
 * - 数学表达式（操作符和操作数）
 * 
 * <AUTHOR>
 */
@Slf4j
public class CompositeDemo {
    
    public static void main(String[] args) {
        log.info("=== 组合模式演示 ===");
        
        // 演示文件系统
        demonstrateFileSystem();
        
        // 演示组织架构
        demonstrateOrganization();
        
        // 演示UI组件树
        demonstrateUIComponents();
    }
    
    private static void demonstrateFileSystem() {
        log.info("\n--- 文件系统组合模式演示 ---");
        
        // 创建根目录
        FileSystemComponent root = new Directory("根目录");
        
        // 创建子目录
        FileSystemComponent documents = new Directory("文档");
        FileSystemComponent pictures = new Directory("图片");
        FileSystemComponent videos = new Directory("视频");
        
        // 创建文件
        FileSystemComponent readme = new File("README.txt", 1024);
        FileSystemComponent config = new File("config.json", 512);
        FileSystemComponent photo1 = new File("vacation.jpg", 2048000);
        FileSystemComponent photo2 = new File("family.png", 1536000);
        FileSystemComponent movie = new File("movie.mp4", 104857600);
        
        // 构建文件系统树
        log.info("🗂️ 构建文件系统结构:");
        
        // 根目录添加文件和子目录
        root.add(readme);
        root.add(config);
        root.add(documents);
        root.add(pictures);
        root.add(videos);
        
        // 文档目录添加文件
        FileSystemComponent doc1 = new File("report.docx", 25600);
        FileSystemComponent doc2 = new File("presentation.pptx", 51200);
        documents.add(doc1);
        documents.add(doc2);
        
        // 图片目录添加文件
        pictures.add(photo1);
        pictures.add(photo2);
        
        // 视频目录添加文件
        videos.add(movie);
        
        // 显示文件系统结构
        log.info("\n📁 文件系统结构:");
        root.display(0);
        
        // 计算总大小
        log.info("\n📊 文件系统统计:");
        log.info("总大小: {} 字节", root.getSize());
        log.info("总文件数: {} 个", root.getFileCount());
        
        // 搜索文件
        log.info("\n🔍 搜索操作:");
        FileSystemComponent found = root.search("vacation.jpg");
        if (found != null) {
            log.info("找到文件: {}", found.getName());
        }
        
        // 删除文件
        log.info("\n🗑️ 删除操作:");
        pictures.remove(photo2);
        log.info("删除文件后的图片目录:");
        pictures.display(0);
    }
    
    private static void demonstrateOrganization() {
        log.info("\n--- 组织架构组合模式演示 ---");
        
        // 创建公司组织架构
        OrganizationComponent company = new Department("科技有限公司");
        
        // 创建部门
        OrganizationComponent techDept = new Department("技术部");
        OrganizationComponent salesDept = new Department("销售部");
        OrganizationComponent hrDept = new Department("人事部");
        
        // 创建员工
        OrganizationComponent ceo = new Employee("张总", "CEO", 50000);
        OrganizationComponent cto = new Employee("李总", "CTO", 40000);
        OrganizationComponent dev1 = new Employee("王工程师", "高级开发", 25000);
        OrganizationComponent dev2 = new Employee("赵工程师", "中级开发", 20000);
        OrganizationComponent sales1 = new Employee("陈经理", "销售经理", 22000);
        OrganizationComponent sales2 = new Employee("刘专员", "销售专员", 15000);
        OrganizationComponent hr1 = new Employee("孙主管", "人事主管", 18000);
        
        // 构建组织架构
        log.info("🏢 构建组织架构:");
        
        // 公司层级
        company.add(ceo);
        company.add(techDept);
        company.add(salesDept);
        company.add(hrDept);
        
        // 技术部
        techDept.add(cto);
        techDept.add(dev1);
        techDept.add(dev2);
        
        // 销售部
        salesDept.add(sales1);
        salesDept.add(sales2);
        
        // 人事部
        hrDept.add(hr1);
        
        // 显示组织架构
        log.info("\n🏢 组织架构:");
        company.display(0);
        
        // 计算统计信息
        log.info("\n📊 组织统计:");
        log.info("总员工数: {} 人", company.getEmployeeCount());
        log.info("总薪资: {} 元", company.getTotalSalary());
        
        // 查找员工
        log.info("\n🔍 查找员工:");
        OrganizationComponent found = company.findEmployee("王工程师");
        if (found != null) {
            log.info("找到员工: {}", found.getName());
        }
    }
    
    private static void demonstrateUIComponents() {
        log.info("\n--- UI组件树组合模式演示 ---");
        
        // 创建主窗口
        UIComponent mainWindow = new Container("主窗口", "Window");
        
        // 创建面板
        UIComponent headerPanel = new Container("头部面板", "Panel");
        UIComponent contentPanel = new Container("内容面板", "Panel");
        UIComponent footerPanel = new Container("底部面板", "Panel");
        
        // 创建控件
        UIComponent logo = new Control("Logo", "Image");
        UIComponent menuBar = new Container("菜单栏", "MenuBar");
        UIComponent loginBtn = new Control("登录按钮", "Button");
        UIComponent searchBox = new Control("搜索框", "TextBox");
        UIComponent dataTable = new Control("数据表格", "Table");
        UIComponent statusBar = new Control("状态栏", "StatusBar");
        
        // 创建菜单项
        UIComponent fileMenu = new Control("文件菜单", "Menu");
        UIComponent editMenu = new Control("编辑菜单", "Menu");
        UIComponent helpMenu = new Control("帮助菜单", "Menu");
        
        // 构建UI组件树
        log.info("🖥️ 构建UI组件树:");
        
        // 主窗口
        mainWindow.add(headerPanel);
        mainWindow.add(contentPanel);
        mainWindow.add(footerPanel);
        
        // 头部面板
        headerPanel.add(logo);
        headerPanel.add(menuBar);
        headerPanel.add(loginBtn);
        
        // 菜单栏
        menuBar.add(fileMenu);
        menuBar.add(editMenu);
        menuBar.add(helpMenu);
        
        // 内容面板
        contentPanel.add(searchBox);
        contentPanel.add(dataTable);
        
        // 底部面板
        footerPanel.add(statusBar);
        
        // 显示UI组件树
        log.info("\n🖥️ UI组件结构:");
        mainWindow.display(0);
        
        // 渲染所有组件
        log.info("\n🎨 渲染UI组件:");
        mainWindow.render();
        
        // 统计组件数量
        log.info("\n📊 UI统计:");
        log.info("总组件数: {} 个", mainWindow.getComponentCount());
    }
}
