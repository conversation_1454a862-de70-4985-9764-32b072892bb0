package com.learning.designpatterns.structural.adapter;

/**
 * 支付处理器接口
 * 
 * 定义了现代支付系统的标准接口
 * 
 * <AUTHOR>
 */
public interface PaymentProcessor {
    
    /**
     * 处理支付
     * 
     * @param amount 支付金额
     * @param currency 货币类型
     * @return 支付是否成功
     */
    boolean processPayment(double amount, String currency);
    
    /**
     * 获取支付处理器名称
     * 
     * @return 处理器名称
     */
    String getProcessorName();
}
