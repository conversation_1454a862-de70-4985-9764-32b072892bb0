package com.learning.designpatterns.structural.proxy;

import lombok.extern.slf4j.Slf4j;

/**
 * 真实文档类
 * 
 * 实际的文档对象
 * 
 * <AUTHOR>
 */
@Slf4j
public class RealDocument implements Document {
    
    private final String filename;
    private StringBuilder content;
    
    public RealDocument(String filename) {
        this.filename = filename;
        this.content = new StringBuilder("这是一份机密文档的内容...");
        log.info("📄 创建真实文档: {}", filename);
    }
    
    @Override
    public void read(User user) {
        log.info("📖 {} 正在读取文档: {}", user.getName(), filename);
        log.info("📄 文档内容: {}", content.toString());
    }
    
    @Override
    public void write(User user, String newContent) {
        log.info("✏️ {} 正在写入文档: {}", user.getName(), filename);
        content.append("\n").append(newContent);
        log.info("💾 内容已添加: {}", newContent);
    }
    
    @Override
    public void delete(User user) {
        log.info("🗑️ {} 正在删除文档: {}", user.getName(), filename);
        log.info("❌ 文档已删除");
    }
}
