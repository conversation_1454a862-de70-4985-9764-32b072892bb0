package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;
import java.util.Base64;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPOutputStream;
import java.util.zip.GZIPInputStream;
import java.io.ByteArrayInputStream;

/**
 * 压缩装饰器
 * 
 * 为数据流添加压缩功能的装饰器
 * 
 * <AUTHOR>
 */
@Slf4j
public class CompressionDecorator extends DataStreamDecorator {
    
    public CompressionDecorator(DataStream dataStream) {
        super(dataStream);
        log.info("🗜️ 添加压缩功能");
    }
    
    @Override
    public String read() {
        String compressedData = dataStream.read();
        return decompress(compressedData);
    }
    
    @Override
    public void write(String data) {
        String compressedData = compress(data);
        dataStream.write(compressedData);
    }
    
    /**
     * 压缩数据
     * 
     * @param data 原始数据
     * @return 压缩后的数据
     */
    private String compress(String data) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            GZIPOutputStream gzipOut = new GZIPOutputStream(baos);
            gzipOut.write(data.getBytes("UTF-8"));
            gzipOut.close();
            
            byte[] compressed = baos.toByteArray();
            String result = Base64.getEncoder().encodeToString(compressed);
            
            log.info("🗜️ 数据压缩: {}字节 -> {}字节 (压缩率: {:.1f}%)", 
                    data.length(), result.length(), 
                    (1.0 - (double)result.length() / data.length()) * 100);
            
            return result;
        } catch (IOException e) {
            log.error("压缩失败", e);
            return data; // 压缩失败时返回原数据
        }
    }
    
    /**
     * 解压数据
     * 
     * @param compressedData 压缩的数据
     * @return 解压后的数据
     */
    private String decompress(String compressedData) {
        try {
            byte[] compressed = Base64.getDecoder().decode(compressedData);
            ByteArrayInputStream bais = new ByteArrayInputStream(compressed);
            GZIPInputStream gzipIn = new GZIPInputStream(bais);
            
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipIn.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
            gzipIn.close();
            
            String result = baos.toString("UTF-8");
            log.info("📂 数据解压: {}字节 -> {}字节", compressedData.length(), result.length());
            
            return result;
        } catch (IOException e) {
            log.error("解压失败", e);
            return compressedData; // 解压失败时返回原数据
        }
    }
}
