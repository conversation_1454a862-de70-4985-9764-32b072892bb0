package com.learning.designpatterns.structural.bridge;

import lombok.extern.slf4j.Slf4j;

/**
 * Mac绘制API实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class MacDrawingAPI implements DrawingAPI {
    
    @Override
    public void drawCircle(double x, double y, double radius) {
        // 模拟Mac Quartz 2D的具体绘制实现
        log.info("🍎 Mac Quartz 2D API:");
        log.info("   CGContextRef context = UIGraphicsGetCurrentContext()");
        log.info("   CGContextSetRGBStrokeColor(context, 0.0, 0.0, 0.0, 1.0)");
        log.info("   CGContextAddEllipseInRect(context, CGRectMake({}, {}, {}, {}))",
                x-radius, y-radius, radius*2, radius*2);
        log.info("   CGContextStrokePath(context)");
        log.info("   ✅ 圆形绘制完成 - 使用Core Graphics");
    }

    @Override
    public void drawRectangle(double x, double y, double width, double height) {
        // 模拟Mac Quartz 2D的具体绘制实现
        log.info("🍎 Mac Quartz 2D API:");
        log.info("   CGContextRef context = UIGraphicsGetCurrentContext()");
        log.info("   CGContextSetRGBStrokeColor(context, 0.0, 0.0, 0.0, 1.0)");
        log.info("   CGContextAddRect(context, CGRectMake({}, {}, {}, {}))", x, y, width, height);
        log.info("   CGContextStrokePath(context)");
        log.info("   ✅ 矩形绘制完成 - 使用Core Graphics");
    }

    @Override
    public void drawTriangle(double x1, double y1, double x2, double y2, double x3, double y3) {
        // 模拟Mac Quartz 2D的具体绘制实现
        log.info("🍎 Mac Quartz 2D API:");
        log.info("   CGContextRef context = UIGraphicsGetCurrentContext()");
        log.info("   CGContextSetRGBStrokeColor(context, 0.0, 0.0, 0.0, 1.0)");
        log.info("   CGContextMoveToPoint(context, {}, {})", x1, y1);
        log.info("   CGContextAddLineToPoint(context, {}, {})", x2, y2);
        log.info("   CGContextAddLineToPoint(context, {}, {})", x3, y3);
        log.info("   CGContextClosePath(context)");
        log.info("   CGContextStrokePath(context)");
        log.info("   ✅ 三角形绘制完成 - 使用Core Graphics");
    }

    @Override
    public void drawLine(double x1, double y1, double x2, double y2) {
        // 模拟Mac Quartz 2D的具体绘制实现
        log.info("🍎 Mac Quartz 2D API:");
        log.info("   CGContextRef context = UIGraphicsGetCurrentContext()");
        log.info("   CGContextSetRGBStrokeColor(context, 0.0, 0.0, 0.0, 1.0)");
        log.info("   CGContextMoveToPoint(context, {}, {})", x1, y1);
        log.info("   CGContextAddLineToPoint(context, {}, {})", x2, y2);
        log.info("   CGContextStrokePath(context)");
        log.info("   ✅ 线条绘制完成 - 使用Core Graphics");
    }
    
    @Override
    public String getAPIName() {
        return "Mac Quartz 2D";
    }
}
