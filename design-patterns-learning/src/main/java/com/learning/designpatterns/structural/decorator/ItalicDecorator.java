package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 斜体装饰器
 * 
 * 为文本添加斜体效果的装饰器
 * 
 * <AUTHOR>
 */
@Slf4j
public class ItalicDecorator extends TextDecorator {
    
    public ItalicDecorator(Text text) {
        super(text);
        log.info("📐 添加斜体效果");
    }
    
    @Override
    public String render() {
        return "<i>" + text.render() + "</i>";
    }
}
