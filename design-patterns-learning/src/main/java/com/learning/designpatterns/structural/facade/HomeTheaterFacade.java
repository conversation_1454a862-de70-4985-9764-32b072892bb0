package com.learning.designpatterns.structural.facade;

import lombok.extern.slf4j.Slf4j;

/**
 * 家庭影院外观类
 * 
 * 简化家庭影院系统的复杂操作
 * 
 * <AUTHOR>
 */
@Slf4j
public class HomeTheaterFacade {
    
    private final Amplifier amplifier;
    private final DvdPlayer dvdPlayer;
    private final Projector projector;
    private final Screen screen;
    private final TheaterLights lights;
    private final PopcornPopper popper;
    
    public HomeTheaterFacade(Amplifier amplifier, DvdPlayer dvdPlayer, 
                           Projector projector, Screen screen, 
                           TheaterLights lights, PopcornPopper popper) {
        this.amplifier = amplifier;
        this.dvdPlayer = dvdPlayer;
        this.projector = projector;
        this.screen = screen;
        this.lights = lights;
        this.popper = popper;
    }
    
    public void watchMovie(String movie) {
        log.info("🎬 准备观看电影: {}", movie);
        
        popper.on();
        popper.pop();
        lights.dim(10);
        screen.down();
        projector.on();
        projector.setInput(dvdPlayer);
        amplifier.on();
        amplifier.setDvd(dvdPlayer);
        amplifier.setSurroundSound();
        amplifier.setVolume(5);
        dvdPlayer.on();
        dvdPlayer.play(movie);
        
        log.info("✅ 观影环境已准备就绪，请享受电影！");
    }
    
    public void endMovie() {
        log.info("🔚 结束观影，关闭所有设备");
        
        popper.off();
        lights.on();
        screen.up();
        projector.off();
        amplifier.off();
        dvdPlayer.stop();
        dvdPlayer.eject();
        dvdPlayer.off();
        
        log.info("✅ 所有设备已关闭");
    }
}
