package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 纯文本实现
 * 
 * 基础的文本实现，作为装饰器的基础组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class PlainText implements Text {
    
    private final String content;
    
    public PlainText(String content) {
        this.content = content;
        log.info("📝 创建纯文本: {}", content);
    }
    
    @Override
    public String render() {
        return content;
    }
}
