package com.learning.designpatterns.structural.bridge;

import lombok.extern.slf4j.Slf4j;

/**
 * 邮件发送实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class EmailSender implements MessageSender {
    
    @Override
    public void sendMessage(String recipient, String content) {
        log.info("📧 通过邮件发送消息");
        log.info("   收件人: {}", recipient);
        log.info("   内容: {}", content);
        log.info("   ✅ 邮件发送成功");
    }
    
    @Override
    public String getSenderType() {
        return "邮件";
    }
}
