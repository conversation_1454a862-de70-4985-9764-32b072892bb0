package com.learning.designpatterns.structural.adapter;

import lombok.extern.slf4j.Slf4j;

/**
 * 遗留支付系统适配器
 * 
 * 将遗留支付系统适配到现代PaymentProcessor接口
 * 处理接口差异和数据格式转换
 * 
 * <AUTHOR>
 */
@Slf4j
public class LegacyPaymentAdapter implements PaymentProcessor {
    
    private final LegacyPaymentSystem legacySystem;
    
    /**
     * 构造函数
     */
    public LegacyPaymentAdapter() {
        this.legacySystem = new LegacyPaymentSystem();
        log.info("🔧 创建遗留支付系统适配器");
        log.info("📟 集成系统: {}", legacySystem.getSystemVersion());
    }
    
    @Override
    public boolean processPayment(double amount, String currency) {
        log.info("🔄 适配器接收现代支付请求，准备转换为遗留系统格式");
        
        // 转换金额：从元转换为分
        int amountInCents = convertToLegacyAmount(amount);
        
        // 转换货币代码：现代系统使用ISO代码，遗留系统使用旧格式
        String legacyCurrency = convertToLegacyCurrency(currency);
        
        log.info("🔄 数据转换完成 - 金额: {} 元 -> {} 分, 货币: {} -> {}", 
                amount, amountInCents, currency, legacyCurrency);
        
        // 调用遗留系统
        int resultCode = legacySystem.makePayment(amountInCents, legacyCurrency);
        
        // 转换返回结果：遗留系统返回错误代码，现代系统需要布尔值
        boolean success = convertLegacyResult(resultCode);
        
        log.info("🔄 结果转换完成 - 错误代码: {} -> 成功状态: {}", resultCode, success);
        
        return success;
    }
    
    @Override
    public String getProcessorName() {
        return "遗留支付系统适配器";
    }
    
    /**
     * 将现代金额格式转换为遗留系统格式
     * 
     * @param amount 以元为单位的金额
     * @return 以分为单位的金额
     */
    private int convertToLegacyAmount(double amount) {
        // 转换为分，并处理精度问题
        return (int) Math.round(amount * 100);
    }
    
    /**
     * 将现代货币代码转换为遗留系统格式
     * 
     * @param modernCurrency 现代货币代码（如USD, CNY）
     * @return 遗留系统货币代码
     */
    private String convertToLegacyCurrency(String modernCurrency) {
        // 遗留系统使用不同的货币代码格式
        return switch (modernCurrency.toUpperCase()) {
            case "USD" -> "US_DOLLAR";
            case "CNY" -> "CHINESE_YUAN";
            case "EUR" -> "EURO";
            case "JPY" -> "JAPANESE_YEN";
            default -> "UNKNOWN_CURRENCY";
        };
    }
    
    /**
     * 将遗留系统的错误代码转换为现代系统的布尔结果
     * 
     * @param resultCode 遗留系统返回的错误代码
     * @return 是否成功
     */
    private boolean convertLegacyResult(int resultCode) {
        // 遗留系统：0表示成功，其他表示失败
        return resultCode == 0;
    }
}
