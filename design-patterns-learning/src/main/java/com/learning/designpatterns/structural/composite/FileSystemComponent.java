package com.learning.designpatterns.structural.composite;

/**
 * 文件系统组件抽象类
 * 
 * 定义了文件系统中文件和目录的公共接口
 * 
 * <AUTHOR>
 */
public abstract class FileSystemComponent {
    
    protected String name;
    
    public FileSystemComponent(String name) {
        this.name = name;
    }
    
    /**
     * 获取名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取大小
     */
    public abstract long getSize();
    
    /**
     * 获取文件数量
     */
    public abstract int getFileCount();
    
    /**
     * 显示结构
     */
    public abstract void display(int depth);
    
    /**
     * 添加子组件（只有容器支持）
     */
    public void add(FileSystemComponent component) {
        throw new UnsupportedOperationException("不支持添加操作");
    }
    
    /**
     * 移除子组件（只有容器支持）
     */
    public void remove(FileSystemComponent component) {
        throw new UnsupportedOperationException("不支持移除操作");
    }
    
    /**
     * 搜索文件
     */
    public abstract FileSystemComponent search(String name);
    
    /**
     * 获取缩进字符串
     */
    protected String getIndent(int depth) {
        return "  ".repeat(depth);
    }
}
