package com.learning.designpatterns.structural.facade;

import lombok.extern.slf4j.Slf4j;

/**
 * 计算机外观类
 * 
 * 简化计算机启动、重启、关闭的复杂过程
 * 
 * <AUTHOR>
 */
@Slf4j
public class ComputerFacade {
    
    public void start() {
        log.info("💻 启动计算机...");
        log.info("🔌 检查电源连接");
        log.info("🧠 初始化CPU");
        log.info("💾 检测内存");
        log.info("💿 加载操作系统");
        log.info("🖥️ 启动图形界面");
        log.info("✅ 计算机启动完成");
    }
    
    public void shutdown() {
        log.info("⚡ 关闭计算机...");
        log.info("💾 保存用户数据");
        log.info("🔄 关闭运行程序");
        log.info("🖥️ 关闭图形界面");
        log.info("💿 卸载操作系统");
        log.info("🔌 断开电源");
        log.info("✅ 计算机已关闭");
    }
    
    public void restart() {
        log.info("🔄 重启计算机...");
        shutdown();
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        start();
    }
}
