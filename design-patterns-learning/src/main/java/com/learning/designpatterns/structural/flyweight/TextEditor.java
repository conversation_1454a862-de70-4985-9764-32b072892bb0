package com.learning.designpatterns.structural.flyweight;

import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * 文本编辑器类
 * 
 * 使用享元模式管理文本中的字符
 * 
 * <AUTHOR>
 */
@Slf4j
public class TextEditor {
    
    private final List<CharacterContext> characters;
    private final CharacterFlyweightFactory factory;
    
    public TextEditor() {
        this.characters = new ArrayList<>();
        this.factory = CharacterFlyweightFactory.getInstance();
        log.info("📝 文本编辑器初始化");
    }
    
    /**
     * 添加字符
     * 
     * @param character 字符
     * @param font 字体
     * @param size 大小
     * @param color 颜色
     * @param x X坐标
     * @param y Y坐标
     */
    public void addCharacter(char character, String font, int size, String color, int x, int y) {
        CharacterFlyweight flyweight = factory.getFlyweight(character);
        CharacterContext context = new CharacterContext(flyweight, font, size, color, x, y);
        characters.add(context);
    }
    
    /**
     * 渲染所有字符
     */
    public void render() {
        log.info("🎨 开始渲染文本 ({} 个字符):", characters.size());
        
        for (int i = 0; i < Math.min(characters.size(), 10); i++) { // 只显示前10个字符
            CharacterContext context = characters.get(i);
            context.render();
        }
        
        if (characters.size() > 10) {
            log.info("... 还有 {} 个字符", characters.size() - 10);
        }
        
        log.info("✅ 文本渲染完成");
    }
    
    /**
     * 获取字符数量
     * 
     * @return 字符数量
     */
    public int getCharacterCount() {
        return characters.size();
    }
    
    /**
     * 字符上下文类 - 存储外部状态
     */
    private static class CharacterContext {
        private final CharacterFlyweight flyweight;
        private final String font;
        private final int size;
        private final String color;
        private final int x;
        private final int y;
        
        public CharacterContext(CharacterFlyweight flyweight, String font, int size, 
                              String color, int x, int y) {
            this.flyweight = flyweight;
            this.font = font;
            this.size = size;
            this.color = color;
            this.x = x;
            this.y = y;
        }
        
        public void render() {
            flyweight.render(font, size, color, x, y);
        }
    }
}
