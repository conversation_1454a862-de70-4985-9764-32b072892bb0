package com.learning.designpatterns.structural.composite;

/**
 * UI组件抽象类
 * 
 * 定义了UI组件的公共接口
 * 
 * <AUTHOR>
 */
public abstract class UIComponent {
    
    protected String name;
    protected String type;
    
    public UIComponent(String name, String type) {
        this.name = name;
        this.type = type;
    }
    
    /**
     * 获取名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取类型
     */
    public String getType() {
        return type;
    }
    
    /**
     * 渲染组件
     */
    public abstract void render();
    
    /**
     * 显示组件结构
     */
    public abstract void display(int depth);
    
    /**
     * 获取组件数量
     */
    public abstract int getComponentCount();
    
    /**
     * 添加子组件（只有容器支持）
     */
    public void add(UIComponent component) {
        throw new UnsupportedOperationException("不支持添加操作");
    }
    
    /**
     * 移除子组件（只有容器支持）
     */
    public void remove(UIComponent component) {
        throw new UnsupportedOperationException("不支持移除操作");
    }
    
    /**
     * 获取缩进字符串
     */
    protected String getIndent(int depth) {
        return "  ".repeat(depth);
    }
}
