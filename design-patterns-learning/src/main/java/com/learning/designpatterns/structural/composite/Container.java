package com.learning.designpatterns.structural.composite;

import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * 容器类（组合节点）
 * 
 * 表示UI中的容器组件，可以包含其他UI组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class Container extends UIComponent {
    
    private final List<UIComponent> children;
    
    public Container(String name, String type) {
        super(name, type);
        this.children = new ArrayList<>();
    }
    
    @Override
    public void add(UIComponent component) {
        children.add(component);
        log.debug("添加组件 {} 到容器 {}", component.getName(), this.name);
    }
    
    @Override
    public void remove(UIComponent component) {
        children.remove(component);
        log.debug("从容器 {} 移除组件 {}", this.name, component.getName());
    }
    
    @Override
    public void render() {
        log.info("🎨 渲染容器: {} ({})", name, type);
        
        // 递归渲染所有子组件
        for (UIComponent child : children) {
            child.render();
        }
        
        log.info("✅ 容器 {} 渲染完成", name);
    }
    
    @Override
    public void display(int depth) {
        String icon = getContainerIcon(type);
        log.info("{}{} {} ({}) - {} 个子组件", 
                getIndent(depth), icon, name, type, children.size());
        
        // 递归显示所有子组件
        for (UIComponent child : children) {
            child.display(depth + 1);
        }
    }
    
    @Override
    public int getComponentCount() {
        int totalComponents = 1; // 容器本身
        for (UIComponent child : children) {
            totalComponents += child.getComponentCount();
        }
        return totalComponents;
    }
    
    /**
     * 根据容器类型获取图标
     */
    private String getContainerIcon(String type) {
        return switch (type.toLowerCase()) {
            case "window" -> "🪟";
            case "panel" -> "📦";
            case "menubar" -> "📋";
            case "toolbar" -> "🔧";
            case "dialog" -> "💬";
            default -> "📁";
        };
    }
    
    /**
     * 获取子组件列表
     */
    public List<UIComponent> getChildren() {
        return new ArrayList<>(children);
    }
    
    /**
     * 根据名称查找组件
     */
    public UIComponent findComponent(String name) {
        if (this.name.equals(name)) {
            return this;
        }
        
        for (UIComponent child : children) {
            if (child.getName().equals(name)) {
                return child;
            }
            
            if (child instanceof Container) {
                Container container = (Container) child;
                UIComponent found = container.findComponent(name);
                if (found != null) {
                    return found;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 获取指定类型的组件
     */
    public List<UIComponent> getComponentsByType(String type) {
        List<UIComponent> result = new ArrayList<>();
        
        if (this.type.equals(type)) {
            result.add(this);
        }
        
        for (UIComponent child : children) {
            if (child.getType().equals(type)) {
                result.add(child);
            }
            
            if (child instanceof Container) {
                Container container = (Container) child;
                result.addAll(container.getComponentsByType(type));
            }
        }
        
        return result;
    }
    
    /**
     * 清空容器
     */
    public void clear() {
        children.clear();
        log.info("容器 {} 已清空", name);
    }
}
