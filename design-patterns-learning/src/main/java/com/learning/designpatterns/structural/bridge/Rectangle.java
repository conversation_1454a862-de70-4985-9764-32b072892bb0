package com.learning.designpatterns.structural.bridge;

import lombok.extern.slf4j.Slf4j;

/**
 * 矩形类
 * 
 * 具体的形状实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class Rectangle extends Shape {
    
    private double x, y, width, height;
    
    public Rectangle(DrawingAPI drawingAPI, double x, double y, double width, double height) {
        super(drawingAPI);
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
    
    @Override
    public void draw() {
        log.info("🔲 绘制矩形 - 使用 {}", drawingAPI.getAPIName());
        drawingAPI.drawRectangle(x, y, width, height);
    }
    
    @Override
    public void resize(double factor) {
        width *= factor;
        height *= factor;
        log.info("🔄 矩形大小调整，新尺寸: {}x{}", width, height);
    }
    
    @Override
    public String getShapeInfo() {
        return String.format("矩形[位置: (%.1f, %.1f), 尺寸: %.1fx%.1f]", x, y, width, height);
    }
    
    /**
     * 移动矩形
     * 
     * @param newX 新的X坐标
     * @param newY 新的Y坐标
     */
    public void move(double newX, double newY) {
        this.x = newX;
        this.y = newY;
        log.info("📍 矩形移动到新位置: ({}, {})", x, y);
    }
    
    /**
     * 获取矩形面积
     * 
     * @return 面积
     */
    public double getArea() {
        return width * height;
    }
    
    /**
     * 获取矩形周长
     * 
     * @return 周长
     */
    public double getPerimeter() {
        return 2 * (width + height);
    }
}
