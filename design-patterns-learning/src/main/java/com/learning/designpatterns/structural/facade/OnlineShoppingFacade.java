package com.learning.designpatterns.structural.facade;

import lombok.extern.slf4j.Slf4j;

/**
 * 在线购物外观类
 * 
 * 简化在线购物的复杂流程
 * 
 * <AUTHOR>
 */
@Slf4j
public class OnlineShoppingFacade {
    
    public boolean purchaseProduct(String productName, String customerName, String creditCard) {
        log.info("🛒 开始购买流程: {}", productName);
        
        try {
            // 1. 验证产品库存
            log.info("📦 检查产品库存: {}", productName);
            if (!checkInventory(productName)) {
                log.warn("❌ 产品库存不足");
                return false;
            }
            
            // 2. 验证客户信息
            log.info("👤 验证客户信息: {}", customerName);
            if (!validateCustomer(customerName)) {
                log.warn("❌ 客户信息验证失败");
                return false;
            }
            
            // 3. 处理支付
            log.info("💳 处理支付: {}", creditCard.substring(0, 4) + "****");
            if (!processPayment(creditCard)) {
                log.warn("❌ 支付处理失败");
                return false;
            }
            
            // 4. 更新库存
            log.info("📊 更新库存");
            updateInventory(productName);
            
            // 5. 安排发货
            log.info("🚚 安排发货");
            scheduleShipping(customerName, productName);
            
            // 6. 发送确认邮件
            log.info("📧 发送订单确认邮件");
            sendConfirmationEmail(customerName, productName);
            
            log.info("✅ 购买成功完成！");
            return true;
            
        } catch (Exception e) {
            log.error("❌ 购买过程中发生错误", e);
            return false;
        }
    }
    
    private boolean checkInventory(String productName) {
        // 模拟库存检查
        return Math.random() > 0.1; // 90% 成功率
    }
    
    private boolean validateCustomer(String customerName) {
        // 模拟客户验证
        return customerName != null && !customerName.trim().isEmpty();
    }
    
    private boolean processPayment(String creditCard) {
        // 模拟支付处理
        return creditCard != null && creditCard.length() >= 16;
    }
    
    private void updateInventory(String productName) {
        // 模拟库存更新
        log.info("📊 产品 {} 库存已更新", productName);
    }
    
    private void scheduleShipping(String customerName, String productName) {
        // 模拟发货安排
        log.info("🚚 为客户 {} 安排 {} 的发货", customerName, productName);
    }
    
    private void sendConfirmationEmail(String customerName, String productName) {
        // 模拟发送邮件
        log.info("📧 向 {} 发送 {} 的订单确认邮件", customerName, productName);
    }
}
