package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;
import java.util.Base64;

/**
 * Base64编码装饰器
 * 
 * 为数据流添加Base64编码功能的装饰器
 * 
 * <AUTHOR>
 */
@Slf4j
public class Base64Decorator extends DataStreamDecorator {
    
    public Base64Decorator(DataStream dataStream) {
        super(dataStream);
        log.info("📊 添加Base64编码功能");
    }
    
    @Override
    public String read() {
        String encodedData = dataStream.read();
        return decode(encodedData);
    }
    
    @Override
    public void write(String data) {
        String encodedData = encode(data);
        dataStream.write(encodedData);
    }
    
    /**
     * Base64编码
     * 
     * @param data 原始数据
     * @return 编码后的数据
     */
    private String encode(String data) {
        try {
            String result = Base64.getEncoder().encodeToString(data.getBytes("UTF-8"));
            log.info("📊 Base64编码: {}字节 -> {}字节", data.length(), result.length());
            return result;
        } catch (Exception e) {
            log.error("Base64编码失败", e);
            return data;
        }
    }
    
    /**
     * Base64解码
     * 
     * @param encodedData 编码的数据
     * @return 解码后的数据
     */
    private String decode(String encodedData) {
        try {
            byte[] decodedBytes = Base64.getDecoder().decode(encodedData);
            String result = new String(decodedBytes, "UTF-8");
            log.info("📋 Base64解码: {}字节 -> {}字节", encodedData.length(), result.length());
            return result;
        } catch (Exception e) {
            log.error("Base64解码失败", e);
            return encodedData;
        }
    }
}
