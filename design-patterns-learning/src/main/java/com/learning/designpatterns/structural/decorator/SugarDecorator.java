package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 糖装饰器
 * 
 * 为咖啡添加糖的装饰器
 * 
 * <AUTHOR>
 */
@Slf4j
public class SugarDecorator extends CoffeeDecorator {
    
    public SugarDecorator(Coffee coffee) {
        super(coffee);
        log.info("🍯 添加糖");
    }
    
    @Override
    public String getDescription() {
        return coffee.getDescription() + " + 糖";
    }
    
    @Override
    public double getCost() {
        return coffee.getCost() + 1.0; // 糖加1元
    }
}
