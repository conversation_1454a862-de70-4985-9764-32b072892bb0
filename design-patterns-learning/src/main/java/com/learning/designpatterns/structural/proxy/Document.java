package com.learning.designpatterns.structural.proxy;

/**
 * 文档接口
 * 
 * 定义了文档的基本操作
 * 
 * <AUTHOR>
 */
public interface Document {
    
    /**
     * 读取文档
     * 
     * @param user 用户
     */
    void read(User user);
    
    /**
     * 写入文档
     * 
     * @param user 用户
     * @param content 内容
     */
    void write(User user, String content);
    
    /**
     * 删除文档
     * 
     * @param user 用户
     */
    void delete(User user);
}
