package com.learning.designpatterns.structural.flyweight;

import lombok.extern.slf4j.Slf4j;

/**
 * 具体粒子享元类
 * 
 * <AUTHOR>
 */
@Slf4j
public class ConcreteParticleFlyweight implements ParticleFlyweight {
    
    private final ParticleType type; // 内部状态
    
    public ConcreteParticleFlyweight(ParticleType type) {
        this.type = type;
        log.debug("🎆 创建粒子享元对象: {}", type.getName());
    }
    
    @Override
    public void render(double x, double y, double velocityX, double velocityY) {
        log.info("{} {} 位置:({:.1f}, {:.1f}) 速度:({:.1f}, {:.1f})", 
                type.getIcon(), type.getName(), x, y, velocityX, velocityY);
    }
    
    @Override
    public void update(ParticleContext context, double deltaTime) {
        // 更新位置
        context.setX(context.getX() + context.getVelocityX() * deltaTime);
        context.setY(context.getY() + context.getVelocityY() * deltaTime);
        
        // 应用重力（向下的加速度）
        context.setVelocityY(context.getVelocityY() + 9.8 * deltaTime);
        
        // 应用阻力
        context.setVelocityX(context.getVelocityX() * 0.99);
        context.setVelocityY(context.getVelocityY() * 0.99);
        
        // 减少生命值
        context.setLife(context.getLife() - deltaTime);
    }
    
    @Override
    public ParticleType getType() {
        return type;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ConcreteParticleFlyweight that = (ConcreteParticleFlyweight) obj;
        return type == that.type;
    }
    
    @Override
    public int hashCode() {
        return type.hashCode();
    }
}
