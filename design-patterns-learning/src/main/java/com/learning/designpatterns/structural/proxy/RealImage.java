package com.learning.designpatterns.structural.proxy;

import lombok.extern.slf4j.Slf4j;

/**
 * 真实图片类
 * 
 * 实际的图片对象，加载和显示需要消耗大量资源
 * 
 * <AUTHOR>
 */
@Slf4j
public class RealImage implements Image {
    
    private final String filename;
    private final String resolution;
    private final double sizeInMB;
    
    public RealImage(String filename, String resolution, double sizeInMB) {
        this.filename = filename;
        this.resolution = resolution;
        this.sizeInMB = sizeInMB;
        loadImageFromDisk();
    }
    
    /**
     * 模拟从磁盘加载图片的耗时操作
     */
    private void loadImageFromDisk() {
        log.info("💿 正在从磁盘加载图片: {}", filename);
        log.info("📏 分辨率: {}, 大小: {}MB", resolution, sizeInMB);
        
        try {
            // 模拟加载时间，大图片加载更慢
            int loadTime = (int) (sizeInMB * 100); // 每MB需要100ms
            Thread.sleep(loadTime);
            log.info("✅ 图片加载完成，耗时: {}ms", loadTime);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("图片加载被中断", e);
        }
    }
    
    @Override
    public void display() {
        log.info("🖼️ 显示图片: {} ({})", filename, resolution);
        
        // 模拟显示过程
        try {
            Thread.sleep(200); // 显示需要200ms
            log.info("📺 图片显示完成");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("图片显示被中断", e);
        }
    }
    
    @Override
    public String getImageInfo() {
        return String.format("%s (%s, %.1fMB)", filename, resolution, sizeInMB);
    }
}
