package com.learning.designpatterns.structural.adapter;

import lombok.extern.slf4j.Slf4j;

/**
 * XML数据处理器
 * 
 * 专门处理XML格式数据的处理器
 * 这是一个遗留系统，只能处理XML格式
 * 
 * <AUTHOR>
 */
@Slf4j
public class XmlDataProcessor {
    
    /**
     * 处理XML数据
     * 
     * @param xmlData XML格式的数据
     */
    public void processXml(String xmlData) {
        log.info("📄 XML处理器开始处理XML数据");
        log.info("原始XML数据: {}", xmlData);
        
        // 模拟XML解析和处理
        try {
            Thread.sleep(300);
            
            // 简单的XML解析模拟
            if (xmlData.contains("<person>")) {
                log.info("🔍 检测到人员信息XML");
                extractPersonInfo(xmlData);
            } else if (xmlData.contains("<product>")) {
                log.info("🔍 检测到产品信息XML");
                extractProductInfo(xmlData);
            } else {
                log.info("🔍 处理通用XML数据");
            }
            
            Thread.sleep(200);
            log.info("✅ XML数据处理完成");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("XML处理被中断", e);
        }
    }
    
    /**
     * 提取人员信息
     */
    private void extractPersonInfo(String xmlData) {
        // 简单的字符串解析模拟
        String name = extractValue(xmlData, "name");
        String age = extractValue(xmlData, "age");
        String city = extractValue(xmlData, "city");
        
        log.info("👤 解析人员信息 - 姓名: {}, 年龄: {}, 城市: {}", name, age, city);
    }
    
    /**
     * 提取产品信息
     */
    private void extractProductInfo(String xmlData) {
        String name = extractValue(xmlData, "name");
        String price = extractValue(xmlData, "price");
        String category = extractValue(xmlData, "category");
        
        log.info("📦 解析产品信息 - 名称: {}, 价格: {}, 类别: {}", name, price, category);
    }
    
    /**
     * 从XML中提取指定标签的值
     */
    private String extractValue(String xml, String tag) {
        String startTag = "<" + tag + ">";
        String endTag = "</" + tag + ">";
        
        int startIndex = xml.indexOf(startTag);
        int endIndex = xml.indexOf(endTag);
        
        if (startIndex != -1 && endIndex != -1) {
            return xml.substring(startIndex + startTag.length(), endIndex);
        }
        
        return "未知";
    }
}
