package com.learning.designpatterns.structural.proxy;

import lombok.extern.slf4j.Slf4j;

/**
 * 真实数据库服务
 * 
 * 模拟实际的数据库查询操作
 * 
 * <AUTHOR>
 */
@Slf4j
public class RealDatabaseService implements DatabaseService {
    
    @Override
    public String getUserById(int userId) {
        log.info("🗄️ 正在从数据库查询用户ID: {}", userId);
        
        // 模拟数据库查询的耗时操作
        try {
            Thread.sleep(1000); // 模拟1秒的数据库查询时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("数据库查询被中断", e);
        }
        
        // 模拟查询结果
        String userData = String.format("用户%d{姓名:'用户%d', 邮箱:'<EMAIL>', 年龄:%d}", 
                userId, userId, userId, 20 + userId);
        
        log.info("✅ 数据库查询完成，返回结果");
        return userData;
    }
}
