package com.learning.designpatterns.structural.bridge;

import lombok.extern.slf4j.Slf4j;

/**
 * 圆形类
 * 
 * 具体的形状实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class Circle extends Shape {
    
    private double x, y, radius;
    
    public Circle(DrawingAPI drawingAPI, double radius, double x, double y) {
        super(drawingAPI);
        this.radius = radius;
        this.x = x;
        this.y = y;
    }
    
    @Override
    public void draw() {
        log.info("🔵 绘制圆形 - 使用 {}", drawingAPI.getAPIName());
        drawingAPI.drawCircle(x, y, radius);
    }
    
    @Override
    public void resize(double factor) {
        radius *= factor;
        log.info("🔄 圆形大小调整，新半径: {}", radius);
    }
    
    @Override
    public String getShapeInfo() {
        return String.format("圆形[中心: (%.1f, %.1f), 半径: %.1f]", x, y, radius);
    }
    
    /**
     * 移动圆形
     * 
     * @param newX 新的X坐标
     * @param newY 新的Y坐标
     */
    public void move(double newX, double newY) {
        this.x = newX;
        this.y = newY;
        log.info("📍 圆形移动到新位置: ({}, {})", x, y);
    }
    
    /**
     * 获取圆形面积
     * 
     * @return 面积
     */
    public double getArea() {
        return Math.PI * radius * radius;
    }
    
    /**
     * 获取圆形周长
     * 
     * @return 周长
     */
    public double getPerimeter() {
        return 2 * Math.PI * radius;
    }
}
