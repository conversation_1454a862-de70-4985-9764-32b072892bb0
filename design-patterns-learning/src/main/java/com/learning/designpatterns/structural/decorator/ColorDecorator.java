package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 颜色装饰器
 * 
 * 为文本添加颜色效果的装饰器
 * 
 * <AUTHOR>
 */
@Slf4j
public class ColorDecorator extends TextDecorator {
    
    private final String color;
    
    public ColorDecorator(Text text, String color) {
        super(text);
        this.color = color;
        log.info("🎨 添加颜色效果: {}", color);
    }
    
    @Override
    public String render() {
        return "<span style=\"color:" + color + "\">" + text.render() + "</span>";
    }
}
