package com.learning.designpatterns.structural.bridge;

import lombok.Setter;

/**
 * 形状抽象类（抽象部分）
 * 
 * 定义了形状的基本操作，并持有绘制API的引用
 * 
 * <AUTHOR>
 */
@Setter
public abstract class Shape {
    
    protected DrawingAPI drawingAPI;
    
    public Shape(DrawingAPI drawingAPI) {
        this.drawingAPI = drawingAPI;
    }
    
    /**
     * 绘制形状
     */
    public abstract void draw();
    
    /**
     * 调整大小
     * 
     * @param factor 缩放因子
     */
    public abstract void resize(double factor);
    
    /**
     * 获取形状信息
     * 
     * @return 形状信息
     */
    public abstract String getShapeInfo();
    
    /**
     * 设置绘制API
     * 
     * @param drawingAPI 绘制API
     */
    public void setDrawingAPI(DrawingAPI drawingAPI) {
        this.drawingAPI = drawingAPI;
    }
    
    /**
     * 获取当前使用的绘制API
     * 
     * @return 绘制API
     */
    public DrawingAPI getDrawingAPI() {
        return drawingAPI;
    }
}
