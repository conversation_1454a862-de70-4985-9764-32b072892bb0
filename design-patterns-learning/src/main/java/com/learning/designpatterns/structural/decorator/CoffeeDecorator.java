package com.learning.designpatterns.structural.decorator;

/**
 * 咖啡装饰器抽象类
 * 
 * 装饰器模式的抽象装饰器类，实现Coffee接口
 * 并持有一个Coffee对象的引用
 * 
 * <AUTHOR>
 */
public abstract class CoffeeDecorator implements Coffee {
    
    /**
     * 被装饰的咖啡对象
     */
    protected Coffee coffee;
    
    /**
     * 构造函数
     * 
     * @param coffee 被装饰的咖啡对象
     */
    public CoffeeDecorator(Coffee coffee) {
        this.coffee = coffee;
    }
    
    @Override
    public String getDescription() {
        return coffee.getDescription();
    }
    
    @Override
    public double getCost() {
        return coffee.getCost();
    }
}
