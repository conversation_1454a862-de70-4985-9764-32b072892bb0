package com.learning.designpatterns.structural.adapter;

import lombok.extern.slf4j.Slf4j;

/**
 * MP4播放器实现
 * 
 * 实现了AdvancedMediaPlayer接口，专门播放MP4格式文件
 * 
 * <AUTHOR>
 */
@Slf4j
public class Mp4Player implements AdvancedMediaPlayer {
    
    @Override
    public void playVlc(String fileName) {
        // MP4播放器不支持VLC格式
        log.warn("⚠️ MP4播放器不支持VLC格式: {}", fileName);
    }
    
    @Override
    public void playMp4(String fileName) {
        log.info("🎥 MP4播放器正在播放MP4文件: {}", fileName);
        // 模拟MP4播放逻辑
        simulatePlayback(fileName, "MP4");
    }
    
    /**
     * 模拟播放过程
     */
    private void simulatePlayback(String fileName, String format) {
        log.info("📀 开始播放 {} 格式文件: {}", format, fileName);
        log.info("🔊 高清画质，立体声音效");
        
        try {
            Thread.sleep(300); // 模拟播放延迟
            log.info("▶️ 播放中... 缓冲: 100%");
            Thread.sleep(700);
            log.info("✅ 播放完成: {}", fileName);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("播放被中断", e);
        }
    }
}
