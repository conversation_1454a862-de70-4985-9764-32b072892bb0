package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 巧克力装饰器
 * 
 * 为咖啡添加巧克力的装饰器
 * 
 * <AUTHOR>
 */
@Slf4j
public class ChocolateDecorator extends CoffeeDecorator {
    
    public ChocolateDecorator(Coffee coffee) {
        super(coffee);
        log.info("🍫 添加巧克力");
    }
    
    @Override
    public String getDescription() {
        return coffee.getDescription() + " + 巧克力";
    }
    
    @Override
    public double getCost() {
        return coffee.getCost() + 3.0; // 巧克力加3元
    }
}
