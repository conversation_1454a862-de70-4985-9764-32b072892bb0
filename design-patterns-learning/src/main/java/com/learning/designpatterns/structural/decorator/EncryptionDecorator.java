package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;
import java.util.Base64;

/**
 * 加密装饰器
 * 
 * 为数据流添加加密功能的装饰器
 * 这里使用简单的Caesar密码作为演示
 * 
 * <AUTHOR>
 */
@Slf4j
public class EncryptionDecorator extends DataStreamDecorator {
    
    private static final int SHIFT = 3; // Caesar密码的位移量
    
    public EncryptionDecorator(DataStream dataStream) {
        super(dataStream);
        log.info("🔐 添加加密功能");
    }
    
    @Override
    public String read() {
        String encryptedData = dataStream.read();
        return decrypt(encryptedData);
    }
    
    @Override
    public void write(String data) {
        String encryptedData = encrypt(data);
        dataStream.write(encryptedData);
    }
    
    /**
     * 加密数据
     * 使用简单的Caesar密码进行加密
     * 
     * @param data 原始数据
     * @return 加密后的数据
     */
    private String encrypt(String data) {
        StringBuilder encrypted = new StringBuilder();
        
        for (char c : data.toCharArray()) {
            if (Character.isLetter(c)) {
                char base = Character.isUpperCase(c) ? 'A' : 'a';
                c = (char) ((c - base + SHIFT) % 26 + base);
            }
            encrypted.append(c);
        }
        
        // 使用Base64编码以便安全传输
        String result = Base64.getEncoder().encodeToString(encrypted.toString().getBytes());
        
        log.info("🔐 数据加密完成: {}字节 -> {}字节", data.length(), result.length());
        
        return result;
    }
    
    /**
     * 解密数据
     * 
     * @param encryptedData 加密的数据
     * @return 解密后的数据
     */
    private String decrypt(String encryptedData) {
        try {
            // 先进行Base64解码
            String caesarEncrypted = new String(Base64.getDecoder().decode(encryptedData));
            
            StringBuilder decrypted = new StringBuilder();
            
            for (char c : caesarEncrypted.toCharArray()) {
                if (Character.isLetter(c)) {
                    char base = Character.isUpperCase(c) ? 'A' : 'a';
                    c = (char) ((c - base - SHIFT + 26) % 26 + base);
                }
                decrypted.append(c);
            }
            
            String result = decrypted.toString();
            log.info("🔓 数据解密完成: {}字节 -> {}字节", encryptedData.length(), result.length());
            
            return result;
        } catch (Exception e) {
            log.error("解密失败", e);
            return encryptedData; // 解密失败时返回原数据
        }
    }
}
