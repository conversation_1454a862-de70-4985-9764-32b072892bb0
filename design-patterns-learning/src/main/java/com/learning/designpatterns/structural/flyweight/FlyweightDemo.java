package com.learning.designpatterns.structural.flyweight;

import lombok.extern.slf4j.Slf4j;
import java.util.Random;

/**
 * 享元模式演示类
 * 
 * 享元模式运用共享技术有效地支持大量细粒度的对象。
 * 通过共享已经存在的对象来大幅度减少需要创建的对象数量、避免大量相似类的开销。
 * 
 * 应用场景：
 * - 文本编辑器中的字符对象
 * - 游戏中的粒子系统（子弹、特效）
 * - 网页中的图标缓存
 * - 数据库连接池
 * - 线程池
 * 
 * <AUTHOR>
 */
@Slf4j
public class FlyweightDemo {
    
    public static void main(String[] args) {
        log.info("=== 享元模式演示 ===");
        
        // 演示文本编辑器字符系统
        demonstrateTextEditor();
        
        // 演示游戏粒子系统
        demonstrateParticleSystem();
        
        // 演示图标缓存系统
        demonstrateIconCache();
    }
    
    private static void demonstrateTextEditor() {
        log.info("\n--- 文本编辑器享元模式演示 ---");
        
        TextEditor editor = new TextEditor();
        
        log.info("📝 创建文本编辑器");
        
        // 添加不同的字符
        log.info("\n✍️ 输入文本内容:");
        String text = "Hello World! 你好世界！123";
        
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            // 随机字体和大小来模拟不同的外部状态
            String font = (i % 3 == 0) ? "Arial" : (i % 3 == 1) ? "Times" : "Courier";
            int size = 12 + (i % 3) * 2; // 12, 14, 16
            String color = (i % 4 == 0) ? "Black" : (i % 4 == 1) ? "Red" : 
                          (i % 4 == 2) ? "Blue" : "Green";
            
            editor.addCharacter(c, font, size, color, i * 10, 50);
        }
        
        // 显示享元对象的使用情况
        log.info("\n📊 享元对象统计:");
        CharacterFlyweightFactory factory = CharacterFlyweightFactory.getInstance();
        log.info("创建的享元对象数量: {}", factory.getCreatedFlyweightsCount());
        log.info("文本中字符总数: {}", text.length());
        log.info("内存节省率: {:.1f}%", 
                (1.0 - (double)factory.getCreatedFlyweightsCount() / text.length()) * 100);
        
        // 渲染文本
        log.info("\n🎨 渲染文本:");
        editor.render();
    }
    
    private static void demonstrateParticleSystem() {
        log.info("\n--- 游戏粒子系统享元模式演示 ---");
        
        ParticleSystem particleSystem = new ParticleSystem();
        Random random = new Random();
        
        log.info("🎮 创建游戏粒子系统");
        
        // 创建大量粒子
        log.info("\n💥 生成爆炸效果 (1000个粒子):");
        for (int i = 0; i < 1000; i++) {
            ParticleType type = ParticleType.values()[random.nextInt(ParticleType.values().length)];
            double x = random.nextDouble() * 800;
            double y = random.nextDouble() * 600;
            double velocityX = (random.nextDouble() - 0.5) * 10;
            double velocityY = (random.nextDouble() - 0.5) * 10;
            
            particleSystem.addParticle(type, x, y, velocityX, velocityY);
        }
        
        // 显示享元对象的使用情况
        log.info("\n📊 粒子系统统计:");
        ParticleFlyweightFactory factory = ParticleFlyweightFactory.getInstance();
        log.info("创建的享元对象数量: {}", factory.getCreatedFlyweightsCount());
        log.info("粒子总数: {}", particleSystem.getParticleCount());
        log.info("内存节省率: {:.1f}%", 
                (1.0 - (double)factory.getCreatedFlyweightsCount() / particleSystem.getParticleCount()) * 100);
        
        // 更新粒子系统
        log.info("\n🔄 更新粒子系统 (3帧):");
        for (int frame = 1; frame <= 3; frame++) {
            log.info("--- 第 {} 帧 ---", frame);
            particleSystem.update(0.016); // 60 FPS
            if (frame == 1) {
                // 只在第一帧显示部分粒子渲染信息
                particleSystem.render(5); // 只渲染前5个粒子
            }
        }
    }
    
    private static void demonstrateIconCache() {
        log.info("\n--- 图标缓存系统享元模式演示 ---");
        
        IconManager iconManager = new IconManager();
        
        log.info("🖼️ 创建图标管理器");
        
        // 模拟网页中使用大量图标
        log.info("\n🌐 网页中使用图标:");
        String[] iconTypes = {"home", "user", "settings", "search", "heart", "star"};
        String[] sizes = {"16x16", "24x24", "32x32"};
        
        // 创建100个图标实例
        for (int i = 0; i < 100; i++) {
            String iconType = iconTypes[i % iconTypes.length];
            String size = sizes[i % sizes.length];
            int x = (i % 10) * 50;
            int y = (i / 10) * 30;
            
            iconManager.displayIcon(iconType, size, x, y);
        }
        
        // 显示享元对象的使用情况
        log.info("\n📊 图标缓存统计:");
        IconFlyweightFactory factory = IconFlyweightFactory.getInstance();
        log.info("创建的享元对象数量: {}", factory.getCreatedFlyweightsCount());
        log.info("显示的图标总数: 100");
        log.info("内存节省率: {:.1f}%", (1.0 - factory.getCreatedFlyweightsCount() / 100.0) * 100);
        
        log.info("\n💡 享元模式的优势:");
        log.info("   ✅ 大幅减少对象创建数量");
        log.info("   ✅ 降低内存占用");
        log.info("   ✅ 提高系统性能");
        log.info("   ✅ 支持大量细粒度对象");
    }
}
