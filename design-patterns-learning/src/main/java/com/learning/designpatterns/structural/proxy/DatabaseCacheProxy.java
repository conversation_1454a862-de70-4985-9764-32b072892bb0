package com.learning.designpatterns.structural.proxy;

import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据库缓存代理
 * 
 * 为数据库查询添加缓存功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class DatabaseCacheProxy implements DatabaseService {
    
    private final RealDatabaseService realDatabaseService;
    private final Map<Integer, String> cache;
    private int cacheHits = 0;
    private int cacheMisses = 0;
    
    public DatabaseCacheProxy() {
        this.realDatabaseService = new RealDatabaseService();
        this.cache = new HashMap<>();
        log.info("💾 创建数据库缓存代理");
    }
    
    @Override
    public String getUserById(int userId) {
        log.info("🔍 缓存代理收到查询请求，用户ID: {}", userId);
        
        // 首先检查缓存
        if (cache.containsKey(userId)) {
            cacheHits++;
            log.info("🎯 缓存命中！直接返回缓存数据");
            return cache.get(userId);
        }
        
        // 缓存未命中，查询真实数据库
        cacheMisses++;
        log.info("❌ 缓存未命中，查询真实数据库");
        
        String userData = realDatabaseService.getUserById(userId);
        
        // 将结果存入缓存
        cache.put(userId, userData);
        log.info("💾 查询结果已缓存");
        
        return userData;
    }
    
    /**
     * 显示缓存统计信息
     */
    public void showCacheStats() {
        int totalRequests = cacheHits + cacheMisses;
        double hitRate = totalRequests > 0 ? (double) cacheHits / totalRequests * 100 : 0;
        
        log.info("\n📊 缓存统计信息:");
        log.info("   总请求数: {}", totalRequests);
        log.info("   缓存命中: {}", cacheHits);
        log.info("   缓存未命中: {}", cacheMisses);
        log.info("   命中率: {:.1f}%", hitRate);
        log.info("   缓存大小: {}", cache.size());
    }
    
    /**
     * 清空缓存
     */
    public void clearCache() {
        cache.clear();
        cacheHits = 0;
        cacheMisses = 0;
        log.info("🗑️ 缓存已清空");
    }
}
