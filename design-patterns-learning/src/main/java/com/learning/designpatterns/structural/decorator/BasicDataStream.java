package com.learning.designpatterns.structural.decorator;

import lombok.extern.slf4j.Slf4j;

/**
 * 基础数据流实现
 * 
 * 基础的数据流实现，作为装饰器的基础组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class BasicDataStream implements DataStream {
    
    private String data;
    
    public BasicDataStream(String data) {
        this.data = data;
        log.info("📄 创建基础数据流");
    }
    
    @Override
    public String read() {
        return data;
    }
    
    @Override
    public void write(String data) {
        this.data = data;
        log.info("📝 写入基础数据流: {}", data);
    }
}
