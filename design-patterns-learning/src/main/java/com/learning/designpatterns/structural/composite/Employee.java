package com.learning.designpatterns.structural.composite;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 员工类（叶子节点）
 * 
 * 表示组织架构中的员工
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class Employee extends OrganizationComponent {
    
    private final String position;
    private final double salary;
    
    public Employee(String name, String position, double salary) {
        super(name);
        this.position = position;
        this.salary = salary;
    }
    
    @Override
    public int getEmployeeCount() {
        return 1; // 员工本身算一个员工
    }
    
    @Override
    public double getTotalSalary() {
        return salary;
    }
    
    @Override
    public void display(int depth) {
        log.info("{}👤 {} - {} (薪资: ¥{})", 
                getIndent(depth), name, position, formatSalary(salary));
    }
    
    @Override
    public OrganizationComponent findEmployee(String name) {
        return this.name.equals(name) ? this : null;
    }
    
    /**
     * 格式化薪资显示
     */
    private String formatSalary(double salary) {
        if (salary >= 10000) {
            return String.format("%.1fW", salary / 10000);
        } else {
            return String.format("%.0f", salary);
        }
    }
    
    /**
     * 获取员工信息
     */
    public String getEmployeeInfo() {
        return String.format("员工: %s, 职位: %s, 薪资: ¥%.0f", name, position, salary);
    }
    
    /**
     * 判断是否为管理层
     */
    public boolean isManager() {
        return position.contains("经理") || position.contains("总监") || 
               position.contains("总") || position.contains("主管");
    }
    
    /**
     * 计算年薪
     */
    public double getAnnualSalary() {
        return salary * 12;
    }
}
