package com.learning.designpatterns.structural.flyweight;

/**
 * 字符享元接口
 * 
 * 定义了享元对象的接口，通过这个接口享元可以接受并作用于外部状态
 * 
 * <AUTHOR>
 */
public interface CharacterFlyweight {
    
    /**
     * 渲染字符
     * 
     * @param font 字体 (外部状态)
     * @param size 大小 (外部状态)
     * @param color 颜色 (外部状态)
     * @param x X坐标 (外部状态)
     * @param y Y坐标 (外部状态)
     */
    void render(String font, int size, String color, int x, int y);
    
    /**
     * 获取字符
     * 
     * @return 字符 (内部状态)
     */
    char getCharacter();
}
