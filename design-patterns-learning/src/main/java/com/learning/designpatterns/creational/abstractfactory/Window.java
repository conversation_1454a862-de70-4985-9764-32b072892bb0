package com.learning.designpatterns.creational.abstractfactory;

/**
 * 窗口抽象接口
 * 
 * <AUTHOR>
 */
public interface Window {
    
    /**
     * 渲染窗口
     */
    void render();
    
    /**
     * 设置标题
     * 
     * @param title 窗口标题
     */
    void setTitle(String title);
    
    /**
     * 获取标题
     * 
     * @return 窗口标题
     */
    String getTitle();
    
    /**
     * 显示窗口
     */
    void show();
    
    /**
     * 隐藏窗口
     */
    void hide();
    
    /**
     * 获取窗口样式
     * 
     * @return 样式描述
     */
    String getStyle();
}
