package com.learning.designpatterns.creational.factory;

import lombok.extern.slf4j.Slf4j;

/**
 * 微信支付实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class WechatPayment implements Payment {
    
    @Override
    public boolean pay(double amount) {
        log.info("正在使用微信支付 {} 元", amount);
        
        // 模拟微信支付流程
        try {
            // 模拟网络请求延迟
            Thread.sleep(800);
            
            // 模拟支付成功（85%成功率）
            boolean success = Math.random() > 0.15;
            
            if (success) {
                log.info("微信支付成功！交易金额: {} 元", amount);
            } else {
                log.warn("微信支付失败！可能是余额不足或网络问题");
            }
            
            return success;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("微信支付过程中断", e);
            return false;
        }
    }
    
    @Override
    public String getPaymentMethod() {
        return "微信支付";
    }
    
    @Override
    public double getFeeRate() {
        return 0.6; // 微信支付手续费率 0.6%
    }
    
    @Override
    public String toString() {
        return "WechatPayment{method=" + getPaymentMethod() + ", feeRate=" + getFeeRate() + "%}";
    }
}
