package com.learning.designpatterns.creational.factory;

import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件日志记录器
 * 
 * 将日志消息写入文件
 * 
 * <AUTHOR>
 */
@Slf4j
public class FileLogger implements Logger {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public void log(String message) {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        String logEntry = String.format("[%s] [FILE] %s", timestamp, message);
        
        // 模拟写入文件
        log.info("写入文件日志: {}", logEntry);
        
        // 实际项目中，这里会写入到具体的日志文件
        // try (FileWriter writer = new FileWriter("app.log", true)) {
        //     writer.write(logEntry + System.lineSeparator());
        // } catch (IOException e) {
        //     log.error("写入文件日志失败", e);
        // }
    }
    
    @Override
    public String getType() {
        return "FILE";
    }
    
    @Override
    public String toString() {
        return "FileLogger{type=" + getType() + "}";
    }
}
