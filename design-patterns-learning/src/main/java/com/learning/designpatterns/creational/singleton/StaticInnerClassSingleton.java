package com.learning.designpatterns.creational.singleton;

import lombok.extern.slf4j.Slf4j;

/**
 * 静态内部类单例模式
 * 
 * 特点：
 * - 懒加载（只有在调用getInstance()时才加载内部类）
 * - 线程安全（利用类加载机制保证线程安全）
 * - 性能好（没有同步开销）
 * 
 * 优点：
 * - 懒加载，节省内存
 * - 线程安全
 * - 性能好，没有同步开销
 * - 实现相对简单
 * 
 * 缺点：
 * - 无法传递参数给构造函数
 * 
 * 推荐指数：⭐⭐⭐⭐⭐
 * 这是实现单例模式的推荐方式之一，结合了懒加载和高性能的优点
 * 
 * <AUTHOR>
 */
@Slf4j
public class StaticInnerClassSingleton {
    
    /**
     * 私有构造函数，防止外部实例化
     */
    private StaticInnerClassSingleton() {
        log.info("StaticInnerClassSingleton 实例被创建");
        // 防止反射攻击
        if (SingletonHolder.INSTANCE != null) {
            throw new RuntimeException("不能通过反射创建单例实例");
        }
    }
    
    /**
     * 静态内部类
     * 
     * 特点：
     * - 只有在第一次调用getInstance()时才会加载
     * - 类加载机制保证线程安全
     * - 不会在外部类加载时就创建实例
     */
    private static class SingletonHolder {
        /**
         * 单例实例
         * 在内部类加载时创建，保证线程安全和懒加载
         */
        private static final StaticInnerClassSingleton INSTANCE = new StaticInnerClassSingleton();
    }
    
    /**
     * 获取单例实例
     * 
     * 当第一次调用此方法时，JVM会加载SingletonHolder类
     * 类加载过程是线程安全的，保证只创建一个实例
     * 
     * @return 单例实例
     */
    public static StaticInnerClassSingleton getInstance() {
        return SingletonHolder.INSTANCE;
    }
    
    /**
     * 业务方法示例
     */
    public void doSomething() {
        log.info("StaticInnerClassSingleton 正在执行业务逻辑...");
    }
    
    /**
     * 防止序列化破坏单例
     * 
     * @return 单例实例
     */
    private Object readResolve() {
        return SingletonHolder.INSTANCE;
    }
    
    @Override
    public String toString() {
        return "StaticInnerClassSingleton{hashCode=" + this.hashCode() + "}";
    }
}
