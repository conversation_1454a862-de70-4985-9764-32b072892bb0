package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Mac风格窗口
 * 
 * <AUTHOR>
 */
@Slf4j
public class MacWindow implements Window {
    
    private String title = "Mac窗口";
    
    @Override
    public void render() {
        log.info("🍎 渲染Mac风格窗口:");
        log.info("   ╭─ 🔴🟡🟢 {} ─╮", title);
        log.info("   │                    │");
        log.info("   │    Mac窗口内容     │");
        log.info("   │                    │");
        log.info("   ╰────────────────────╯");
    }
    
    @Override
    public void setTitle(String title) {
        this.title = title;
        log.info("🏷️ Mac窗口标题设置为: {}", title);
    }
    
    @Override
    public String getTitle() {
        return title;
    }
    
    @Override
    public void show() {
        log.info("👁️ 显示Mac窗口 - 优雅的缩放动画");
    }
    
    @Override
    public void hide() {
        log.info("🙈 隐藏Mac窗口 - 平滑的缩放动画");
    }
    
    @Override
    public String getStyle() {
        return "Mac风格 - 圆角窗口，红黄绿交通灯按钮";
    }
}
