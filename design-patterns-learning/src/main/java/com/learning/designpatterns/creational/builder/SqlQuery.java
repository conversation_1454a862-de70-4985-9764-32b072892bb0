package com.learning.designpatterns.creational.builder;

import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * SQL查询类 - 建造者模式示例
 * 
 * 演示如何使用建造者模式构建复杂的SQL查询
 * 
 * <AUTHOR>
 */
@Slf4j
public class SqlQuery {
    
    private final List<String> selectFields;
    private final String fromTable;
    private final List<String> joins;
    private final List<String> whereConditions;
    private final List<String> groupByFields;
    private final List<String> havingConditions;
    private final List<String> orderByFields;
    private final Integer limitCount;
    private final Integer offsetCount;
    
    /**
     * 私有构造函数，只能通过Builder创建
     */
    private SqlQuery(Builder builder) {
        this.selectFields = new ArrayList<>(builder.selectFields);
        this.fromTable = builder.fromTable;
        this.joins = new ArrayList<>(builder.joins);
        this.whereConditions = new ArrayList<>(builder.whereConditions);
        this.groupByFields = new ArrayList<>(builder.groupByFields);
        this.havingConditions = new ArrayList<>(builder.havingConditions);
        this.orderByFields = new ArrayList<>(builder.orderByFields);
        this.limitCount = builder.limitCount;
        this.offsetCount = builder.offsetCount;
        
        log.info("📝 SQL查询构建完成");
    }
    
    /**
     * 建造者类
     */
    public static class Builder {
        private List<String> selectFields = new ArrayList<>();
        private String fromTable;
        private List<String> joins = new ArrayList<>();
        private List<String> whereConditions = new ArrayList<>();
        private List<String> groupByFields = new ArrayList<>();
        private List<String> havingConditions = new ArrayList<>();
        private List<String> orderByFields = new ArrayList<>();
        private Integer limitCount;
        private Integer offsetCount;
        
        public Builder select(String... fields) {
            for (String field : fields) {
                this.selectFields.add(field);
            }
            return this;
        }
        
        public Builder from(String table) {
            this.fromTable = table;
            return this;
        }
        
        public Builder join(String table, String condition) {
            this.joins.add("JOIN " + table + " ON " + condition);
            return this;
        }
        
        public Builder leftJoin(String table, String condition) {
            this.joins.add("LEFT JOIN " + table + " ON " + condition);
            return this;
        }
        
        public Builder rightJoin(String table, String condition) {
            this.joins.add("RIGHT JOIN " + table + " ON " + condition);
            return this;
        }
        
        public Builder innerJoin(String table, String condition) {
            this.joins.add("INNER JOIN " + table + " ON " + condition);
            return this;
        }
        
        public Builder where(String condition) {
            this.whereConditions.add(condition);
            return this;
        }
        
        public Builder groupBy(String... fields) {
            for (String field : fields) {
                this.groupByFields.add(field);
            }
            return this;
        }
        
        public Builder having(String condition) {
            this.havingConditions.add(condition);
            return this;
        }
        
        public Builder orderBy(String field) {
            this.orderByFields.add(field);
            return this;
        }
        
        public Builder limit(int count) {
            this.limitCount = count;
            return this;
        }
        
        public Builder offset(int count) {
            this.offsetCount = count;
            return this;
        }
        
        /**
         * 构建SqlQuery对象
         * 
         * @return SqlQuery实例
         */
        public SqlQuery build() {
            // 验证必需参数
            if (selectFields.isEmpty()) {
                throw new IllegalStateException("SELECT字段是必需的");
            }
            if (fromTable == null || fromTable.trim().isEmpty()) {
                throw new IllegalStateException("FROM表是必需的");
            }
            
            log.info("🔨 开始构建SQL查询...");
            return new SqlQuery(this);
        }
    }
    
    /**
     * 生成SQL字符串
     * 
     * @return SQL查询字符串
     */
    public String toSql() {
        StringBuilder sql = new StringBuilder();
        
        // SELECT子句
        sql.append("SELECT ");
        StringJoiner selectJoiner = new StringJoiner(", ");
        for (String field : selectFields) {
            selectJoiner.add(field);
        }
        sql.append(selectJoiner.toString());
        
        // FROM子句
        sql.append("\nFROM ").append(fromTable);
        
        // JOIN子句
        for (String join : joins) {
            sql.append("\n").append(join);
        }
        
        // WHERE子句
        if (!whereConditions.isEmpty()) {
            sql.append("\nWHERE ");
            StringJoiner whereJoiner = new StringJoiner(" AND ");
            for (String condition : whereConditions) {
                whereJoiner.add("(" + condition + ")");
            }
            sql.append(whereJoiner.toString());
        }
        
        // GROUP BY子句
        if (!groupByFields.isEmpty()) {
            sql.append("\nGROUP BY ");
            StringJoiner groupJoiner = new StringJoiner(", ");
            for (String field : groupByFields) {
                groupJoiner.add(field);
            }
            sql.append(groupJoiner.toString());
        }
        
        // HAVING子句
        if (!havingConditions.isEmpty()) {
            sql.append("\nHAVING ");
            StringJoiner havingJoiner = new StringJoiner(" AND ");
            for (String condition : havingConditions) {
                havingJoiner.add("(" + condition + ")");
            }
            sql.append(havingJoiner.toString());
        }
        
        // ORDER BY子句
        if (!orderByFields.isEmpty()) {
            sql.append("\nORDER BY ");
            StringJoiner orderJoiner = new StringJoiner(", ");
            for (String field : orderByFields) {
                orderJoiner.add(field);
            }
            sql.append(orderJoiner.toString());
        }
        
        // LIMIT子句
        if (limitCount != null) {
            sql.append("\nLIMIT ").append(limitCount);
        }
        
        // OFFSET子句
        if (offsetCount != null) {
            sql.append("\nOFFSET ").append(offsetCount);
        }
        
        return sql.toString();
    }
    
    // Getters
    public List<String> getSelectFields() { return new ArrayList<>(selectFields); }
    public String getFromTable() { return fromTable; }
    public List<String> getJoins() { return new ArrayList<>(joins); }
    public List<String> getWhereConditions() { return new ArrayList<>(whereConditions); }
    public List<String> getGroupByFields() { return new ArrayList<>(groupByFields); }
    public List<String> getHavingConditions() { return new ArrayList<>(havingConditions); }
    public List<String> getOrderByFields() { return new ArrayList<>(orderByFields); }
    public Integer getLimitCount() { return limitCount; }
    public Integer getOffsetCount() { return offsetCount; }
    
    @Override
    public String toString() {
        return toSql();
    }
}
