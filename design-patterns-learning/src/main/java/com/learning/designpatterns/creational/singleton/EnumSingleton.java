package com.learning.designpatterns.creational.singleton;

import lombok.extern.slf4j.Slf4j;

/**
 * 枚举单例模式
 * 
 * 特点：
 * - 天然线程安全
 * - 防止反射攻击
 * - 防止序列化破坏
 * - 实现简单
 * 
 * 优点：
 * - 实现最简单
 * - 线程安全
 * - 防止反射和序列化攻击
 * - JVM保证只有一个实例
 * 
 * 缺点：
 * - 不是懒加载
 * - 枚举类型不能继承其他类（但可以实现接口）
 * 
 * 推荐指数：⭐⭐⭐⭐⭐
 * 这是实现单例模式的最佳方式，被《Effective Java》作者Joshua Bloch推荐
 * 
 * <AUTHOR>
 */
@Slf4j
public enum EnumSingleton {
    
    /**
     * 单例实例
     * 枚举类型的实例在类加载时创建，JVM保证线程安全
     */
    INSTANCE;
    
    /**
     * 构造函数
     * 枚举的构造函数默认是private的
     */
    EnumSingleton() {
        System.out.println("EnumSingleton 实例被创建");
    }
    
    /**
     * 业务方法示例
     */
    public void doSomething() {
        log.info("EnumSingleton 正在执行业务逻辑...");
    }
    
    /**
     * 可以添加其他业务方法
     */
    public void performTask(String task) {
        log.info("EnumSingleton 正在执行任务: {}", task);
    }
    
    /**
     * 获取配置信息示例
     */
    public String getConfig(String key) {
        // 这里可以实现配置管理逻辑
        log.info("获取配置项: {}", key);
        return "config_value_for_" + key;
    }
    
    @Override
    public String toString() {
        return "EnumSingleton{hashCode=" + this.hashCode() + "}";
    }
}
