package com.learning.designpatterns.creational.singleton;

import lombok.extern.slf4j.Slf4j;

/**
 * 单例模式演示类
 * 
 * 单例模式确保一个类只有一个实例，并提供全局访问点。
 * 
 * 应用场景：
 * - 数据库连接池
 * - 日志记录器
 * - 配置管理器
 * - 线程池
 * 
 * <AUTHOR>
 */
@Slf4j
public class SingletonDemo {
    
    public static void main(String[] args) {
        log.info("=== 单例模式演示 ===");
        
        // 演示饿汉式单例
        demonstrateEagerSingleton();
        
        // 演示懒汉式单例
        demonstrateLazySingleton();
        
        // 演示双重检查锁定单例
        demonstrateDoubleCheckedSingleton();
        
        // 演示枚举单例
        demonstrateEnumSingleton();
        
        // 演示静态内部类单例
        demonstrateStaticInnerClassSingleton();
    }
    
    private static void demonstrateEagerSingleton() {
        log.info("\n--- 饿汉式单例 ---");
        EagerSingleton instance1 = EagerSingleton.getInstance();
        EagerSingleton instance2 = EagerSingleton.getInstance();
        
        log.info("实例1: {}", instance1);
        log.info("实例2: {}", instance2);
        log.info("两个实例是否相同: {}", instance1 == instance2);
        
        instance1.doSomething();
    }
    
    private static void demonstrateLazySingleton() {
        log.info("\n--- 懒汉式单例 ---");
        LazySingleton instance1 = LazySingleton.getInstance();
        LazySingleton instance2 = LazySingleton.getInstance();
        
        log.info("实例1: {}", instance1);
        log.info("实例2: {}", instance2);
        log.info("两个实例是否相同: {}", instance1 == instance2);
        
        instance1.doSomething();
    }
    
    private static void demonstrateDoubleCheckedSingleton() {
        log.info("\n--- 双重检查锁定单例 ---");
        DoubleCheckedSingleton instance1 = DoubleCheckedSingleton.getInstance();
        DoubleCheckedSingleton instance2 = DoubleCheckedSingleton.getInstance();
        
        log.info("实例1: {}", instance1);
        log.info("实例2: {}", instance2);
        log.info("两个实例是否相同: {}", instance1 == instance2);
        
        instance1.doSomething();
    }
    
    private static void demonstrateEnumSingleton() {
        log.info("\n--- 枚举单例 ---");
        EnumSingleton instance1 = EnumSingleton.INSTANCE;
        EnumSingleton instance2 = EnumSingleton.INSTANCE;
        
        log.info("实例1: {}", instance1);
        log.info("实例2: {}", instance2);
        log.info("两个实例是否相同: {}", instance1 == instance2);
        
        instance1.doSomething();
    }
    
    private static void demonstrateStaticInnerClassSingleton() {
        log.info("\n--- 静态内部类单例 ---");
        StaticInnerClassSingleton instance1 = StaticInnerClassSingleton.getInstance();
        StaticInnerClassSingleton instance2 = StaticInnerClassSingleton.getInstance();
        
        log.info("实例1: {}", instance1);
        log.info("实例2: {}", instance2);
        log.info("两个实例是否相同: {}", instance1 == instance2);
        
        instance1.doSomething();
    }
}
