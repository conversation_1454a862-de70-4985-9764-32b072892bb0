package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Windows风格按钮
 * 
 * <AUTHOR>
 */
@Slf4j
public class WindowsButton implements Button {
    
    @Override
    public void render() {
        log.info("🔘 渲染Windows风格按钮: [  确定  ] (3D边框效果)");
    }
    
    @Override
    public void click() {
        log.info("🖱️ Windows按钮被点击 - 播放系统点击音效");
    }
    
    @Override
    public String getStyle() {
        return "Windows风格 - 3D立体边框，渐变背景";
    }
}
