package com.learning.designpatterns.creational.factory;

/**
 * 支付工厂抽象类
 * 
 * 定义了创建支付方式的工厂方法
 * 具体的创建逻辑由子类实现
 * 
 * <AUTHOR>
 */
public abstract class PaymentFactory {
    
    /**
     * 工厂方法：创建支付方式
     * 
     * 这是一个抽象方法，具体的实现由子类提供
     * 
     * @return 支付方式实例
     */
    public abstract Payment createPayment();
    
    /**
     * 模板方法：执行支付的完整流程
     * 
     * 这个方法定义了支付的标准流程：
     * 1. 创建支付方式
     * 2. 验证支付参数
     * 3. 执行支付
     * 4. 记录支付日志
     * 
     * @param amount 支付金额
     * @return 支付是否成功
     */
    public boolean processPayment(double amount) {
        if (amount <= 0) {
            System.out.println("支付金额必须大于0");
            return false;
        }
        
        Payment payment = createPayment();
        System.out.println("使用 " + payment.getPaymentMethod() + " 进行支付");
        
        boolean success = payment.pay(amount);
        
        if (success) {
            double fee = amount * payment.getFeeRate() / 100;
            System.out.println("支付成功！手续费: " + fee + " 元");
        } else {
            System.out.println("支付失败！");
        }
        
        return success;
    }
}
