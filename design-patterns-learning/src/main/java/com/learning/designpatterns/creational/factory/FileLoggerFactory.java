package com.learning.designpatterns.creational.factory;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件日志记录器工厂
 * 
 * 负责创建文件日志记录器实例
 * 
 * <AUTHOR>
 */
@Slf4j
public class FileLoggerFactory extends LoggerFactory {
    
    @Override
    public Logger createLogger() {
        log.info("创建文件日志记录器");
        return new FileLogger();
    }
    
    @Override
    protected void cleanup(Logger logger) {
        // 文件日志记录器的清理逻辑
        log.info("清理文件日志记录器资源: {}", logger.getType());
        // 实际项目中可能需要关闭文件流等
    }
}
