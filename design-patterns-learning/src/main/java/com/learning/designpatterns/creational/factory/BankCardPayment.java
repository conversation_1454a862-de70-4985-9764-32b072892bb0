package com.learning.designpatterns.creational.factory;

import lombok.extern.slf4j.Slf4j;

/**
 * 银行卡支付实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class BankCardPayment implements Payment {
    
    @Override
    public boolean pay(double amount) {
        log.info("正在使用银行卡支付 {} 元", amount);
        
        // 模拟银行卡支付流程
        try {
            // 模拟银行系统处理延迟
            Thread.sleep(1500);
            
            // 模拟支付成功（95%成功率，银行卡相对更稳定）
            boolean success = Math.random() > 0.05;
            
            if (success) {
                log.info("银行卡支付成功！交易金额: {} 元", amount);
            } else {
                log.warn("银行卡支付失败！可能是余额不足、卡片过期或银行系统维护");
            }
            
            return success;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("银行卡支付过程中断", e);
            return false;
        }
    }
    
    @Override
    public String getPaymentMethod() {
        return "银行卡";
    }
    
    @Override
    public double getFeeRate() {
        return 1.0; // 银行卡手续费率 1.0%
    }
    
    @Override
    public String toString() {
        return "BankCardPayment{method=" + getPaymentMethod() + ", feeRate=" + getFeeRate() + "%}";
    }
}
