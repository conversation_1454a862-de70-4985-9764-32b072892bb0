package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Windows风格文本框
 * 
 * <AUTHOR>
 */
@Slf4j
public class WindowsTextField implements TextField {
    
    private String text = "";
    
    @Override
    public void render() {
        log.info("📝 渲染Windows风格文本框: [{}] (内凹边框)", text.isEmpty() ? "请输入文本..." : text);
    }
    
    @Override
    public void setText(String text) {
        this.text = text;
        log.info("✏️ Windows文本框内容设置为: {}", text);
    }
    
    @Override
    public String getText() {
        return text;
    }
    
    @Override
    public String getStyle() {
        return "Windows风格 - 内凹边框，白色背景";
    }
}
