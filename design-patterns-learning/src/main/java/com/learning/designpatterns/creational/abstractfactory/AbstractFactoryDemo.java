package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * 抽象工厂模式演示类
 * 
 * 抽象工厂模式提供一个创建一系列相关或相互依赖对象的接口，
 * 而无需指定它们具体的类。
 * 
 * 应用场景：
 * - 跨平台UI组件库（Windows、Mac、Linux风格）
 * - 数据库驱动程序（MySQL、Oracle、PostgreSQL）
 * - 游戏中的不同主题（现代、古典、科幻风格）
 * - 文档格式处理（PDF、Word、Excel）
 * - 操作系统相关组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class AbstractFactoryDemo {
    
    public static void main(String[] args) {
        log.info("=== 抽象工厂模式演示 ===");
        
        // 演示UI组件工厂
        demonstrateUIComponentFactory();

        // 演示其他工厂的概念
        demonstrateOtherFactoryConcepts();
    }
    
    private static void demonstrateUIComponentFactory() {
        log.info("\n--- UI组件工厂演示 ---");
        
        // Windows风格UI
        log.info("🪟 创建Windows风格UI:");
        UIComponentFactory windowsFactory = new WindowsUIFactory();
        createAndShowUI(windowsFactory, "Windows应用程序");
        
        // Mac风格UI
        log.info("\n🍎 创建Mac风格UI:");
        UIComponentFactory macFactory = new MacUIFactory();
        createAndShowUI(macFactory, "Mac应用程序");
        
        // Linux风格UI
        log.info("\n🐧 创建Linux风格UI:");
        UIComponentFactory linuxFactory = new LinuxUIFactory();
        createAndShowUI(linuxFactory, "Linux应用程序");
    }
    
    private static void createAndShowUI(UIComponentFactory factory, String appName) {
        // 创建UI组件
        Button button = factory.createButton();
        TextField textField = factory.createTextField();
        Window window = factory.createWindow();
        
        // 设置应用程序
        Application app = new Application(button, textField, window);
        app.setTitle(appName);
        app.render();
    }
    
    private static void demonstrateOtherFactoryConcepts() {
        log.info("\n--- 其他抽象工厂应用场景 ---");

        log.info("💾 数据库工厂:");
        log.info("   • MySQL工厂 - 创建MySQL连接、查询、事务对象");
        log.info("   • PostgreSQL工厂 - 创建PostgreSQL连接、查询、事务对象");
        log.info("   • Oracle工厂 - 创建Oracle连接、查询、事务对象");

        log.info("\n🎮 游戏主题工厂:");
        log.info("   • 现代主题工厂 - 创建现代角色、武器、环境");
        log.info("   • 古典主题工厂 - 创建古代角色、武器、环境");
        log.info("   • 科幻主题工厂 - 创建未来角色、武器、环境");

        log.info("\n📄 文档处理工厂:");
        log.info("   • PDF工厂 - 创建PDF阅读器、编辑器、转换器");
        log.info("   • Word工厂 - 创建Word阅读器、编辑器、转换器");
        log.info("   • Excel工厂 - 创建Excel阅读器、编辑器、转换器");

        log.info("\n💡 抽象工厂模式的优势:");
        log.info("   ✅ 确保产品族的一致性");
        log.info("   ✅ 易于切换产品族");
        log.info("   ✅ 符合开闭原则");
        log.info("   ✅ 分离了具体类的创建和使用");
    }
}
