package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Linux风格文本框
 * 
 * <AUTHOR>
 */
@Slf4j
public class LinuxTextField implements TextField {
    
    private String text = "";
    
    @Override
    public void render() {
        log.info("📝 渲染Linux风格文本框: |{}| (简洁边框)", text.isEmpty() ? "输入文本..." : text);
    }
    
    @Override
    public void setText(String text) {
        this.text = text;
        log.info("✏️ Linux文本框内容设置为: {}", text);
    }
    
    @Override
    public String getText() {
        return text;
    }
    
    @Override
    public String getStyle() {
        return "Linux风格 - 简洁边框，开源设计";
    }
}
