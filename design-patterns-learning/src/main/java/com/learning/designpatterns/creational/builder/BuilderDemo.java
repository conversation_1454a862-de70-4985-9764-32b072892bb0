package com.learning.designpatterns.creational.builder;

import lombok.extern.slf4j.Slf4j;

/**
 * 建造者模式演示类
 * 
 * 建造者模式将一个复杂对象的构建与它的表示分离，使得同样的构建过程可以创建不同的表示。
 * 
 * 应用场景：
 * - 复杂对象的创建（如汽车、房屋、电脑配置）
 * - SQL查询构建器
 * - HTTP请求构建器
 * - 配置文件构建
 * - 游戏角色创建
 * 
 * <AUTHOR>
 */
@Slf4j
public class BuilderDemo {
    
    public static void main(String[] args) {
        log.info("=== 建造者模式演示 ===");
        
        // 演示电脑配置建造者
        demonstrateComputerBuilder();
        
        // 演示房屋建造者
        demonstrateHouseBuilder();
        
        // 演示SQL查询建造者
        demonstrateSqlQueryBuilder();
    }
    
    private static void demonstrateComputerBuilder() {
        log.info("\n--- 电脑配置建造者演示 ---");
        
        // 创建游戏电脑
        Computer gamingComputer = new Computer.Builder()
                .cpu("Intel i9-13900K")
                .gpu("NVIDIA RTX 4090")
                .ram(32)
                .storage("1TB NVMe SSD")
                .motherboard("ASUS ROG STRIX Z790-E")
                .powerSupply("850W 80+ Gold")
                .coolingSystem("液冷散热器")
                .caseType("全塔机箱")
                .build();
        
        log.info("🎮 游戏电脑配置完成:");
        log.info(gamingComputer.toString());
        
        // 创建办公电脑
        Computer officeComputer = new Computer.Builder()
                .cpu("Intel i5-13400")
                .ram(16)
                .storage("512GB SSD")
                .motherboard("华硕 B760M")
                .powerSupply("500W")
                .build();
        
        log.info("\n💼 办公电脑配置完成:");
        log.info(officeComputer.toString());
        
        // 创建服务器
        Computer server = new Computer.Builder()
                .cpu("Intel Xeon E5-2690")
                .ram(128)
                .storage("2TB SSD RAID")
                .motherboard("超微服务器主板")
                .powerSupply("1200W 冗余电源")
                .networkCard("万兆网卡")
                .build();
        
        log.info("\n🖥️ 服务器配置完成:");
        log.info(server.toString());
    }
    
    private static void demonstrateHouseBuilder() {
        log.info("\n--- 房屋建造者演示 ---");
        
        // 创建别墅
        House villa = new House.Builder()
                .foundation("钢筋混凝土基础")
                .structure("框架结构")
                .roof("坡屋顶")
                .walls("砖混墙体")
                .windows("断桥铝合金窗")
                .doors("实木门")
                .floors("实木地板")
                .rooms(8)
                .bathrooms(4)
                .garage(true)
                .garden(true)
                .swimmingPool(true)
                .build();
        
        log.info("🏰 别墅建造完成:");
        log.info(villa.toString());
        
        // 创建公寓
        House apartment = new House.Builder()
                .foundation("桩基础")
                .structure("剪力墙结构")
                .walls("混凝土墙体")
                .windows("塑钢窗")
                .doors("防盗门")
                .floors("瓷砖地面")
                .rooms(3)
                .bathrooms(2)
                .build();
        
        log.info("\n🏢 公寓建造完成:");
        log.info(apartment.toString());
    }
    
    private static void demonstrateSqlQueryBuilder() {
        log.info("\n--- SQL查询建造者演示 ---");
        
        // 构建复杂查询
        SqlQuery complexQuery = new SqlQuery.Builder()
                .select("u.name", "u.email", "p.title", "c.name as category")
                .from("users u")
                .join("posts p", "u.id = p.user_id")
                .join("categories c", "p.category_id = c.id")
                .where("u.status = 'active'")
                .where("p.published_at > '2023-01-01'")
                .groupBy("u.id", "c.id")
                .having("COUNT(p.id) > 5")
                .orderBy("u.name ASC")
                .limit(50)
                .build();
        
        log.info("🔍 复杂SQL查询构建完成:");
        log.info(complexQuery.toSql());
        
        // 构建简单查询
        SqlQuery simpleQuery = new SqlQuery.Builder()
                .select("*")
                .from("products")
                .where("price > 100")
                .orderBy("price DESC")
                .limit(10)
                .build();
        
        log.info("\n📝 简单SQL查询构建完成:");
        log.info(simpleQuery.toSql());
    }
}
