package com.learning.designpatterns.creational.builder;

import lombok.extern.slf4j.Slf4j;

/**
 * 电脑类 - 建造者模式示例
 * 
 * 演示如何使用建造者模式构建复杂的电脑配置对象
 * 
 * <AUTHOR>
 */
@Slf4j
public class Computer {
    
    // 必需参数
    private final String cpu;
    private final int ram; // GB
    private final String storage;
    
    // 可选参数
    private final String gpu;
    private final String motherboard;
    private final String powerSupply;
    private final String coolingSystem;
    private final String caseType;
    private final String networkCard;
    private final boolean hasWifi;
    private final boolean hasBluetooth;
    
    /**
     * 私有构造函数，只能通过Builder创建
     */
    private Computer(Builder builder) {
        this.cpu = builder.cpu;
        this.ram = builder.ram;
        this.storage = builder.storage;
        this.gpu = builder.gpu;
        this.motherboard = builder.motherboard;
        this.powerSupply = builder.powerSupply;
        this.coolingSystem = builder.coolingSystem;
        this.caseType = builder.caseType;
        this.networkCard = builder.networkCard;
        this.hasWifi = builder.hasWifi;
        this.hasBluetooth = builder.hasBluetooth;
        
        log.info("🔧 电脑组装完成: CPU={}, RAM={}GB", cpu, ram);
    }
    
    /**
     * 建造者类
     */
    public static class Builder {
        // 必需参数
        private String cpu;
        private int ram;
        private String storage;
        
        // 可选参数 - 初始化为默认值
        private String gpu = "集成显卡";
        private String motherboard = "标准主板";
        private String powerSupply = "400W";
        private String coolingSystem = "风冷散热器";
        private String caseType = "中塔机箱";
        private String networkCard = "千兆网卡";
        private boolean hasWifi = false;
        private boolean hasBluetooth = false;
        
        public Builder cpu(String cpu) {
            this.cpu = cpu;
            return this;
        }
        
        public Builder ram(int ram) {
            if (ram <= 0) {
                throw new IllegalArgumentException("内存容量必须大于0");
            }
            this.ram = ram;
            return this;
        }
        
        public Builder storage(String storage) {
            this.storage = storage;
            return this;
        }
        
        public Builder gpu(String gpu) {
            this.gpu = gpu;
            return this;
        }
        
        public Builder motherboard(String motherboard) {
            this.motherboard = motherboard;
            return this;
        }
        
        public Builder powerSupply(String powerSupply) {
            this.powerSupply = powerSupply;
            return this;
        }
        
        public Builder coolingSystem(String coolingSystem) {
            this.coolingSystem = coolingSystem;
            return this;
        }
        
        public Builder caseType(String caseType) {
            this.caseType = caseType;
            return this;
        }
        
        public Builder networkCard(String networkCard) {
            this.networkCard = networkCard;
            return this;
        }
        
        public Builder hasWifi(boolean hasWifi) {
            this.hasWifi = hasWifi;
            return this;
        }
        
        public Builder hasBluetooth(boolean hasBluetooth) {
            this.hasBluetooth = hasBluetooth;
            return this;
        }
        
        /**
         * 构建Computer对象
         * 
         * @return Computer实例
         */
        public Computer build() {
            // 验证必需参数
            if (cpu == null || cpu.trim().isEmpty()) {
                throw new IllegalStateException("CPU是必需的配置");
            }
            if (ram <= 0) {
                throw new IllegalStateException("内存容量必须大于0");
            }
            if (storage == null || storage.trim().isEmpty()) {
                throw new IllegalStateException("存储设备是必需的配置");
            }
            
            log.info("🔨 开始组装电脑...");
            return new Computer(this);
        }
    }
    
    // Getters
    public String getCpu() { return cpu; }
    public int getRam() { return ram; }
    public String getStorage() { return storage; }
    public String getGpu() { return gpu; }
    public String getMotherboard() { return motherboard; }
    public String getPowerSupply() { return powerSupply; }
    public String getCoolingSystem() { return coolingSystem; }
    public String getCaseType() { return caseType; }
    public String getNetworkCard() { return networkCard; }
    public boolean hasWifi() { return hasWifi; }
    public boolean hasBluetooth() { return hasBluetooth; }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("💻 电脑配置清单:\n");
        sb.append("   🧠 CPU: ").append(cpu).append("\n");
        sb.append("   🎮 GPU: ").append(gpu).append("\n");
        sb.append("   💾 内存: ").append(ram).append("GB\n");
        sb.append("   💿 存储: ").append(storage).append("\n");
        sb.append("   🔌 主板: ").append(motherboard).append("\n");
        sb.append("   ⚡ 电源: ").append(powerSupply).append("\n");
        sb.append("   ❄️ 散热: ").append(coolingSystem).append("\n");
        sb.append("   📦 机箱: ").append(caseType).append("\n");
        sb.append("   🌐 网卡: ").append(networkCard).append("\n");
        sb.append("   📶 WiFi: ").append(hasWifi ? "支持" : "不支持").append("\n");
        sb.append("   🔵 蓝牙: ").append(hasBluetooth ? "支持" : "不支持");
        return sb.toString();
    }
}
