package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Linux风格按钮
 * 
 * <AUTHOR>
 */
@Slf4j
public class LinuxButton implements Button {
    
    @Override
    public void render() {
        log.info("🔘 渲染Linux风格按钮: |  确定  | (简洁边框)");
    }
    
    @Override
    public void click() {
        log.info("🖱️ Linux按钮被点击 - 简单的视觉反馈");
    }
    
    @Override
    public String getStyle() {
        return "Linux风格 - 简洁边框，开源设计";
    }
}
