package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Mac UI工厂
 * 
 * 创建Mac风格的UI组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class MacUIFactory implements UIComponentFactory {
    
    public MacUIFactory() {
        log.info("🍎 初始化Mac UI工厂");
    }
    
    @Override
    public Button createButton() {
        log.info("🔘 创建Mac风格按钮");
        return new MacButton();
    }
    
    @Override
    public TextField createTextField() {
        log.info("📝 创建Mac风格文本框");
        return new MacTextField();
    }
    
    @Override
    public Window createWindow() {
        log.info("🪟 创建Mac风格窗口");
        return new MacWindow();
    }
}
