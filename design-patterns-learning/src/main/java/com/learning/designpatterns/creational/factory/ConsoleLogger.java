package com.learning.designpatterns.creational.factory;

import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 控制台日志记录器
 * 
 * 将日志消息输出到控制台
 * 
 * <AUTHOR>
 */
@Slf4j
public class ConsoleLogger implements Logger {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public void log(String message) {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        String logEntry = String.format("[%s] [CONSOLE] %s", timestamp, message);
        
        // 输出到控制台
        System.out.println(logEntry);
        log.info("控制台日志: {}", message);
    }
    
    @Override
    public String getType() {
        return "CONSOLE";
    }
    
    @Override
    public String toString() {
        return "ConsoleLogger{type=" + getType() + "}";
    }
}
