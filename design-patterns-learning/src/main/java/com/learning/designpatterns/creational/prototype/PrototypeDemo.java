package com.learning.designpatterns.creational.prototype;

import lombok.extern.slf4j.Slf4j;

/**
 * 原型模式演示类
 * 
 * 原型模式用于创建重复的对象，同时又能保证性能。
 * 这种模式是实现了一个原型接口，该接口用于创建当前对象的克隆。
 * 
 * 应用场景：
 * - 对象创建成本较高（如数据库查询、网络请求）
 * - 需要创建大量相似对象
 * - 游戏中的角色复制
 * - 文档模板复制
 * - 图形编辑器中的形状复制
 * 
 * <AUTHOR>
 */
@Slf4j
public class PrototypeDemo {
    
    public static void main(String[] args) {
        log.info("=== 原型模式演示 ===");
        
        // 演示游戏角色原型
        demonstrateGameCharacterPrototype();

        // 演示其他原型应用场景
        demonstrateOtherPrototypeScenarios();
    }
    
    private static void demonstrateGameCharacterPrototype() {
        log.info("\n--- 游戏角色原型演示 ---");
        
        // 创建原型角色（模拟复杂的初始化过程）
        log.info("🎮 创建战士原型角色...");
        GameCharacter warriorPrototype = new GameCharacter("战士", "人类", 100, 80, 60);
        warriorPrototype.addSkill("剑术精通");
        warriorPrototype.addSkill("盾牌防御");
        warriorPrototype.addEquipment("钢铁剑");
        warriorPrototype.addEquipment("皮甲");
        
        log.info("🎮 创建法师原型角色...");
        GameCharacter magePrototype = new GameCharacter("法师", "精灵", 60, 40, 120);
        magePrototype.addSkill("火球术");
        magePrototype.addSkill("冰冻术");
        magePrototype.addEquipment("法杖");
        magePrototype.addEquipment("法袍");
        
        // 使用原型快速创建多个角色
        log.info("\n👥 使用原型快速创建角色:");
        
        GameCharacter warrior1 = (GameCharacter) warriorPrototype.clone();
        warrior1.setName("亚瑟");
        warrior1.setLevel(5);
        
        GameCharacter warrior2 = (GameCharacter) warriorPrototype.clone();
        warrior2.setName("兰斯洛特");
        warrior2.setLevel(8);
        
        GameCharacter mage1 = (GameCharacter) magePrototype.clone();
        mage1.setName("梅林");
        mage1.setLevel(10);
        
        // 显示角色信息
        log.info("⚔️ 战士1: {}", warrior1);
        log.info("⚔️ 战士2: {}", warrior2);
        log.info("🔮 法师1: {}", mage1);
    }
    
    private static void demonstrateOtherPrototypeScenarios() {
        log.info("\n--- 其他原型模式应用场景 ---");

        log.info("📄 文档模板原型:");
        log.info("   • 报告模板 - 快速创建月度、季度报告");
        log.info("   • 合同模板 - 基于标准合同创建具体合同");
        log.info("   • 邮件模板 - 批量发送个性化邮件");

        log.info("\n🎨 图形编辑器原型:");
        log.info("   • 形状原型 - 圆形、矩形、三角形等基础图形");
        log.info("   • 复杂图形 - 组合图形的快速复制");
        log.info("   • 样式模板 - 颜色、线条、填充样式");

        log.info("\n🏭 工业设计原型:");
        log.info("   • 产品配置 - 基于标准配置创建定制产品");
        log.info("   • 零件模板 - 标准零件的变体设计");
        log.info("   • 工艺流程 - 基于标准流程创建特定流程");

        log.info("\n💡 原型模式的优势:");
        log.info("   ✅ 避免重复的复杂初始化");
        log.info("   ✅ 提高对象创建性能");
        log.info("   ✅ 简化对象创建过程");
        log.info("   ✅ 支持动态配置");
    }
}
