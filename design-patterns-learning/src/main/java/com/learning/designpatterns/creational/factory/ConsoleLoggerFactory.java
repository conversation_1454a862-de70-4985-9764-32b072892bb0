package com.learning.designpatterns.creational.factory;

import lombok.extern.slf4j.Slf4j;

/**
 * 控制台日志记录器工厂
 * 
 * 负责创建控制台日志记录器实例
 * 
 * <AUTHOR>
 */
@Slf4j
public class ConsoleLoggerFactory extends LoggerFactory {
    
    @Override
    public Logger createLogger() {
        log.info("创建控制台日志记录器");
        return new ConsoleLogger();
    }
    
    @Override
    protected void cleanup(Logger logger) {
        // 控制台日志记录器的清理逻辑
        log.info("清理控制台日志记录器资源: {}", logger.getType());
        // 控制台日志通常不需要特殊清理
    }
}
