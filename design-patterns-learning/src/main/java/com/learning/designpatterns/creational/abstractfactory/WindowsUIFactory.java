package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Windows UI工厂
 * 
 * 创建Windows风格的UI组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class WindowsUIFactory implements UIComponentFactory {
    
    public WindowsUIFactory() {
        log.info("🪟 初始化Windows UI工厂");
    }
    
    @Override
    public Button createButton() {
        log.info("🔘 创建Windows风格按钮");
        return new WindowsButton();
    }
    
    @Override
    public TextField createTextField() {
        log.info("📝 创建Windows风格文本框");
        return new WindowsTextField();
    }
    
    @Override
    public Window createWindow() {
        log.info("🪟 创建Windows风格窗口");
        return new WindowsWindow();
    }
}
