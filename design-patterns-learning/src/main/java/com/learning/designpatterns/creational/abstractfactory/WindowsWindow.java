package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Windows风格窗口
 * 
 * <AUTHOR>
 */
@Slf4j
public class WindowsWindow implements Window {
    
    private String title = "Windows窗口";
    
    @Override
    public void render() {
        log.info("🪟 渲染Windows风格窗口:");
        log.info("   ┌─ {} ─ □ ✕ ┐", title);
        log.info("   │                    │");
        log.info("   │   Windows窗口内容   │");
        log.info("   │                    │");
        log.info("   └────────────────────┘");
    }
    
    @Override
    public void setTitle(String title) {
        this.title = title;
        log.info("🏷️ Windows窗口标题设置为: {}", title);
    }
    
    @Override
    public String getTitle() {
        return title;
    }
    
    @Override
    public void show() {
        log.info("👁️ 显示Windows窗口 - 淡入动画效果");
    }
    
    @Override
    public void hide() {
        log.info("🙈 隐藏Windows窗口 - 淡出动画效果");
    }
    
    @Override
    public String getStyle() {
        return "Windows风格 - 标题栏，最小化/最大化/关闭按钮";
    }
}
