package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Linux UI工厂
 * 
 * 创建Linux风格的UI组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class LinuxUIFactory implements UIComponentFactory {
    
    public LinuxUIFactory() {
        log.info("🐧 初始化Linux UI工厂");
    }
    
    @Override
    public Button createButton() {
        log.info("🔘 创建Linux风格按钮");
        return new LinuxButton();
    }
    
    @Override
    public TextField createTextField() {
        log.info("📝 创建Linux风格文本框");
        return new LinuxTextField();
    }
    
    @Override
    public Window createWindow() {
        log.info("🪟 创建Linux风格窗口");
        return new LinuxWindow();
    }
}
