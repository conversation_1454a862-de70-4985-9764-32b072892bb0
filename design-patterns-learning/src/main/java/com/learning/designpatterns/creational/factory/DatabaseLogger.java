package com.learning.designpatterns.creational.factory;

import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 数据库日志记录器
 * 
 * 将日志消息存储到数据库
 * 
 * <AUTHOR>
 */
@Slf4j
public class DatabaseLogger implements Logger {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public void log(String message) {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        String logEntry = String.format("[%s] [DATABASE] %s", timestamp, message);
        
        // 模拟存储到数据库
        log.info("存储到数据库: {}", logEntry);
        
        // 实际项目中，这里会执行数据库插入操作
        // String sql = "INSERT INTO logs (timestamp, level, message) VALUES (?, ?, ?)";
        // try (PreparedStatement stmt = connection.prepareStatement(sql)) {
        //     stmt.setTimestamp(1, Timestamp.valueOf(LocalDateTime.now()));
        //     stmt.setString(2, "INFO");
        //     stmt.setString(3, message);
        //     stmt.executeUpdate();
        // } catch (SQLException e) {
        //     log.error("存储数据库日志失败", e);
        // }
    }
    
    @Override
    public String getType() {
        return "DATABASE";
    }
    
    @Override
    public String toString() {
        return "DatabaseLogger{type=" + getType() + "}";
    }
}
