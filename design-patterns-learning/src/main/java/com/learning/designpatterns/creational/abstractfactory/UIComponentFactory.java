package com.learning.designpatterns.creational.abstractfactory;

/**
 * UI组件抽象工厂接口
 * 
 * 定义了创建一系列相关UI组件的接口
 * 
 * <AUTHOR>
 */
public interface UIComponentFactory {
    
    /**
     * 创建按钮
     * 
     * @return 按钮实例
     */
    Button createButton();
    
    /**
     * 创建文本框
     * 
     * @return 文本框实例
     */
    TextField createTextField();
    
    /**
     * 创建窗口
     * 
     * @return 窗口实例
     */
    Window createWindow();
}
