package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Linux风格窗口
 * 
 * <AUTHOR>
 */
@Slf4j
public class LinuxWindow implements Window {
    
    private String title = "Linux窗口";
    
    @Override
    public void render() {
        log.info("🐧 渲染Linux风格窗口:");
        log.info("   ┌─ {} ─ _ □ ✕ ┐", title);
        log.info("   │                    │");
        log.info("   │   Linux窗口内容    │");
        log.info("   │                    │");
        log.info("   └────────────────────┘");
    }
    
    @Override
    public void setTitle(String title) {
        this.title = title;
        log.info("🏷️ Linux窗口标题设置为: {}", title);
    }
    
    @Override
    public String getTitle() {
        return title;
    }
    
    @Override
    public void show() {
        log.info("👁️ 显示Linux窗口 - 快速显示");
    }
    
    @Override
    public void hide() {
        log.info("🙈 隐藏Linux窗口 - 快速隐藏");
    }
    
    @Override
    public String getStyle() {
        return "Linux风格 - 简洁设计，开源风格";
    }
}
