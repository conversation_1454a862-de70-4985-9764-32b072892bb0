package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Mac风格文本框
 * 
 * <AUTHOR>
 */
@Slf4j
public class MacTextField implements TextField {
    
    private String text = "";
    
    @Override
    public void render() {
        log.info("📝 渲染Mac风格文本框: ⌈{}⌉ (圆角边框)", text.isEmpty() ? "输入文本..." : text);
    }
    
    @Override
    public void setText(String text) {
        this.text = text;
        log.info("✏️ Mac文本框内容设置为: {}", text);
    }
    
    @Override
    public String getText() {
        return text;
    }
    
    @Override
    public String getStyle() {
        return "Mac风格 - 圆角边框，简洁外观";
    }
}
