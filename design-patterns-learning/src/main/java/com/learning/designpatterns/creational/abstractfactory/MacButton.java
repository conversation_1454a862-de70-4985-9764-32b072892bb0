package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * Mac风格按钮
 * 
 * <AUTHOR>
 */
@Slf4j
public class MacButton implements Button {
    
    @Override
    public void render() {
        log.info("🔘 渲染Mac风格按钮: ( 确定 ) (圆角设计)");
    }
    
    @Override
    public void click() {
        log.info("🖱️ Mac按钮被点击 - 优雅的点击反馈");
    }
    
    @Override
    public String getStyle() {
        return "Mac风格 - 圆角边框，简洁设计";
    }
}
