package com.learning.designpatterns.creational.factory;

import lombok.extern.slf4j.Slf4j;

/**
 * 工厂方法模式演示类
 * 
 * 工厂方法模式定义了一个创建对象的接口，但让子类决定实例化哪个类。
 * 工厂方法让类的实例化推迟到子类。
 * 
 * 应用场景：
 * - 数据库连接工厂（MySQL、Oracle、PostgreSQL）
 * - 日志记录器工厂（文件日志、控制台日志、网络日志）
 * - 图形界面组件工厂（Windows、Mac、Linux风格）
 * - 支付方式工厂（支付宝、微信、银行卡）
 * 
 * <AUTHOR>
 */
@Slf4j
public class FactoryMethodDemo {
    
    public static void main(String[] args) {
        log.info("=== 工厂方法模式演示 ===");
        
        // 演示不同类型的日志记录器
        demonstrateLoggerFactory();
        
        // 演示不同类型的支付方式
        demonstratePaymentFactory();
    }
    
    private static void demonstrateLoggerFactory() {
        log.info("\n--- 日志记录器工厂演示 ---");
        
        // 创建文件日志记录器
        LoggerFactory fileLoggerFactory = new FileLoggerFactory();
        Logger fileLogger = fileLoggerFactory.createLogger();
        fileLogger.log("这是一条文件日志消息");
        
        // 创建控制台日志记录器
        LoggerFactory consoleLoggerFactory = new ConsoleLoggerFactory();
        Logger consoleLogger = consoleLoggerFactory.createLogger();
        consoleLogger.log("这是一条控制台日志消息");
        
        // 创建数据库日志记录器
        LoggerFactory databaseLoggerFactory = new DatabaseLoggerFactory();
        Logger databaseLogger = databaseLoggerFactory.createLogger();
        databaseLogger.log("这是一条数据库日志消息");
    }
    
    private static void demonstratePaymentFactory() {
        log.info("\n--- 支付方式工厂演示 ---");
        
        // 创建支付宝支付
        PaymentFactory alipayFactory = new AlipayFactory();
        Payment alipayPayment = alipayFactory.createPayment();
        alipayPayment.pay(100.0);
        
        // 创建微信支付
        PaymentFactory wechatFactory = new WechatPayFactory();
        Payment wechatPayment = wechatFactory.createPayment();
        wechatPayment.pay(200.0);
        
        // 创建银行卡支付
        PaymentFactory bankCardFactory = new BankCardFactory();
        Payment bankCardPayment = bankCardFactory.createPayment();
        bankCardPayment.pay(300.0);
    }
}
