package com.learning.designpatterns.creational.factory;

import lombok.extern.slf4j.Slf4j;

/**
 * 支付宝支付实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class AlipayPayment implements Payment {
    
    @Override
    public boolean pay(double amount) {
        log.info("正在使用支付宝支付 {} 元", amount);
        
        // 模拟支付宝支付流程
        try {
            // 模拟网络请求延迟
            Thread.sleep(1000);
            
            // 模拟支付成功（90%成功率）
            boolean success = Math.random() > 0.1;
            
            if (success) {
                log.info("支付宝支付成功！交易金额: {} 元", amount);
            } else {
                log.warn("支付宝支付失败！可能是余额不足或网络问题");
            }
            
            return success;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("支付宝支付过程中断", e);
            return false;
        }
    }
    
    @Override
    public String getPaymentMethod() {
        return "支付宝";
    }
    
    @Override
    public double getFeeRate() {
        return 0.6; // 支付宝手续费率 0.6%
    }
    
    @Override
    public String toString() {
        return "AlipayPayment{method=" + getPaymentMethod() + ", feeRate=" + getFeeRate() + "%}";
    }
}
