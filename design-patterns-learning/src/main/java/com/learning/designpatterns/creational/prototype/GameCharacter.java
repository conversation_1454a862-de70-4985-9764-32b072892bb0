package com.learning.designpatterns.creational.prototype;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * 游戏角色类
 * 
 * 实现原型模式的具体类
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
@Setter
public class GameCharacter implements Prototype {
    
    private String name;
    private String race;
    private int health;
    private int attack;
    private int magic;
    private int level = 1;
    private List<String> skills;
    private List<String> equipment;
    
    public GameCharacter(String name, String race, int health, int attack, int magic) {
        this.name = name;
        this.race = race;
        this.health = health;
        this.attack = attack;
        this.magic = magic;
        this.skills = new ArrayList<>();
        this.equipment = new ArrayList<>();
        
        // 模拟复杂的初始化过程
        log.info("🎮 正在创建角色: {} ({})", name, race);
        try {
            Thread.sleep(500); // 模拟复杂初始化
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    // 私有构造函数用于克隆
    private GameCharacter(GameCharacter other) {
        this.name = other.name;
        this.race = other.race;
        this.health = other.health;
        this.attack = other.attack;
        this.magic = other.magic;
        this.level = other.level;
        this.skills = new ArrayList<>(other.skills);
        this.equipment = new ArrayList<>(other.equipment);
        
        log.info("🔄 克隆角色: {} ({})", name, race);
    }
    
    @Override
    public Prototype clone() {
        return new GameCharacter(this);
    }
    
    public void addSkill(String skill) {
        skills.add(skill);
    }
    
    public void addEquipment(String item) {
        equipment.add(item);
    }
    
    @Override
    public String toString() {
        return String.format("%s (%s) - 等级:%d, 生命:%d, 攻击:%d, 魔法:%d, 技能:%s, 装备:%s",
                name, race, level, health, attack, magic, skills, equipment);
    }
}
