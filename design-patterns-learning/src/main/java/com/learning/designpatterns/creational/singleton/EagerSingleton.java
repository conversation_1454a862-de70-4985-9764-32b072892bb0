package com.learning.designpatterns.creational.singleton;

import lombok.extern.slf4j.Slf4j;

/**
 * 饿汉式单例模式
 * 
 * 特点：
 * - 类加载时就创建实例
 * - 线程安全
 * - 可能造成资源浪费（如果实例从未被使用）
 * 
 * 优点：
 * - 实现简单
 * - 线程安全
 * - 没有性能问题
 * 
 * 缺点：
 * - 不是懒加载，可能造成内存浪费
 * - 无法传递参数给构造函数
 * 
 * <AUTHOR>
 */
@Slf4j
public class EagerSingleton {
    
    /**
     * 在类加载时就创建实例
     * 由于类加载机制保证了线程安全
     */
    private static final EagerSingleton INSTANCE = new EagerSingleton();
    
    /**
     * 私有构造函数，防止外部实例化
     */
    private EagerSingleton() {
        log.info("EagerSingleton 实例被创建");
        // 防止反射攻击
        if (INSTANCE != null) {
            throw new RuntimeException("不能通过反射创建单例实例");
        }
    }
    
    /**
     * 获取单例实例
     * 
     * @return 单例实例
     */
    public static EagerSingleton getInstance() {
        return INSTANCE;
    }
    
    /**
     * 业务方法示例
     */
    public void doSomething() {
        log.info("EagerSingleton 正在执行业务逻辑...");
    }
    
    /**
     * 防止序列化破坏单例
     * 
     * @return 单例实例
     */
    private Object readResolve() {
        return INSTANCE;
    }
    
    @Override
    public String toString() {
        return "EagerSingleton{hashCode=" + this.hashCode() + "}";
    }
}
