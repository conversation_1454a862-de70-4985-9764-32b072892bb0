package com.learning.designpatterns.creational.factory;

/**
 * 日志记录器工厂抽象类
 * 
 * 定义了创建日志记录器的工厂方法
 * 具体的创建逻辑由子类实现
 * 
 * <AUTHOR>
 */
public abstract class LoggerFactory {
    
    /**
     * 工厂方法：创建日志记录器
     * 
     * 这是一个抽象方法，具体的实现由子类提供
     * 
     * @return 日志记录器实例
     */
    public abstract Logger createLogger();
    
    /**
     * 模板方法：记录日志的完整流程
     * 
     * 这个方法定义了使用日志记录器的标准流程：
     * 1. 创建日志记录器
     * 2. 记录日志
     * 3. 清理资源（如果需要）
     * 
     * @param message 日志消息
     */
    public void logMessage(String message) {
        Logger logger = createLogger();
        logger.log(message);
        // 可以在这里添加清理逻辑
        cleanup(logger);
    }
    
    /**
     * 清理资源的钩子方法
     * 
     * 子类可以重写此方法来实现特定的清理逻辑
     * 
     * @param logger 需要清理的日志记录器
     */
    protected void cleanup(Logger logger) {
        // 默认不做任何清理
        // 子类可以重写此方法来实现特定的清理逻辑
    }
}
