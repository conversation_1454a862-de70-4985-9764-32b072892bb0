package com.learning.designpatterns.creational.abstractfactory;

/**
 * 文本框抽象接口
 * 
 * <AUTHOR>
 */
public interface TextField {
    
    /**
     * 渲染文本框
     */
    void render();
    
    /**
     * 设置文本
     * 
     * @param text 文本内容
     */
    void setText(String text);
    
    /**
     * 获取文本
     * 
     * @return 文本内容
     */
    String getText();
    
    /**
     * 获取文本框样式
     * 
     * @return 样式描述
     */
    String getStyle();
}
