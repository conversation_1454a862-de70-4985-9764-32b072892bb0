package com.learning.designpatterns.creational.abstractfactory;

import lombok.extern.slf4j.Slf4j;

/**
 * 应用程序类
 * 
 * 使用抽象工厂创建的UI组件
 * 
 * <AUTHOR>
 */
@Slf4j
public class Application {
    
    private final Button button;
    private final TextField textField;
    private final Window window;
    private String title = "应用程序";
    
    public Application(Button button, TextField textField, Window window) {
        this.button = button;
        this.textField = textField;
        this.window = window;
    }
    
    public void setTitle(String title) {
        this.title = title;
        window.setTitle(title);
    }
    
    public void render() {
        log.info("🎨 渲染应用程序: {}", title);
        
        // 渲染窗口
        window.render();
        window.show();
        
        // 设置和渲染文本框
        textField.setText("欢迎使用" + title);
        textField.render();
        
        // 渲染按钮
        button.render();
        
        // 模拟用户交互
        log.info("👆 模拟用户交互:");
        button.click();
        
        log.info("📊 组件样式信息:");
        log.info("   按钮: {}", button.getStyle());
        log.info("   文本框: {}", textField.getStyle());
        log.info("   窗口: {}", window.getStyle());
    }
}
