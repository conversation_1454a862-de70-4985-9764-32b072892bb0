package com.learning.designpatterns.creational.factory;

/**
 * 支付接口
 * 
 * 定义了支付的基本行为
 * 
 * <AUTHOR>
 */
public interface Payment {
    
    /**
     * 执行支付
     * 
     * @param amount 支付金额
     * @return 支付是否成功
     */
    boolean pay(double amount);
    
    /**
     * 获取支付方式名称
     * 
     * @return 支付方式名称
     */
    String getPaymentMethod();
    
    /**
     * 获取支付手续费率
     * 
     * @return 手续费率（百分比）
     */
    double getFeeRate();
}
