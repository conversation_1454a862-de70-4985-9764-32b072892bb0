package com.learning.designpatterns.creational.singleton;

import lombok.extern.slf4j.Slf4j;

/**
 * 懒汉式单例模式（线程安全版本）
 * 
 * 特点：
 * - 延迟加载，只有在需要时才创建实例
 * - 使用synchronized保证线程安全
 * - 性能相对较差（每次获取实例都需要同步）
 * 
 * 优点：
 * - 懒加载，节省内存
 * - 线程安全
 * 
 * 缺点：
 * - 性能较差，每次调用getInstance()都需要同步
 * - 可能存在性能瓶颈
 * 
 * <AUTHOR>
 */
@Slf4j
public class LazySingleton {
    
    /**
     * 单例实例，初始为null
     */
    private static LazySingleton instance;
    
    /**
     * 私有构造函数，防止外部实例化
     */
    private LazySingleton() {
        log.info("LazySingleton 实例被创建");
        // 防止反射攻击
        if (instance != null) {
            throw new RuntimeException("不能通过反射创建单例实例");
        }
    }
    
    /**
     * 获取单例实例（线程安全）
     * 使用synchronized关键字保证线程安全
     * 
     * @return 单例实例
     */
    public static synchronized LazySingleton getInstance() {
        if (instance == null) {
            log.info("首次创建 LazySingleton 实例");
            instance = new LazySingleton();
        }
        return instance;
    }
    
    /**
     * 业务方法示例
     */
    public void doSomething() {
        log.info("LazySingleton 正在执行业务逻辑...");
    }
    
    /**
     * 防止序列化破坏单例
     * 
     * @return 单例实例
     */
    private Object readResolve() {
        return instance;
    }
    
    @Override
    public String toString() {
        return "LazySingleton{hashCode=" + this.hashCode() + "}";
    }
}
