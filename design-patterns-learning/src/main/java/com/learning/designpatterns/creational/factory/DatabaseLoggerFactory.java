package com.learning.designpatterns.creational.factory;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库日志记录器工厂
 * 
 * 负责创建数据库日志记录器实例
 * 
 * <AUTHOR>
 */
@Slf4j
public class DatabaseLoggerFactory extends LoggerFactory {
    
    @Override
    public Logger createLogger() {
        log.info("创建数据库日志记录器");
        return new DatabaseLogger();
    }
    
    @Override
    protected void cleanup(Logger logger) {
        // 数据库日志记录器的清理逻辑
        log.info("清理数据库日志记录器资源: {}", logger.getType());
        // 实际项目中可能需要关闭数据库连接等
    }
}
