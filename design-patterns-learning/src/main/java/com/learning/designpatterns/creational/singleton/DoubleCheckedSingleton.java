package com.learning.designpatterns.creational.singleton;

import lombok.extern.slf4j.Slf4j;

/**
 * 双重检查锁定单例模式
 * 
 * 特点：
 * - 延迟加载
 * - 线程安全
 * - 性能较好（只在第一次创建时同步）
 * - 使用volatile关键字防止指令重排序
 * 
 * 优点：
 * - 懒加载，节省内存
 * - 线程安全
 * - 性能较好，只在第一次创建时同步
 * 
 * 缺点：
 * - 实现复杂
 * - 需要JDK 1.5+（volatile语义）
 * 
 * <AUTHOR>
 */
@Slf4j
public class DoubleCheckedSingleton {
    
    /**
     * 单例实例，使用volatile防止指令重排序
     * volatile确保多线程环境下的可见性和有序性
     */
    private static volatile DoubleCheckedSingleton instance;
    
    /**
     * 私有构造函数，防止外部实例化
     */
    private DoubleCheckedSingleton() {
        log.info("DoubleCheckedSingleton 实例被创建");
        // 防止反射攻击
        if (instance != null) {
            throw new RuntimeException("不能通过反射创建单例实例");
        }
    }
    
    /**
     * 获取单例实例（双重检查锁定）
     * 
     * 第一次检查：避免不必要的同步
     * 同步块：保证线程安全
     * 第二次检查：防止多个线程同时通过第一次检查
     * 
     * @return 单例实例
     */
    public static DoubleCheckedSingleton getInstance() {
        // 第一次检查，避免不必要的同步
        if (instance == null) {
            synchronized (DoubleCheckedSingleton.class) {
                // 第二次检查，防止多个线程同时通过第一次检查
                if (instance == null) {
                    log.info("首次创建 DoubleCheckedSingleton 实例");
                    instance = new DoubleCheckedSingleton();
                }
            }
        }
        return instance;
    }
    
    /**
     * 业务方法示例
     */
    public void doSomething() {
        log.info("DoubleCheckedSingleton 正在执行业务逻辑...");
    }
    
    /**
     * 防止序列化破坏单例
     * 
     * @return 单例实例
     */
    private Object readResolve() {
        return instance;
    }
    
    @Override
    public String toString() {
        return "DoubleCheckedSingleton{hashCode=" + this.hashCode() + "}";
    }
}
