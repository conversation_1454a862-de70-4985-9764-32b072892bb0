package com.learning.designpatterns.creational.builder;

import lombok.extern.slf4j.Slf4j;

/**
 * 房屋类 - 建造者模式示例
 * 
 * 演示如何使用建造者模式构建复杂的房屋对象
 * 
 * <AUTHOR>
 */
@Slf4j
public class House {
    
    // 基础结构
    private final String foundation;
    private final String structure;
    private final String roof;
    private final String walls;
    
    // 门窗
    private final String windows;
    private final String doors;
    
    // 内部装修
    private final String floors;
    private final int rooms;
    private final int bathrooms;
    
    // 附加设施
    private final boolean hasGarage;
    private final boolean hasGarden;
    private final boolean hasSwimmingPool;
    private final boolean hasBasement;
    private final boolean hasBalcony;
    
    /**
     * 私有构造函数，只能通过Builder创建
     */
    private House(Builder builder) {
        this.foundation = builder.foundation;
        this.structure = builder.structure;
        this.roof = builder.roof;
        this.walls = builder.walls;
        this.windows = builder.windows;
        this.doors = builder.doors;
        this.floors = builder.floors;
        this.rooms = builder.rooms;
        this.bathrooms = builder.bathrooms;
        this.hasGarage = builder.hasGarage;
        this.hasGarden = builder.hasGarden;
        this.hasSwimmingPool = builder.hasSwimmingPool;
        this.hasBasement = builder.hasBasement;
        this.hasBalcony = builder.hasBalcony;
        
        log.info("🏠 房屋建造完成: {}室{}卫", rooms, bathrooms);
    }
    
    /**
     * 建造者类
     */
    public static class Builder {
        // 必需参数
        private String foundation;
        private String structure;
        private int rooms = 1;
        private int bathrooms = 1;
        
        // 可选参数 - 初始化为默认值
        private String roof = "平屋顶";
        private String walls = "砖墙";
        private String windows = "普通窗户";
        private String doors = "木门";
        private String floors = "水泥地面";
        private boolean hasGarage = false;
        private boolean hasGarden = false;
        private boolean hasSwimmingPool = false;
        private boolean hasBasement = false;
        private boolean hasBalcony = false;
        
        public Builder foundation(String foundation) {
            this.foundation = foundation;
            return this;
        }
        
        public Builder structure(String structure) {
            this.structure = structure;
            return this;
        }
        
        public Builder roof(String roof) {
            this.roof = roof;
            return this;
        }
        
        public Builder walls(String walls) {
            this.walls = walls;
            return this;
        }
        
        public Builder windows(String windows) {
            this.windows = windows;
            return this;
        }
        
        public Builder doors(String doors) {
            this.doors = doors;
            return this;
        }
        
        public Builder floors(String floors) {
            this.floors = floors;
            return this;
        }
        
        public Builder rooms(int rooms) {
            if (rooms <= 0) {
                throw new IllegalArgumentException("房间数量必须大于0");
            }
            this.rooms = rooms;
            return this;
        }
        
        public Builder bathrooms(int bathrooms) {
            if (bathrooms <= 0) {
                throw new IllegalArgumentException("卫生间数量必须大于0");
            }
            this.bathrooms = bathrooms;
            return this;
        }
        
        public Builder garage(boolean hasGarage) {
            this.hasGarage = hasGarage;
            return this;
        }
        
        public Builder garden(boolean hasGarden) {
            this.hasGarden = hasGarden;
            return this;
        }
        
        public Builder swimmingPool(boolean hasSwimmingPool) {
            this.hasSwimmingPool = hasSwimmingPool;
            return this;
        }
        
        public Builder basement(boolean hasBasement) {
            this.hasBasement = hasBasement;
            return this;
        }
        
        public Builder balcony(boolean hasBalcony) {
            this.hasBalcony = hasBalcony;
            return this;
        }
        
        /**
         * 构建House对象
         * 
         * @return House实例
         */
        public House build() {
            // 验证必需参数
            if (foundation == null || foundation.trim().isEmpty()) {
                throw new IllegalStateException("地基是必需的");
            }
            if (structure == null || structure.trim().isEmpty()) {
                throw new IllegalStateException("结构是必需的");
            }
            
            log.info("🔨 开始建造房屋...");
            return new House(this);
        }
    }
    
    // Getters
    public String getFoundation() { return foundation; }
    public String getStructure() { return structure; }
    public String getRoof() { return roof; }
    public String getWalls() { return walls; }
    public String getWindows() { return windows; }
    public String getDoors() { return doors; }
    public String getFloors() { return floors; }
    public int getRooms() { return rooms; }
    public int getBathrooms() { return bathrooms; }
    public boolean hasGarage() { return hasGarage; }
    public boolean hasGarden() { return hasGarden; }
    public boolean hasSwimmingPool() { return hasSwimmingPool; }
    public boolean hasBasement() { return hasBasement; }
    public boolean hasBalcony() { return hasBalcony; }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("🏠 房屋建造清单:\n");
        sb.append("   🏗️ 地基: ").append(foundation).append("\n");
        sb.append("   🏢 结构: ").append(structure).append("\n");
        sb.append("   🏠 屋顶: ").append(roof).append("\n");
        sb.append("   🧱 墙体: ").append(walls).append("\n");
        sb.append("   🪟 窗户: ").append(windows).append("\n");
        sb.append("   🚪 门: ").append(doors).append("\n");
        sb.append("   🏠 地板: ").append(floors).append("\n");
        sb.append("   🛏️ 房间: ").append(rooms).append("间\n");
        sb.append("   🚿 卫生间: ").append(bathrooms).append("间\n");
        sb.append("   🚗 车库: ").append(hasGarage ? "有" : "无").append("\n");
        sb.append("   🌳 花园: ").append(hasGarden ? "有" : "无").append("\n");
        sb.append("   🏊 游泳池: ").append(hasSwimmingPool ? "有" : "无").append("\n");
        sb.append("   🏠 地下室: ").append(hasBasement ? "有" : "无").append("\n");
        sb.append("   🌅 阳台: ").append(hasBalcony ? "有" : "无");
        return sb.toString();
    }
}
