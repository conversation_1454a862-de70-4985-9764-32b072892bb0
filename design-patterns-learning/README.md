# Java 设计模式学习项目

## 项目简介

这是一个专门用于学习Java设计模式的项目，包含了23种经典设计模式的完整实现和详细示例。每个设计模式都包含：

- 📝 详细的代码实现
- 💡 实际应用场景示例
- 🧪 完整的单元测试
- 📖 中文注释和说明

## 设计模式分类

### 🏗️ 创建型模式 (Creational Patterns)
创建型模式关注对象的创建过程，将对象的创建与使用分离。

1. **单例模式 (Singleton)** - 确保一个类只有一个实例
2. **工厂方法模式 (Factory Method)** - 创建对象的接口，让子类决定实例化哪个类
3. **抽象工厂模式 (Abstract Factory)** - 创建相关对象家族的接口
4. **建造者模式 (Builder)** - 分步骤构建复杂对象
5. **原型模式 (Prototype)** - 通过复制现有实例创建新对象

### 🔧 结构型模式 (Structural Patterns)
结构型模式关注类和对象的组合，形成更大的结构。

6. **适配器模式 (Adapter)** - 让不兼容的接口协同工作
7. **桥接模式 (Bridge)** - 将抽象与实现分离
8. **组合模式 (Composite)** - 将对象组合成树形结构
9. **装饰器模式 (Decorator)** - 动态地给对象添加功能
10. **外观模式 (Facade)** - 为复杂子系统提供简单接口
11. **享元模式 (Flyweight)** - 通过共享减少内存使用
12. **代理模式 (Proxy)** - 为其他对象提供代理以控制访问

### 🎭 行为型模式 (Behavioral Patterns)
行为型模式关注对象之间的通信和职责分配。

13. **责任链模式 (Chain of Responsibility)** - 将请求沿着处理链传递
14. **命令模式 (Command)** - 将请求封装为对象
15. **解释器模式 (Interpreter)** - 定义语言的语法表示
16. **迭代器模式 (Iterator)** - 顺序访问集合元素
17. **中介者模式 (Mediator)** - 定义对象间的交互方式
18. **备忘录模式 (Memento)** - 保存和恢复对象状态
19. **观察者模式 (Observer)** - 定义对象间的一对多依赖关系
20. **状态模式 (State)** - 根据内部状态改变对象行为
21. **策略模式 (Strategy)** - 定义算法家族并使其可互换
22. **模板方法模式 (Template Method)** - 定义算法骨架，子类实现具体步骤
23. **访问者模式 (Visitor)** - 在不修改类的前提下定义新操作

## 项目结构

```
design-patterns-learning/
├── src/
│   ├── main/
│   │   └── java/
│   │       └── com/
│   │           └── learning/
│   │               └── designpatterns/
│   │                   ├── creational/          # 创建型模式
│   │                   │   ├── singleton/
│   │                   │   ├── factory/
│   │                   │   ├── abstractfactory/
│   │                   │   ├── builder/
│   │                   │   └── prototype/
│   │                   ├── structural/          # 结构型模式
│   │                   │   ├── adapter/
│   │                   │   ├── bridge/
│   │                   │   ├── composite/
│   │                   │   ├── decorator/
│   │                   │   ├── facade/
│   │                   │   ├── flyweight/
│   │                   │   └── proxy/
│   │                   └── behavioral/          # 行为型模式
│   │                       ├── chainofresponsibility/
│   │                       ├── command/
│   │                       ├── interpreter/
│   │                       ├── iterator/
│   │                       ├── mediator/
│   │                       ├── memento/
│   │                       ├── observer/
│   │                       ├── state/
│   │                       ├── strategy/
│   │                       ├── templatemethod/
│   │                       └── visitor/
│   └── test/
│       └── java/                               # 单元测试
├── pom.xml
└── README.md
```

## 如何使用

1. **克隆或下载项目**
2. **导入IDE** - 推荐使用IntelliJ IDEA或Eclipse
3. **运行示例** - 每个模式都有对应的示例类和测试类
4. **学习顺序建议**：
   - 先学习创建型模式（相对简单）
   - 再学习结构型模式
   - 最后学习行为型模式（相对复杂）

## 技术栈

- **Java 17** - 使用最新的LTS版本
- **Maven** - 项目管理和构建工具
- **JUnit 5** - 单元测试框架
- **Lombok** - 减少样板代码
- **SLF4J + Logback** - 日志框架

## 学习建议

1. **理论结合实践** - 先理解模式的意图和结构，再看代码实现
2. **运行示例代码** - 通过运行和调试加深理解
3. **修改和扩展** - 尝试修改示例，实现自己的变体
4. **思考应用场景** - 考虑在实际项目中如何应用这些模式
5. **避免过度设计** - 理解何时使用和何时不使用设计模式

## 贡献

欢迎提交Issue和Pull Request来改进这个学习项目！

## 许可证

MIT License
