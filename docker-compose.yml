version: '3.8'

# 定义通用服务配置
x-service-common: &service-common
  networks:
    - app-network
  restart: unless-stopped
  environment:
    - TZ=Asia/Shanghai
    - SPRING_PROFILES_ACTIVE=prod
    - JAVA_OPTS=-Xmx512m -Dlogging.level.root=DEBUG
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"

services:
  redis-learning:
    <<: *service-common  # 继承通用配置
    image: ${REDIS_LEARNING_IMAGE}
    ports:
      - "8081:8081"

  camel-learning:
    <<: *service-common  # 继承通用配置
    image: ${CAMEL_LEARNING_IMAGE}
    ports:
      - "8082:8082"

  web-learning:
    <<: *service-common  # 继承通用配置
    image: ${WEB_LEARNING_IMAGE}
    ports:
      - "8083:8083"

networks:
  app-network:
    driver: bridge

volumes:
  jenkins_home:
  maven_repo: