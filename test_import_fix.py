# -*- coding: utf-8 -*-
"""
测试导入配置修复
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.config_manager import ConfigManager
from logger import <PERSON><PERSON><PERSON><PERSON><PERSON>ogger

def test_import_functionality():
    """测试导入功能"""
    print("🧪 测试配置导入功能修复...")
    
    # 初始化组件
    logger = KcptunLogger()
    config_manager = ConfigManager("test_import_output.json", logger)
    
    print("\n1️⃣ 测试导入单个配置文件...")
    
    # 测试导入单个配置
    single_config_path = "test_config.json"
    if os.path.exists(single_config_path):
        print(f"导入文件: {single_config_path}")
        
        if config_manager.import_config(single_config_path):
            print("✅ 单个配置导入成功")
            
            # 检查导入的配置
            imported_profiles = config_manager.get_last_imported_profiles()
            print(f"📋 导入的配置: {imported_profiles}")
            
            if imported_profiles:
                profile_name = imported_profiles[0]
                profile_data = config_manager.get_profile(profile_name)
                print(f"📝 配置 '{profile_name}' 内容:")
                print(f"   服务器IP: {profile_data.get('kcp_ip')}")
                print(f"   服务器端口: {profile_data.get('kcp_port')}")
                print(f"   本地端口: {profile_data.get('local_port')}")
                print(f"   传输模式: {profile_data.get('advanced', {}).get('mode')}")
            
            config_manager.clear_last_imported_profiles()
        else:
            print("❌ 单个配置导入失败")
    else:
        print(f"❌ 测试文件不存在: {single_config_path}")
    
    print("\n2️⃣ 测试导入完整配置文件...")
    
    # 测试导入完整配置文件
    full_config_path = "all_configs.json"
    if os.path.exists(full_config_path):
        print(f"导入文件: {full_config_path}")
        
        if config_manager.import_config(full_config_path):
            print("✅ 完整配置导入成功")
            
            # 检查导入的配置
            imported_profiles = config_manager.get_last_imported_profiles()
            print(f"📋 导入的配置: {imported_profiles}")
            
            # 显示所有配置
            all_profiles = config_manager.get_profile_names()
            print(f"📋 当前所有配置: {all_profiles}")
            
            # 检查具体配置内容
            for profile_name in imported_profiles[:2]:  # 只显示前两个
                profile_data = config_manager.get_profile(profile_name)
                print(f"📝 配置 '{profile_name}' 内容:")
                print(f"   服务器IP: {profile_data.get('kcp_ip')}")
                print(f"   服务器端口: {profile_data.get('kcp_port')}")
                print(f"   本地端口: {profile_data.get('local_port')}")
                print(f"   传输模式: {profile_data.get('advanced', {}).get('mode')}")
                print(f"   发送窗口: {profile_data.get('advanced', {}).get('sndwnd')}")
            
            config_manager.clear_last_imported_profiles()
        else:
            print("❌ 完整配置导入失败")
    else:
        print(f"❌ 测试文件不存在: {full_config_path}")
    
    print("\n🎉 导入功能测试完成！")

if __name__ == "__main__":
    test_import_functionality()
