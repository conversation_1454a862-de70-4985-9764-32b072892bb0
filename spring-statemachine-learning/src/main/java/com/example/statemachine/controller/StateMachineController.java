package com.example.statemachine.controller;

import com.example.statemachine.service.StateMachineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class StateMachineController {

    private final StateMachineService stateMachineService;

    @Autowired
    public StateMachineController(StateMachineService stateMachineService) {
        this.stateMachineService = stateMachineService;
    }

    @PostMapping("/trigger")
    public String triggerEvent(@RequestParam String event) {
        stateMachineService.sendEvent(event);
        return "Event " + event + " triggered.";
    }
} 