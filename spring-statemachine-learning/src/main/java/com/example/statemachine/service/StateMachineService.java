package com.example.statemachine.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.stereotype.Service;

@Service
public class StateMachineService {

    private final StateMachine<String, String> stateMachine;

    @Autowired
    public StateMachineService(StateMachineFactory<String, String> stateMachineFactory) {
        this.stateMachine = stateMachineFactory.getStateMachine();
        this.stateMachine.start();
    }

    public void sendEvent(String event) {
        stateMachine.sendEvent(event);
    }
} 