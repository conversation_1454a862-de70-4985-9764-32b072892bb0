server:
  port: 8080

spring:
  application:
    name: spring-statemachine-learning

logging:
  level:
    root: INFO
    com.example: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

common:
  logging:
    enabled: true
    log-path: logs
    level: INFO
    module-name: ${spring.application.name}
    max-history: 30
    max-file-size: 100MB
    enable-console: true
    pattern: "%d{HH:mm:ss} [%-5thread] %-5level %logger{36} [%X{traceId}] >>> %msg%n"