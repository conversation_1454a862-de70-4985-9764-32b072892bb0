#!/bin/bash

# 构建状态
BUILD_STATUS=$1
DING_TOKEN="你的钉钉机器人token"

if [ "$BUILD_STATUS" = "success" ]; then
    MESSAGE="构建成功：${JOB_NAME} #${BUILD_NUMBER}"
else
    MESSAGE="构建失败：${JOB_NAME} #${BUILD_NUMBER}"
fi

# 发送钉钉通知
curl -H "Content-Type: application/json" \
     -d "{\"msgtype\": \"text\", \"text\": {\"content\": \"${MESSAGE}\"}}" \
     https://oapi.dingtalk.com/robot/send?access_token=${DING_TOKEN} 