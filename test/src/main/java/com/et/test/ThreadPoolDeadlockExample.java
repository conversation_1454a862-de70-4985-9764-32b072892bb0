package com.et.test;

import java.util.concurrent.*;

public class ThreadPoolDeadlockExample {
    public static void main(String[] args) {
        // 创建一个只有一个线程的线程池
        ExecutorService executorService = Executors.newFixedThreadPool(1);

        try {
            // 提交一个任务（父任务）
            Future<?> parentFuture = executorService.submit(() -> {
                System.out.println("执行父任务");

                // 在父任务内部提交另一个任务（子任务）到同一个线程池中
                Future<?> future = executorService.submit(() -> {
                    System.out.println("执行子任务");
                });

                try {
                    // 等待子任务完成
                    future.get();
                } catch (InterruptedException | ExecutionException e) {
                    e.printStackTrace();
                }

                System.out.println("父任务完成");
            });

            // 确保父任务执行完成
            try {
                parentFuture.get();
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }

        } finally {
            // 关闭线程池，并确保任务完成
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    System.out.println("线程池未能在规定时间内终止，强制终止");
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                // 当前线程被中断时，再次强制关闭线程池
                executorService.shutdownNow();
            }
        }
    }
}
