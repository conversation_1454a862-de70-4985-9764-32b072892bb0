<dataConfig>
  <dataSource type="BinFileDataSource"/>
  <document>
    <entity name="file" processor="FileListEntityProcessor" dataSource="null"
            baseDir="${solr.install.dir}/files/file_repos" fileName=".*\.(DOC)|(PDF)|(pdf)|(doc)|(docx)|(ppt)|(txt)|(log)"
            rootEntity="false" onError="skip" recursive="true">

      
		<field column="fileAbsolutePath" name="id" />
		<field column="fileSize" name="size" />
		<field column="fileLastModified" name="lastModified" />
		<entity name="tika-test" dataSource="bin" processor="TikaEntityProcessor"
				url="${file.fileAbsolutePath}" format="text" onError="skip">                
			<field column="Author" name="author" meta="true"/>
			<field column="title" name="title" meta="true"/>
			<field column="dc:format" name="format" meta="true"/>
			<field column="text" name="content"/> 				           
		</entity>
	  
    </entity>
  </document>
</dataConfig>