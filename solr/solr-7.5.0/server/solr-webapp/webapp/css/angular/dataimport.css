/*

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

*/

#content #dataimport
{
  background-image: url( ../../img/div.gif );
  background-position: 21% 0;
  background-repeat: repeat-y;
}

#content #dataimport #frame
{
  float: right;
  width: 78%;
}

#content #dataimport #form
{
  float: left;
  width: 20%;
}

#content #dataimport #form #navigation
{
  border-right: 0;
}

#content #dataimport #form #navigation a
{
  background-image: url( ../../img/ico/status-offline.png );
}

#content #dataimport #form #navigation .current a
{
  background-image: url( ../../img/ico/status.png );
}

#content #dataimport #form form
{
  border-top: 1px solid #f0f0f0;
  margin-top: 10px;
  padding-top: 5px;
}

#content #dataimport #form label
{
  cursor: pointer;
  display: block;
  margin-top: 5px;
}

#content #dataimport #form input,
#content #dataimport #form select,
#content #dataimport #form textarea
{
  margin-bottom: 2px;
  width: 100%;
}

#content #dataimport #form input
{
  width: 98%;
}

#content #dataimport #form button
{
  margin-top: 10px;
}

#content #dataimport #form .execute span
{
  background-image: url( ../../img/ico/document-import.png );
}

#content #dataimport #form .refresh-status span
{
  background-image: url( ../../img/ico/arrow-circle.png );
}

#content #dataimport #form .refresh-status span.success
{
  background-image: url( ../../img/ico/tick.png );
}

#content #dataimport #form #start
{
  float: left;
  width: 47%;
}

#content #dataimport #form #rows
{
  float: right;
  width: 47%;
}

#content #dataimport #form .checkbox input
{
  margin-bottom: 0;
  width: auto;
}

#content #dataimport #form #auto-refresh-status
{
  margin-top: 20px;
}

#content #dataimport #form #auto-refresh-status a
{
  background-image: url( ../../img/ico/ui-check-box-uncheck.png );
  background-position: 0 50%;
  color: #4D4D4D;
  display: block;
  padding-left: 21px;
}

#content #dataimport #form #auto-refresh-status a.on,
#content #dataimport #form #auto-refresh-status a:hover
{
  color: #333;
}

#content #dataimport #form #auto-refresh-status a.on
{
  background-image: url( ../../img/ico/ui-check-box.png );
}

#content #dataimport #current_state
{
  padding: 10px;
  margin-bottom: 20px;
}

#content #dataimport #current_state .last_update,
#content #dataimport #current_state .info
{
  display: block;
  padding-left: 21px;
}

#content #dataimport #current_state .last_update
{
  color: #4D4D4D;
  font-size: 11px;
}

#content #dataimport #current_state .info
{
  background-position: 0 1px;
  position: relative;
}

#content #dataimport #current_state .info .details span
{
#  color: #c0c0c0;
}

#content #dataimport #current_state .info .abort-import
{
  position: absolute;
  right: 0px;
  top: 0px;
}

#content #dataimport #current_state .info .abort-import span
{
  background-image: url( ../../img/ico/cross.png );
}

#content #dataimport #current_state .info .abort-import.success span
{
  background-image: url( ../../img/ico/tick.png );
}

#content #dataimport #current_state.indexing
{
  background-color: #f9f9f9;
}

#content #dataimport #current_state.indexing .info
{
  background-image: url( ../../img/ico/hourglass.png );
}

#content #dataimport #current_state.indexing .info .abort-import
{
  display: block;
}

#content #dataimport #current_state.success
{
  background-color: #e6f3e6;
}

#content #dataimport #current_state.success .info
{
  background-image: url( ../../img/ico/tick-circle.png );
}

#content #dataimport #current_state.success .info strong
{
  color: #080;
}

#content #dataimport #current_state.aborted
{
  background-color: #f3e6e6;
}

#content #dataimport #current_state.aborted .info
{
  background-image: url( ../../img/ico/slash.png );
}

#content #dataimport #current_state.aborted .info strong
{
  color: #800;
}

#content #dataimport #current_state.failure
{
  background-color: #f3e6e6;
}

#content #dataimport #current_state.failure .info
{
  background-image: url( ../../img/ico/cross-button.png );
}

#content #dataimport #current_state.failure .info strong
{
  color: #800;
}

#content #dataimport #current_state.idle
{
  background-color: #e6e6ff;
}

#content #dataimport #current_state.idle .info
{
  background-image: url( ../../img/ico/information.png );
}

#content #dataimport #error
{
  background-color: #f00;
  background-image: url( ../../img/ico/construction.png );
  background-position: 10px 50%;
  color: #fff;
  font-weight: bold;
  margin-bottom: 20px;
  padding: 10px;
  padding-left: 35px;
}

#content #dataimport .block h2
{
  border-color: #c0c0c0;
  padding-left: 5px;
  position: relative;
}

#content #dataimport .block.hidden h2
{
  border-color: #fafafa;
}

#content #dataimport .block h2 a.toggle
{
  background-image: url( ../../img/ico/toggle-small.png );
  background-position: 0 50%;
  padding-left: 21px;
}

#content #dataimport .block.hidden h2 a.toggle
{
  background-image: url( ../../img/ico/toggle-small-expand.png );
}

#content #dataimport #config h2 a.r
{
  background-position: 3px 50%;
  display: block;
  float: right;
  margin-left: 10px;
  padding-left: 24px;
  padding-right: 3px;
}

#content #dataimport #config h2 a.reload_config
{
  background-image: url( ../../img/ico/arrow-circle.png );
}

#content #dataimport #config h2 a.reload_config.success
{
  background-image: url( ../../img/ico/tick.png );
}

#content #dataimport #config h2 a.reload_config.error
{
  background-image: url( ../../img/ico/slash.png );
}

#content #dataimport #config h2 a.debug_mode
{
  background-image: url( ../../img/ico/hammer.png );
  color: #4D4D4D;
}

#content #dataimport #config.debug_mode h2 a.debug_mode
{
  background-color: #ff0;
  background-image: url( ../../img/ico/hammer-screwdriver.png );
  color: #333;
}

#content #dataimport #config .content
{
  padding: 5px 2px;
}

#content #dataimport #dataimport_config .loader
{
  background-position: 0 50%;
  padding-left: 21px;
}

#content #dataimport #dataimport_config .formatted
{
  border: 1px solid #fff;
  display: block;
  padding: 2px;
}

#content #dataimport .debug_mode #dataimport_config .editable
{
  display: block;
}

#content #dataimport #dataimport_config .editable textarea
{
  font-family: monospace;
  height: 120px;
  min-height: 60px;
  width: 100%;
}

#content #dataimport #debug_response em
{
  color: #4D4D4D;
  font-style: normal;
}
