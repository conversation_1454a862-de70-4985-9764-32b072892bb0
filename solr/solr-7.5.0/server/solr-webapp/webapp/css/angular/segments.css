/*

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

*/

#content #segments .loader
{
  background-position: 0 50%;
  padding-left: 21px;
}

#content #segments .reload
{
  background-image: url( ../../img/ico/arrow-circle.png );
  background-position: 50% 50%;
  display: block;
  height: 30px;
  position: absolute;
  right: 10px;
  top: 10px;
  width: 30px;
}

#content #segments .reload.loader
{
  padding-left: 0;
}

#content #segments .reload span
{
  display: none;
}

#content #segments #result
{
  width: 77%;
}

#content #segments #result #response
{
  margin-left: 25px;
}

#content #segments .segments-holder ul {
    margin-left: 25px;
}
#content #segments .segments-holder li {
    margin-bottom: 2px;
    position: relative;
    width: 100%;
}

#content #segments .segments-holder li .tooltip {
  display: none;
    background: #f0f0f0;
    position: absolute;
    z-index: 1000;
    width:220px;
    height:120px;
    margin-left: 100%;
    opacity: .8;
    padding: 5px;
    border: 1px solid;
    border-radius: 5px;
}

#content #segments .segments-holder li .tooltip .label {
  float: left;
  width: 20%;
  opacity: 1;
}

#content #segments .segments-holder li:hover .tooltip {
  display:block;
}

#content #segments .segments-holder li dl,
#content #segments .segments-holder li dt {
    padding-bottom: 1px;
    padding-top: 1px;
}
#content #segments .segments-holder li dl {
    min-width: 1px;
}
#content #segments .segments-holder li dt {
    color: #4D4D4D;
    left: -45px;
    overflow: hidden;
    position: absolute;
    top: 0;
}
#content #segments .segments-holder li dt div {
    display: block;
    padding-right: 4px;
    text-align: right;
}
#content #segments .segments-holder li dd {
    clear: left;
    float: left;
    margin-left: 2px;
    white-space: nowrap;
    width: 100%;
}

#content #segments .segments-holder li dd div.deleted {
    background-color: #808080;
    padding-left: 5px;
}

#content #segments .segments-holder li dd div.live {
  background-color: #DDDDDD;
  float: left;
}

#content #segments .segments-holder li dd div.start {
  float: left;
  width: 20%;
}

#content #segments .segments-holder li dd div.end {
  text-align: right;
}

.merge-candidate {
  background-color: #FFC9F9 !important;
}

#content #segments .segments-holder li dd div.w5 {
  width: 20%;
  float: left;
}

#content #segments #auto-refresh {
  margin-top: 4px;
  background-position: 50% 50%;
  display: block;
  height: 30px;
  position: absolute;
  right: 50px;
  top: 10px;
}

#content #segments #auto-refresh a {
  background-image: url( ../../img/ico/ui-check-box-uncheck.png );
  background-position: 0 50%;
  color: #4D4D4D;
  display: block;
  padding-left: 21px;
}

#content #segments #auto-refresh a.on,
#content #segments #auto-refresh a:hover {
  color: #333;
}

#content #segments #auto-refresh a.on {
  background-image: url( ../../img/ico/ui-check-box.png );
}
