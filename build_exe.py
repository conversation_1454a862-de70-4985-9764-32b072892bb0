# -*- coding: utf-8 -*-
"""
Kcptun GUI v2.0 打包脚本
使用 PyInstaller 将程序打包为 exe 文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_dependencies():
    """检查打包依赖"""
    print("🔍 检查打包依赖...")
    
    required_packages = ['pyinstaller', 'pystray', 'Pillow']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'pyinstaller':
                import PyInstaller
            elif package == 'pystray':
                import pystray
            elif package == 'Pillow':
                import PIL
            else:
                __import__(package.replace('-', '_'))
            print(f"✅ {package} - 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    if missing_packages:
        print(f"\n📦 需要安装以下依赖包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def clean_build_dirs():
    """清理构建目录"""
    print("\n🧹 清理构建目录...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🗑️ 删除目录: {dir_name}")
    
    # 清理 .spec 文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"🗑️ 删除文件: {spec_file}")

def create_icon():
    """创建或确保图标文件存在"""
    print("\n🎨 准备图标文件...")
    
    icon_path = Path("resources/icon.ico")
    png_path = Path("resources/icon.png")
    
    # 确保 resources 目录存在
    icon_path.parent.mkdir(exist_ok=True)
    
    if icon_path.exists():
        print(f"✅ 图标文件已存在: {icon_path}")
        return str(icon_path)
    
    if png_path.exists():
        try:
            from PIL import Image
            print(f"🔄 转换 PNG 到 ICO: {png_path} -> {icon_path}")
            img = Image.open(png_path)
            img.save(icon_path, format='ICO', sizes=[(32, 32), (48, 48)])
            print(f"✅ 图标转换成功: {icon_path}")
            return str(icon_path)
        except Exception as e:
            print(f"⚠️ 图标转换失败: {e}")
    
    # 创建默认图标
    print("🎨 创建默认图标...")
    try:
        from PIL import Image, ImageDraw
        
        # 创建 64x64 的图标
        size = 64
        img = Image.new('RGBA', (size, size), color=(52, 152, 219, 255))
        draw = ImageDraw.Draw(img)
        
        # 绘制简单的 K 字母
        font_size = size // 2
        text_x = size // 4
        text_y = size // 4
        
        # 简单的几何图形代替文字
        draw.rectangle([size//4, size//4, size*3//4, size*3//4], 
                      fill=(255, 255, 255, 255), outline=(0, 0, 0, 255))
        
        img.save(icon_path, format='ICO', sizes=[(32, 32), (48, 48)])
        print(f"✅ 默认图标创建成功: {icon_path}")
        return str(icon_path)
        
    except Exception as e:
        print(f"⚠️ 创建默认图标失败: {e}")
        return None

def build_exe():
    """构建 exe 文件"""
    print("\n🔨 开始构建 exe 文件...")
    
    # 准备图标
    icon_path = create_icon()
    
    # PyInstaller 命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--windowed',                   # 无控制台窗口
        '--name=KcptunGUI_v2.0',       # 输出文件名
        '--distpath=dist',              # 输出目录
        '--workpath=build',             # 工作目录
        '--specpath=.',                 # spec文件位置
    ]
    
    # 添加图标
    if icon_path:
        cmd.extend(['--icon', icon_path])
    
    # 添加数据文件
    cmd.extend([
        '--add-data', 'src;src',                    # 源代码目录
        '--add-data', 'resources;resources',        # 资源目录
        '--add-data', 'bin;bin',                    # 二进制目录（如果存在）
    ])
    
    # 添加隐藏导入
    hidden_imports = [
        'pystray._win32',
        'PIL._tkinter_finder',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'tkinter.constants',
        'tkinter.commondialog',
        'asyncio',
        'concurrent.futures',
        'threading',
        'json',
        'pathlib',
        'datetime',
        'tempfile',
        'shutil',
        'subprocess',
        'logging',
        'logging.handlers',
        're',
        'socket',
        'urllib.parse',
        'ctypes',
        'ctypes.wintypes',
    ]
    
    for module in hidden_imports:
        cmd.extend(['--hidden-import', module])
    
    # 排除不需要的模块
    excludes = [
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'IPython',
        'jupyter',
    ]
    
    for module in excludes:
        cmd.extend(['--exclude-module', module])
    
    # 主程序文件
    cmd.append('main_v2.py')
    
    print(f"📋 PyInstaller 命令:")
    print(f"   {' '.join(cmd)}")
    
    # 执行打包
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 打包成功!")
            
            # 检查输出文件
            exe_path = Path("dist/KcptunGUI_v2.0.exe")
            if exe_path.exists():
                file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
                print(f"📦 输出文件: {exe_path}")
                print(f"📊 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ 未找到输出文件")
                return False
        else:
            print("❌ 打包失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def create_portable_package():
    """创建便携版包"""
    print("\n📦 创建便携版包...")
    
    exe_path = Path("dist/KcptunGUI_v2.0.exe")
    if not exe_path.exists():
        print("❌ exe 文件不存在，无法创建便携版")
        return False
    
    # 创建便携版目录
    portable_dir = Path("dist/KcptunGUI_v2.0_Portable")
    portable_dir.mkdir(exist_ok=True)
    
    # 复制 exe 文件
    shutil.copy2(exe_path, portable_dir / "KcptunGUI_v2.0.exe")
    
    # 创建 bin 目录（用于放置 kcptun 客户端）
    bin_dir = portable_dir / "bin"
    bin_dir.mkdir(exist_ok=True)
    
    # 创建说明文件
    readme_content = """# Kcptun GUI v2.0 便携版

## 使用说明

1. 将 kcptun 客户端程序放入 bin 目录
   - Windows: client_windows_amd64.exe
   - Linux: client_linux_amd64
   - macOS: client_darwin_amd64

2. 双击 KcptunGUI_v2.0.exe 启动程序

3. 在基本设置中配置服务器信息

4. 点击启动按钮开始连接

## 功能特性

- 图形化界面，操作简单
- 支持多配置管理
- 配置导入导出功能
- 系统托盘集成
- 实时状态监控

## 版本信息

- 版本: v2.0
- 构建时间: {build_time}
- 支持平台: Windows 10/11

## 技术支持

如有问题，请查看日志文件或联系技术支持。
"""
    
    from datetime import datetime
    readme_path = portable_dir / "README.txt"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content.format(build_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    
    # 复制示例配置
    if Path("test_config.json").exists():
        shutil.copy2("test_config.json", portable_dir / "example_config.json")
    
    print(f"✅ 便携版创建成功: {portable_dir}")
    return True

def main():
    """主函数"""
    print("🚀 Kcptun GUI v2.0 打包工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的包")
        return 1
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建 exe
    if not build_exe():
        print("\n❌ 构建失败")
        return 1
    
    # 创建便携版
    create_portable_package()
    
    print("\n🎉 打包完成!")
    print("\n📁 输出文件:")
    print("   dist/KcptunGUI_v2.0.exe - 单文件版本")
    print("   dist/KcptunGUI_v2.0_Portable/ - 便携版")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
