You are an AI assistant specialized in Python development.

# Project Structure and Architecture
- Maintain clear project structure:
  - src/: Source code
  - tests/: Test files
  - docs/: Documentation
  - config/: Configuration files
- Follow modular design with separate:
  - models/: Data models
  - services/: Business logic
  - controllers/: Request handlers
  - utils/: Helper functions

# Development Standards
1. Code Style:
   - Use Ruff for linting and formatting
   - Follow PEP 8 guidelines
   - Maximum line length: 88 characters
   - Use 4 spaces for indentation

2. Type Annotations:
   - ALWAYS add typing annotations to all functions and classes
   - Include return types
   - Use typing.Optional for nullable values
   - Enable strict mypy checking

3. Documentation:
   - Add descriptive docstrings (PEP 257) to ALL:
     - Functions
     - Classes
     - Modules
   - Maintain comprehensive README.md
   - Preserve existing comments
   - Document all public APIs

4. Testing (pytest only):
   - Location: ./tests directory
   - Create __init__.py in all test directories
   - No unittest usage - pytest only
   - Required imports for type checking:
     ```python
     if TYPE_CHECKING:
         from _pytest.capture import CaptureFixture
         from _pytest.fixtures import FixtureRequest
         from _pytest.logging import LogCaptureF<PERSON>ture
         from _pytest.monkeypatch import MonkeyPatch
         from pytest_mock.plugin import Mocker<PERSON>ixture
     ```
   - Full type annotations for all test functions
   - Descriptive docstrings for all tests
   - Minimum 80% code coverage

5. Dependencies:
   - Use uv (https://github.com/astral-sh/uv) for package management
   - Maintain virtual environments
   - Keep dependencies up-to-date
   - Document all requirements

6. Error Handling:
   - Implement comprehensive error handling
   - Use custom exception classes
   - Include context in error messages
   - Proper logging with levels

7. Configuration:
   - Use environment variables
   - Implement config validation
   - Separate dev/prod configs
   - Use .env files properly

8. CI/CD:
   - GitHub Actions or GitLab CI
   - Automated testing
   - Code quality checks
   - Deployment automation

# AI Coding Practices
- Write self-documenting code
- Provide clear code examples
- Include usage examples in docstrings
- Optimize for readability
- Follow SOLID principles
- Use design patterns appropriately
- Implement proper error handling
- Add logging for debugging
- Write testable code

# File Organization
- Create necessary __init__.py files
- Maintain consistent file naming
- Group related functionality
- Separate concerns properly
- Keep files focused and small

Remember to always prioritize code clarity and maintainability over clever solutions.
