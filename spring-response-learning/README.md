# Spring Response 学习模块

这个模块演示了Spring Boot应用程序中各种HTTP响应处理和异常处理的高级概念。本项目旨在提供一个完整的Spring响应处理学习示例，包含了从基础到高级的各种使用场景。

## 目录
- [主要功能](#主要功能)
- [项目结构](#项目结构)
- [核心特性详解](#核心特性详解)
- [配置说明](#配置说明)
- [使用示例](#使用示例)
- [开发环境要求](#开发环境要求)
- [启动说明](#启动说明)
- [注意事项](#注意事项)
- [常见问题](#常见问题)
- [最佳实践](#最佳实践)

## 主要功能

1. REST API响应处理
   - 标准HTTP状态码使用示例（200~599）
   - 自定义响应包装（ApiResponse）
   - ResponseEntity的灵活使用
   - 异步响应处理
   - 响应数据加密/解密
   - 响应数据压缩
   - 响应数据格式化

2. 视图响应处理
   - Thymeleaf模板集成
   - 表单处理和验证
   - 重定向和Flash属性
   - ModelAndView使用示例
   - 动态视图选择
   - 视图国际化支持
   - 自定义错误页面

3. 高级响应特性
   - 服务器发送事件(SSE)实时推送
   - 文件上传和下载（支持断点续传）
   - 大文件流式处理
   - 响应压缩（GZIP/Deflate）
   - ETag和条件请求
   - 异步任务处理
   - WebSocket支持
   - 响应缓存控制

4. 异常处理
   - 全局异常处理
   - REST API错误响应
   - 视图错误页面
   - 验证异常处理
   - 自定义异常处理
   - 异常日志记录
   - 开发/生产环境错误处理

## 项目结构

```
src/main/java/com/example/response/
├── Application.java                # 应用程序入口
├── controller/                     # 控制器层
│   ├── AdvancedResponseController.java  # 高级响应示例
│   ├── RestResponseController.java      # REST API示例
│   └── ViewResponseController.java      # 视图控制器示例
├── model/                         # 数据模型层
│   ├── ApiResponse.java          # 通用响应封装
│   ├── UserDTO.java             # 用户数据传输对象
│   └── enums/                   # 枚举类
│       └── ResponseStatus.java  # 响应状态枚举
├── exception/                    # 异常处理层
│   ├── BusinessException.java   # 业务异常
│   ├── ResourceNotFoundException.java  # 资源未找到异常
│   └── handler/                 # 异常处理器
│       └── CustomExceptionHandler.java
├── config/                      # 配置层
│   ├── GlobalExceptionHandler.java    # 全局异常处理
│   ├── ResponseAdvice.java           # 响应处理增强
│   ├── WebConfig.java               # Web MVC配置
│   ├── WebFluxConfig.java          # WebFlux配置
│   ├── WebSocketConfig.java        # WebSocket配置
│   └── SecurityConfig.java         # 安全配置
├── service/                     # 服务层
│   └── UserService.java        # 用户服务实现
├── util/                       # 工具类
│   ├── ResponseUtil.java      # 响应工具类
│   └── FileUtil.java         # 文件处理工具类
└── aspect/                    # 切面层
    └── ResponseAspect.java   # 响应处理切面
```

## 核心特性详解

### 1. REST API响应处理
- 支持所有标准HTTP状态码（200, 201, 204, 400, 401, 403, 404, 409, 500等）
- 统一的响应格式封装
  ```java
  public class ApiResponse<T> {
      private int code;          // 状态码
      private String message;    // 消息
      private T data;           // 数据
      private long timestamp;   // 时间戳
      // getter/setter
  }
  ```
- 支持异步响应和延迟处理
  - DeferredResult
  - CompletableFuture
  - WebFlux支持
- 条件请求处理（ETag支持）
- 响应数据加密
- 响应压缩
- 自定义响应头

### 2. 视图响应处理
- 完整的表单处理流程
  - 表单验证（前端+后端）
  - 错误消息国际化
  - 文件上传
- 数据验证和错误显示
  - Bean Validation
  - 自定义验证器
- PRG（Post-Redirect-Get）模式实现
- 灵活的视图解析
  - 动态视图选择
  - 视图命名约定
- 国际化支持
  - 多语言消息源
  - 动态语言切换

### 3. 高级响应功能
- 服务器发送事件（SSE）
  ```java
  // SSE控制器示例
  @GetMapping("/sse")
  public SseEmitter handleSSE() {
      SseEmitter emitter = new SseEmitter();
      // SSE处理逻辑
      return emitter;
  }
  ```
- 文件处理
  - 大文件上传
  - 断点续传下载
  - 文件类型检测
  - 文件预览
- 响应压缩
  - GZIP
  - Deflate
- 缓存控制
  - ETag
  - Last-Modified
  - Cache-Control

### 4. 异常处理机制
- 统一异常处理
  ```java
  @ControllerAdvice
  public class GlobalExceptionHandler {
      @ExceptionHandler(Exception.class)
      public ResponseEntity<ApiResponse> handleException(Exception e) {
          // 异常处理逻辑
      }
  }
  ```
- 异常类型
  - 业务异常
  - 系统异常
  - 验证异常
- 错误页面
  - 4xx错误页面
  - 5xx错误页面
  - 自定义错误页面

## 配置说明

### Web MVC配置
```java
@Configuration
public class WebConfig implements WebMvcConfigurer {
    // 异步请求配置
    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        configurer.setDefaultTimeout(60000);
    }
    
    // 跨域配置
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE");
    }
}
```

### WebFlux配置
```java
@Configuration
public class WebFluxConfig implements WebFluxConfigurer {
    @Override
    public void configureHttpMessageCodecs(ServerCodecConfigurer configurer) {
        configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024);
    }
}
```

### 安全配置
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeRequests()
            .antMatchers("/api/**").authenticated()
            .anyRequest().permitAll();
        return http.build();
    }
}
```

## 使用示例

### REST API示例
```java
// 1. 创建用户
POST /api/users
Content-Type: application/json

{
    "username": "test",
    "email": "<EMAIL>",
    "password": "password123"
}

// 2. 获取用户
GET /api/users/{id}

// 3. 更新用户
PUT /api/users/{id}
Content-Type: application/json

{
    "username": "updated_test"
}

// 4. 删除用户
DELETE /api/users/{id}

// 5. 条件查询
GET /api/users?page=0&size=10&sort=username,desc

// 6. 批量操作
POST /api/users/batch
Content-Type: application/json

{
    "userIds": [1, 2, 3]
}
```

### 视图示例
```
// 1. 用户管理
GET /users                 # 用户列表
GET /users/new            # 新建用户表单
GET /users/{id}           # 用户详情
GET /users/{id}/edit      # 编辑用户表单

// 2. 文件管理
GET /files                # 文件列表
GET /files/upload        # 文件上传页面
GET /files/{id}/preview  # 文件预览

// 3. 仪表板
GET /dashboard           # 系统仪表板
GET /dashboard/stats    # 统计数据
```

### 高级功能示例
```
// 1. SSE订阅
GET /api/advanced/sse
Accept: text/event-stream

// 2. WebSocket连接
WS /ws/notifications

// 3. 文件操作
POST /api/advanced/upload              # 文件上传
GET /api/advanced/download/{filename}  # 文件下载
GET /api/advanced/preview/{filename}   # 文件预览

// 4. 压缩响应
GET /api/advanced/compressed
Accept-Encoding: gzip

// 5. 异步任务
POST /api/advanced/async-task
```

## 开发环境要求
- Java 17+
- Spring Boot 3.x
- Maven 3.x
- Node.js 14+ (前端开发)
- Redis 6+ (会话存储)
- MySQL 8+ (数据存储)

## 启动说明
1. 环境准备
   ```bash
   # 克隆项目
   git clone https://github.com/your-repo/spring-response-learning.git
   cd spring-response-learning
   
   # 安装依赖
   mvn clean install
   ```

2. 配置文件
   - 复制`application.properties.example`到`application.properties`
   - 修改数据库连接等配置

3. 运行应用
   ```bash
   # 开发环境
   mvn spring-boot:run -Dspring.profiles.active=dev
   
   # 生产环境
   mvn spring-boot:run -Dspring.profiles.active=prod
   ```

4. 访问应用
   - 主页：`http://localhost:8080`
   - API文档：`http://localhost:8080/swagger-ui.html`
   - 监控页面：`http://localhost:8080/actuator`

## 注意事项
1. 文件上传
   - 默认保存路径：项目根目录的uploads文件夹
   - 最大文件大小：16MB
   - 支持的文件类型：图片、文档、压缩包

2. 性能优化
   - 异步任务超时时间：60秒
   - 静态资源缓存时间：365天
   - 响应压缩阈值：2KB

3. 安全配置
   - CORS策略
   - CSRF保护
   - XSS防护
   - SQL注入防护

4. 监控告警
   - 接口响应时间监控
   - 异常监控
   - 资源使用监控

## 常见问题

### 1. 上传文件失败
- 检查文件大小是否超过限制
- 确认上传目录权限
- 验证文件类型是否支持

### 2. 异步请求超时
- 检查超时配置
- 确认任务执行时间
- 查看线程池状态

### 3. 缓存问题
- 清除浏览器缓存
- 检查Cache-Control配置
- 验证ETag实现

## 最佳实践

### 1. 响应处理
- 使用统一的响应格式
- 合理使用HTTP状态码
- 实现幂等性接口
- 添加适当的缓存控制

### 2. 异常处理
- 区分业务异常和系统异常
- 统一异常处理格式
- 记录详细错误日志
- 返回友好错误信息

### 3. 性能优化
- 使用异步处理长任务
- 实现响应压缩
- 合理使用缓存
- 优化数据库查询

### 4. 安全性
- 实现请求认证
- 添加访问控制
- 防止XSS攻击
- 使用HTTPS 