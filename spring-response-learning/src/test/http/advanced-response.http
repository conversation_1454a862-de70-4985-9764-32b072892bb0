### 1. 测试异步响应
GET http://localhost:8080/api/advanced/async/deferred

### 2. 测试响应流 (SSE)
GET http://localhost:8080/api/advanced/stream
Accept: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

### 3. 测试条件响应（ETag支持）
# 第一次请求
GET http://localhost:8080/api/advanced/conditional

### 使用ETag的条件请求
GET http://localhost:8080/api/advanced/conditional
If-None-Match: "上一次响应返回的ETag"

### 4. 测试文件上传
POST http://localhost:8080/api/advanced/upload
Content-Type: multipart/form-data; boundary=WebAppBoundary

--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="test.txt"
Content-Type: text/plain

< ./test.txt
--WebAppBoundary--

### 5. 测试文件下载
GET http://localhost:8080/api/advanced/download/test.txt

### 6. 测试大文件流式下载
GET http://localhost:8080/api/advanced/stream-download/test.txt

### 7. 测试压缩响应
GET http://localhost:8080/api/advanced/compressed
Accept-Encoding: gzip

### 8. 测试异步任务
POST http://localhost:8080/api/advanced/async-task 