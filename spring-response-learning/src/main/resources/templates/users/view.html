<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>View User</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div th:if="${message}" class="alert alert-success" th:text="${message}">
            Success message
        </div>
        
        <div class="card">
            <div class="card-header">
                <h2>User Details</h2>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-3 fw-bold">ID:</div>
                    <div class="col-md-9" th:text="${user.id}">1</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3 fw-bold">Username:</div>
                    <div class="col-md-9" th:text="${user.username}">John</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3 fw-bold">Email:</div>
                    <div class="col-md-9" th:text="${user.email}"><EMAIL></div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3 fw-bold">Bio:</div>
                    <div class="col-md-9" th:text="${user.bio}">User bio</div>
                </div>
            </div>
            <div class="card-footer">
                <a th:href="@{/users/{id}/edit(id=${user.id})}" class="btn btn-warning">Edit</a>
                <a href="/users" class="btn btn-secondary">Back to List</a>
            </div>
        </div>
    </div>
</body>
</html> 