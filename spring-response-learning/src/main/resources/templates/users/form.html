<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title th:text="${user.id} ? 'Edit User' : 'New User'">User Form</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2 th:text="${user.id} ? 'Edit User' : 'New User'">User Form</h2>
        <form th:action="${user.id} ? @{/users/{id}(id=${user.id})} : @{/users/new}" 
              th:object="${user}" method="post" class="needs-validation" novalidate>
            
            <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <input type="text" class="form-control" id="username" th:field="*{username}"
                       th:classappend="${#fields.hasErrors('username')} ? 'is-invalid'">
                <div class="invalid-feedback" th:if="${#fields.hasErrors('username')}"
                     th:errors="*{username}">Username error</div>
            </div>

            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" th:field="*{email}"
                       th:classappend="${#fields.hasErrors('email')} ? 'is-invalid'">
                <div class="invalid-feedback" th:if="${#fields.hasErrors('email')}"
                     th:errors="*{email}">Email error</div>
            </div>

            <div class="mb-3">
                <label for="bio" class="form-label">Bio</label>
                <textarea class="form-control" id="bio" th:field="*{bio}" rows="3"
                          th:classappend="${#fields.hasErrors('bio')} ? 'is-invalid'"></textarea>
                <div class="invalid-feedback" th:if="${#fields.hasErrors('bio')}"
                     th:errors="*{bio}">Bio error</div>
            </div>

            <button type="submit" class="btn btn-primary">Save</button>
            <a href="/users" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
</body>
</html> 