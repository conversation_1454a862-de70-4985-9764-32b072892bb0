<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Users</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Users</h2>
        <div th:if="${message}" class="alert alert-success" th:text="${message}">
            Success message
        </div>
        <a href="/users/new" class="btn btn-primary mb-3">Add New User</a>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="user : ${users}">
                    <td th:text="${user.id}">1</td>
                    <td th:text="${user.username}">John</td>
                    <td th:text="${user.email}"><EMAIL></td>
                    <td>
                        <a th:href="@{/users/{id}(id=${user.id})}" class="btn btn-info btn-sm">View</a>
                        <a th:href="@{/users/{id}/edit(id=${user.id})}" class="btn btn-warning btn-sm">Edit</a>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</body>
</html> 