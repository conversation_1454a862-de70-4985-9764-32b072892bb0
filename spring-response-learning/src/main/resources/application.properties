# Bean definition overriding
spring.main.allow-bean-definition-overriding=true

# Server configuration
server.port=8080

# Logging configuration
logging.level.root=INFO
logging.level.com.example.response=DEBUG
logging.level.org.springframework.web=DEBUG

# File upload configuration
spring.servlet.multipart.max-file-size=-1
spring.servlet.multipart.max-request-size=-1
spring.servlet.multipart.enabled=true
spring.servlet.multipart.location=${java.io.tmpdir}

# Static resource configuration
spring.web.resources.static-locations=classpath:/static/,file:uploads/
spring.web.resources.cache.period=365d

# MIME type mappings
spring.mvc.contentnegotiation.media-types.woff2=font/woff2

# Character encoding
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# Tomcat configuration
server.tomcat.max-swallow-size=-1
server.tomcat.max-http-form-post-size=-1

# CORS configuration
spring.web.cors.allowed-origins=*
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.max-age=3600

# 禁用默认的favicon处理
spring.mvc.favicon.enabled=false

# 启用响应压缩
server.compression.enabled=true
# 压缩的MIME类型
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain
# 启用压缩的最小响应大小
server.compression.min-response-size=1024
# 压缩级别 1-9，值越大压缩率越高，但消耗CPU也越多
server.compression.level=6 