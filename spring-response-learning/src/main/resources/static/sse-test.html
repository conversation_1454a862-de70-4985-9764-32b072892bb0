<!DOCTYPE html>
<html>
<head>
    <title>SSE Test</title>
    <style>
        #messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            background-color: #f0f0f0;
        }
    </style>
</head>
<body>
    <h1>Server-Sent Events Test</h1>
    <div id="messages"></div>
    <button onclick="startSSE()">Start SSE</button>
    <button onclick="stopSSE()">Stop SSE</button>

    <script>
        let eventSource;

        function startSSE() {
            if (eventSource) {
                eventSource.close();
            }

            eventSource = new EventSource('/api/advanced/stream');
            
            eventSource.onmessage = function(event) {
                const messagesDiv = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.textContent = event.data;
                messagesDiv.appendChild(messageDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            };

            eventSource.onerror = function(error) {
                console.error('SSE Error:', error);
                const messagesDiv = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.style.color = 'red';
                messageDiv.textContent = 'Error: Connection lost';
                messagesDiv.appendChild(messageDiv);
            };

            eventSource.onopen = function() {
                const messagesDiv = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.style.color = 'green';
                messageDiv.textContent = 'Connected to server';
                messagesDiv.appendChild(messageDiv);
            };
        }

        function stopSSE() {
            if (eventSource) {
                eventSource.close();
                const messagesDiv = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.style.color = 'blue';
                messageDiv.textContent = 'Connection closed';
                messagesDiv.appendChild(messageDiv);
            }
        }
    </script>
</body>
</html> 