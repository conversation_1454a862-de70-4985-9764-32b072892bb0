<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Compression & Async Task Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .card {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .response {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .stats {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>API Testing Interface</h1>

    <!-- Compressed Response Test -->
    <div class="card">
        <h2>1. Compressed Response Test</h2>
        <button onclick="testAutoCompressedResponse()">Test Auto Compression</button>
        <button onclick="testManualCompressedResponse()">Test Manual Compression</button>
        <div class="response" id="compressedResponse"></div>
        <div class="stats" id="compressedStats"></div>
    </div>

    <!-- Async Task Test -->
    <div class="card">
        <h2>2. Async Task Test</h2>
        <button onclick="testAsyncTask()">Test Async Task</button>
        <div class="response" id="asyncResponse"></div>
        <div class="stats" id="asyncStats"></div>
    </div>

    <script>
        // Utility function to display error details
        function displayError(element, error, details = '') {
            element.innerHTML = `
                <div class="error">
                    <strong>Error:</strong> ${error.message}
                    ${details ? `<br><br><strong>Details:</strong><br>${details}` : ''}
                </div>`;
        }

        // Test Auto Compressed Response
        async function testAutoCompressedResponse() {
            const startTime = performance.now();
            const responseDiv = document.getElementById('compressedResponse');
            const statsDiv = document.getElementById('compressedStats');
            
            responseDiv.innerHTML = '<pre>Loading auto compression test...</pre>';
            statsDiv.innerHTML = '';
            
            try {
                console.log('Starting auto compressed response test...');
                const response = await fetch('/api/advanced/compressed/auto', {
                    headers: {
                        'Accept-Encoding': 'gzip',
                        'Accept': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const endTime = performance.now();
                const data = await response.json();
                
                // Display response
                responseDiv.innerHTML = `<pre>Auto Compression Result:\n${JSON.stringify(data, null, 2)}</pre>`;
                
                // Display stats
                const headers = Array.from(response.headers.entries())
                    .map(([key, value]) => `${key}: ${value}`)
                    .join('\n');
                
                statsDiv.innerHTML = `
                    <pre>
Auto Compression Stats:
Response Time: ${(endTime - startTime).toFixed(2)}ms
Status: ${response.status}
Headers:
${headers}
                    </pre>`;
                
                console.log('Auto compressed response test completed successfully');
            } catch (error) {
                console.error('Auto compressed response test failed:', error);
                const details = `
                    Request URL: ${window.location.origin}/api/advanced/compressed/auto
                    Browser: ${navigator.userAgent}
                    Time: ${new Date().toISOString()}
                `;
                displayError(responseDiv, error, details);
            }
        }

        // Test Manual Compressed Response
        async function testManualCompressedResponse() {
            const startTime = performance.now();
            const responseDiv = document.getElementById('compressedResponse');
            const statsDiv = document.getElementById('compressedStats');
            
            responseDiv.innerHTML = '<pre>Loading manual compression test...</pre>';
            statsDiv.innerHTML = '';
            
            try {
                console.log('Starting manual compressed response test...');
                const response = await fetch('/api/advanced/compressed/manual', {
                    headers: {
                        'Accept-Encoding': 'gzip',
                        'Accept': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const endTime = performance.now();
                const data = await response.json();
                
                // Display response
                responseDiv.innerHTML = `<pre>Manual Compression Result:\n${JSON.stringify(data, null, 2)}</pre>`;
                
                // Display stats with compression info
                const originalSize = response.headers.get('X-Original-Size');
                const compressedSize = response.headers.get('X-Compressed-Size');
                const compressionRatio = originalSize && compressedSize 
                    ? ((1 - (compressedSize / originalSize)) * 100).toFixed(2)
                    : 'N/A';
                
                const headers = Array.from(response.headers.entries())
                    .map(([key, value]) => `${key}: ${value}`)
                    .join('\n');
                
                statsDiv.innerHTML = `
                    <pre>
Manual Compression Stats:
Response Time: ${(endTime - startTime).toFixed(2)}ms
Status: ${response.status}
Original Size: ${originalSize ? formatBytes(originalSize) : 'N/A'}
Compressed Size: ${compressedSize ? formatBytes(compressedSize) : 'N/A'}
Compression Ratio: ${compressionRatio}%
Headers:
${headers}
                    </pre>`;
                
                console.log('Manual compressed response test completed successfully');
            } catch (error) {
                console.error('Manual compressed response test failed:', error);
                const details = `
                    Request URL: ${window.location.origin}/api/advanced/compressed/manual
                    Browser: ${navigator.userAgent}
                    Time: ${new Date().toISOString()}
                `;
                displayError(responseDiv, error, details);
            }
        }

        // Format bytes to human readable format
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Test Async Task
        async function testAsyncTask() {
            const startTime = performance.now();
            const responseDiv = document.getElementById('asyncResponse');
            const statsDiv = document.getElementById('asyncStats');
            
            responseDiv.innerHTML = '<pre>Task started...</pre>';
            statsDiv.innerHTML = '';
            
            try {
                console.log('Starting async task test...');
                const response = await fetch('/api/advanced/async-task', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const endTime = performance.now();
                const data = await response.json();
                
                // Display response
                responseDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                // Display stats
                statsDiv.innerHTML = `
                    <pre>
Total Time: ${(endTime - startTime).toFixed(2)}ms
Status: ${response.status}
Response Headers:
${Array.from(response.headers.entries())
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n')}
                    </pre>`;
                
                console.log('Async task test completed successfully');
            } catch (error) {
                console.error('Async task test failed:', error);
                const details = `
                    Request URL: ${window.location.origin}/api/advanced/async-task
                    Browser: ${navigator.userAgent}
                    Time: ${new Date().toISOString()}
                `;
                displayError(responseDiv, error, details);
            }
        }

        // Log initial page load
        console.log('Test page loaded at:', new Date().toISOString());
        console.log('Base URL:', window.location.origin);
    </script>
</body>
</html> 