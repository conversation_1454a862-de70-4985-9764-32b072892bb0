<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理器</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/file-manager.css" rel="stylesheet">
    <!-- 引入视频播放器相关CSS -->
    <link href="https://vjs.zencdn.net/7.20.3/video-js.css" rel="stylesheet" />
    <link href="https://cdn.plyr.io/3.7.8/plyr.css" rel="stylesheet" />
    <!-- 引入 PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';</script>
    <!-- 在 head 中添加 PDF.js Viewer 的样式 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf_viewer.min.css">
</head>
<body class="app-container">
    <!-- 侧边栏 -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <i class="bi bi-folder2"></i>
            <h1>文件管理器</h1>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li class="active" data-filter="all">
                    <i class="bi bi-grid"></i>
                    全部文件
                </li>
                <li data-filter="image">
                    <i class="bi bi-image"></i>
                    图片
                </li>
                <li data-filter="document">
                    <i class="bi bi-file-text"></i>
                    文档
                </li>
                <li data-filter="video">
                    <i class="bi bi-camera-video"></i>
                    视频
                </li>
                <li data-filter="audio">
                    <i class="bi bi-music-note"></i>
                    音频
                </li>
            </ul>
        </nav>
        <div class="storage-info">
            <div class="storage-text">
                <span class="storage-used">计算中...</span>
            </div>
            <div class="progress">
                <div class="progress-bar" style="width: 0%"></div>
            </div>
            <div class="storage-details">
                <span>计算中...</span>
            </div>
        </div>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
        <!-- 顶部工具栏 -->
        <header class="toolbar">
            <div class="search-box">
                <i class="bi bi-search"></i>
                <input type="text" placeholder="搜索文件...">
            </div>
            <div class="toolbar-actions">
                <button class="btn btn-icon" title="网格视图">
                    <i class="bi bi-grid-3x3-gap"></i>
                </button>
                <button class="btn btn-icon" title="列表视图">
                    <i class="bi bi-list"></i>
                </button>
                <button class="btn btn-icon" title="排序">
                    <i class="bi bi-sort-down"></i>
                </button>
            </div>
        </header>

        <!-- 上传区域 -->
        <div class="upload-zone" id="dropZone">
            <div class="upload-content">
                <input type="file" id="fileInput" class="file-input" multiple accept="image/*,application/pdf,.doc,.docx,video/*,audio/*" />
                <div class="upload-icon">
                    <i class="bi bi-cloud-arrow-up"></i>
                </div>
                <h3>拖放文件到这里</h3>
                <p>或者点击选择多个文件</p>
                <div class="upload-types">支持的格式: JPG, PNG, PDF, DOC...</div>
            </div>
        </div>

        <!-- 文件队列 -->
        <div id="fileQueue" class="file-queue" style="display: none;">
            <div class="file-queue-header">
                <h4>待上传文件</h4>
                <div class="file-queue-actions">
                    <button class="btn btn-primary btn-sm" onclick="fileManager.uploadAll()">
                        <i class="bi bi-cloud-upload"></i> 全部上传
                    </button>
                    <button class="btn btn-text btn-sm" onclick="fileManager.clearQueue()">
                        <i class="bi bi-x-circle"></i> 清空队列
                    </button>
                </div>
            </div>
            <div class="file-queue-list"></div>
        </div>

        <!-- 已选择文件信息 -->
        <div id="selectedFile" class="selected-file" style="display: none;">
            <div class="selected-file-info">
                <i class="bi bi-file-earmark"></i>
                <div class="selected-file-details">
                    <span id="fileName"></span>
                    <span id="fileSize" class="text-muted"></span>
                </div>
            </div>
        </div>

        <!-- 上传进度 -->
        <div id="uploadProgress" class="upload-progress" style="display: none;">
            <div class="upload-progress-header">
                <h4>正在上传...</h4>
                <button class="btn-close"></button>
            </div>
            <div class="upload-progress-content">
                <div class="progress">
                    <div class="progress-bar" role="progressbar"></div>
                </div>
                <div class="upload-progress-info">
                    <div class="upload-progress-text">0%</div>
                    <div class="upload-progress-details">
                        <span class="upload-speed">0 KB/s</span>
                        <span class="upload-remaining">剩余时间: 计算中...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件列表 -->
        <div class="files-container">
            <div class="files-header">
                <div class="files-header-left">
                    <h2>最近上传</h2>
                    <div class="file-selection-group">
                        <label class="select-all-checkbox">
                            <input type="checkbox" id="selectAllCheckbox" onchange="fileManager.toggleSelectAll(this.checked)">
                            <span class="checkbox-text">全选</span>
                        </label>
                    </div>
                </div>
                <div class="files-header-right">
                    <div class="batch-actions" style="display: none;">
                        <span class="selected-count"></span>
                        <button class="btn btn-primary btn-sm" onclick="fileManager.downloadSelected()">
                            <i class="bi bi-download"></i> 下载所选
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="fileManager.deleteSelected()">
                            <i class="bi bi-trash"></i> 删除所选
                        </button>
                    </div>
                    <div class="files-actions">
                        <button class="btn btn-icon" onclick="fileManager.refreshList()" title="刷新">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="files-grid" id="fileList">
                <!-- 文件列表将通过JavaScript动态加载 -->
            </div>
        </div>
    </main>

    <!-- 预览模态框 -->
    <div class="modal fade preview-modal" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">文件预览</h5>
                    <div class="preview-actions">
                        <button type="button" class="btn btn-primary" onclick="fileManager.downloadCurrent()">
                            <i class="bi bi-download"></i> 下载
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-lg"></i> 关闭
                        </button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="image-preview-container">
                        <img id="imagePreview" class="preview-item" hidden>
                        <div class="image-preview-toolbar">
                            <button onclick="fileManager.zoomIn()"><i class="bi bi-zoom-in"></i></button>
                            <button onclick="fileManager.zoomOut()"><i class="bi bi-zoom-out"></i></button>
                            <button onclick="fileManager.resetZoom()"><i class="bi bi-arrows-angle-contract"></i></button>
                        </div>
                    </div>
                    
                    <!-- PDF预览 -->
                    <div id="pdfPreviewContainer" class="pdf-preview-container preview-item" hidden>
                        <!-- 工具栏 -->
                        <div class="pdf-toolbar">
                            <div class="pdf-nav">
                                <button id="pdfPrev" title="上一页"><i class="bi bi-chevron-left"></i></button>
                                <div class="pdf-page-info">
                                    <span>第</span>
                                    <input type="number" id="pdfCurrentPage" min="1" value="1">
                                    <span>页 / <span id="pdfTotalPages">0</span> 页</span>
                                </div>
                                <button id="pdfNext" title="下一页"><i class="bi bi-chevron-right"></i></button>
                            </div>
                            <div class="pdf-zoom">
                                <button id="pdfZoomOut" title="缩小"><i class="bi bi-zoom-out"></i></button>
                                <span id="pdfZoomLevel">100%</span>
                                <button id="pdfZoomIn" title="放大"><i class="bi bi-zoom-in"></i></button>
                            </div>
                        </div>
                        
                        <!-- 主内容区 -->
                        <div class="pdf-main">
                            <!-- 左侧缩略图 -->
                            <div class="pdf-sidebar">
                                <div id="pdfThumbnails" class="pdf-thumbnails"></div>
                            </div>
                            
                            <!-- PDF内容 -->
                            <div class="pdf-content">
                                <canvas id="pdfCanvas"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 视频预览 -->
                    <div class="video-preview-container preview-item" hidden>
                        <!-- 播放器切换按钮组 -->
                        <div class="player-switcher">
                            <button type="button" class="active" data-player="native">原生播放器</button>
                            <button type="button" data-player="videojs">Video.js</button>
                            <button type="button" data-player="plyr">Plyr</button>
                        </div>
                        
                        <!-- 原生播放器 -->
                        <video id="videoPreview" class="video-player" controls>
                            <source src="" type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                        
                        <!-- Video.js播放器 -->
                        <div class="video-js-wrapper">
                            <video id="videoJsPlayer" class="video-js vjs-big-play-centered">
                                <source src="" type="video/mp4">
                            </video>
                        </div>
                        
                        <!-- Plyr播放器 -->
                        <div class="plyr-wrapper">
                            <video id="plyrPlayer" class="plyr-player">
                                <source src="" type="video/mp4">
                            </video>
                        </div>
                    </div>
                    
                    <!-- 音频预览 -->
                    <audio id="audioPreview" class="preview-item" controls hidden></audio>
                    
                    <!-- 文本预览 -->
                    <div id="textPreview" class="preview-item" hidden></div>
                    
                    <!-- 不支持的文件类型 -->
                    <div id="unsupportedPreview" class="preview-item" hidden>
                        <i class="bi bi-exclamation-triangle"></i>
                        <p>暂不支持该文件类型的预览</p>
                        <p class="hint-text">您可以点击"下载"按钮后查看文件内容</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="delete-confirmation">
                        <i class="bi bi-exclamation-triangle"></i>
                        <p>确定要删除文件 <strong id="deleteFileName"></strong> 吗？</p>
                        <p class="text-muted">此操作无法撤销</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-text" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="fileManager.confirmDelete()">
                        <i class="bi bi-trash"></i>
                        删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast消息 -->
    <div class="toast-container"></div>

    <!-- 引入 Bootstrap JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="js/file-manager.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化所有模态框
            const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            
            window.fileManager = new FileManager({
                api: {
                    upload: '/api/advanced/upload',
                    download: '/api/advanced/download',
                    streamDownload: '/api/advanced/stream-download',
                    list: '/api/advanced/files',
                    delete: '/api/advanced/files'
                },
                modals: {
                    preview: previewModal,
                    delete: deleteModal
                }
            });
        });
    </script>

    <!-- 引入视频播放器相关JS -->
    <script src="https://vjs.zencdn.net/7.20.3/video.min.js"></script>
    <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>
</body>
</html> 