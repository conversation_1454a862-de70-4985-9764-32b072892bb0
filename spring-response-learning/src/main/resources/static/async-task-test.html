<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>异步任务测试</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .card {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .task-list {
            margin-top: 20px;
        }
        .task-item {
            padding: 10px;
            border: 1px solid #ddd;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .task-item.completed {
            background-color: #e8f5e9;
        }
        .task-item.failed {
            background-color: #ffebee;
        }
        .status {
            font-weight: bold;
        }
        .status.pending {
            color: #f57c00;
        }
        .status.completed {
            color: #4caf50;
        }
        .status.failed {
            color: #d32f2f;
        }
        .time {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>异步任务测试</h1>
        <p>这个示例展示了如何处理长时间运行的任务，而不阻塞用户界面。</p>
        <button onclick="startNewTask()" id="startButton">启动新任务</button>
        <button onclick="startMultipleTasks()" id="multipleButton">启动多个任务</button>
        <button onclick="clearTasks()">清除任务列表</button>
    </div>

    <div class="card task-list">
        <h2>任务列表</h2>
        <div id="taskList"></div>
    </div>

    <script>
        let taskCounter = 0;
        
        // 启动单个任务
        async function startNewTask() {
            const taskId = ++taskCounter;
            const startTime = new Date();
            addTaskToList(taskId, startTime);
            
            try {
                const response = await fetch('/api/advanced/async-task', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误！状态码：${response.status}`);
                }
                
                const result = await response.json();
                updateTaskStatus(taskId, 'completed', result.data);
            } catch (error) {
                console.error('任务执行失败:', error);
                updateTaskStatus(taskId, 'failed', error.message);
            }
        }
        
        // 启动多个任务
        async function startMultipleTasks() {
            const taskCount = 5;
            document.getElementById('multipleButton').disabled = true;
            
            try {
                const promises = Array.from({ length: taskCount }, () => startNewTask());
                await Promise.all(promises);
            } finally {
                document.getElementById('multipleButton').disabled = false;
            }
        }
        
        // 添加任务到列表
        function addTaskToList(taskId, startTime) {
            const taskList = document.getElementById('taskList');
            const taskElement = document.createElement('div');
            taskElement.id = `task-${taskId}`;
            taskElement.className = 'task-item';
            taskElement.innerHTML = `
                <div>
                    <span>任务 #${taskId}</span>
                    <span class="time">开始时间: ${formatTime(startTime)}</span>
                </div>
                <span class="status pending">处理中...</span>
            `;
            taskList.insertBefore(taskElement, taskList.firstChild);
        }
        
        // 更新任务状态
        function updateTaskStatus(taskId, status, message) {
            const taskElement = document.getElementById(`task-${taskId}`);
            if (taskElement) {
                taskElement.className = `task-item ${status}`;
                const statusText = status === 'completed' ? '已完成' : '失败';
                const statusClass = status === 'completed' ? 'completed' : 'failed';
                const endTime = new Date();
                
                taskElement.innerHTML = `
                    <div>
                        <span>任务 #${taskId}</span>
                        <span class="time">完成时间: ${formatTime(endTime)}</span>
                    </div>
                    <span class="status ${statusClass}">${statusText}: ${message}</span>
                `;
            }
        }
        
        // 清除任务列表
        function clearTasks() {
            document.getElementById('taskList').innerHTML = '';
            taskCounter = 0;
        }
        
        // 格式化时间
        function formatTime(date) {
            return date.toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit',
                fractionalSecondDigits: 3 
            });
        }
    </script>
</body>
</html> 