<!DOCTYPE html>
<html>
<head>
    <title>ETag Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .log {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            background-color: #fff;
            max-height: 800px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.4;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .entry {
            margin-bottom: 15px;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .entry-header {
            padding: 8px 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .entry-header:hover {
            background-color: #f0f0f0;
        }
        .entry-content {
            display: flex;
            transition: height 0.3s ease;
        }
        .entry-content.collapsed {
            display: none;
        }
        .request-panel, .response-panel {
            width: 600px;
            padding: 15px;
            overflow: hidden;
        }
        .request-panel {
            border-right: 1px solid #eee;
            background-color: #fafbfc;
        }
        .response-panel {
            background-color: #fcfcfc;
        }
        .headers, .data {
            margin: 10px 0;
        }
        .header-item {
            margin: 3px 0;
            padding-left: 15px;
            color: #555;
        }
        .json-data {
            padding-left: 15px;
            color: #555;
            white-space: pre-wrap;
        }
        .timestamp {
            color: #888;
            font-size: 0.9em;
        }
        .status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .status-200 {
            background-color: #e3fcef;
            color: #28a745;
        }
        .status-304 {
            background-color: #e3f2fd;
            color: #0066cc;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        button:hover {
            opacity: 0.9;
        }
        button:first-child {
            background-color: #0066cc;
            color: white;
        }
        button:nth-child(2) {
            background-color: #28a745;
            color: white;
        }
        button:last-child {
            background-color: #dc3545;
            color: white;
        }
        .json-viewer {
            font-family: 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .json-line {
            font-family: Consolas, monospace;
            font-size: 14px;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .json-toggle {
            cursor: pointer;
            color: rgb(110, 110, 110);
            display: inline-block;
            width: 12px;
            user-select: none;
        }
        .json-preview {
            display: inline;
            color: rgb(110, 110, 110);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .preview-content {
            display: inline;
        }
        .json-content {
            display: none;
        }
        .json-key {
            color: rgb(136, 19, 145);
        }
        .json-string {
            color: rgb(196, 26, 22);
        }
        .json-number {
            color: rgb(28, 0, 207);
        }
        .json-boolean {
            color: rgb(13, 34, 170);
        }
        .json-null {
            color: rgb(13, 34, 170);
        }
        .json-mark {
            color: rgb(110, 110, 110);
        }
        .section-title {
            font-weight: bold;
            color: #666;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1>ETag Testing</h1>
    <div>
        <button onclick="makeRequest(false)">Make Normal Request</button>
        <button onclick="makeRequest(true)">Make Conditional Request</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    <div id="log" class="log"></div>

    <script>
        let lastEtag = null;
        let requestCounter = 0;

        function formatJSONValue(value) {
            if (value === null) return '<span class="json-null">null</span>';
            if (typeof value === 'string') return `<span class="json-string">"${value}"</span>`;
            if (typeof value === 'number') return `<span class="json-number">${value}</span>`;
            if (typeof value === 'boolean') return `<span class="json-boolean">${value}</span>`;
            return value;
        }

        function createArrayPreview(arr) {
            const preview = arr.slice(0, 3).map(item => formatJSONValue(item));
            return `[${preview.join(', ')}${arr.length > 3 ? ', …' : ''}]`;
        }

        function createObjectPreview(obj) {
            const entries = Object.entries(obj).slice(0, 3);
            const preview = entries.map(([k, v]) => {
                if (Array.isArray(v)) {
                    return `${k}: ${createArrayPreview(v)}`;
                }
                return `${k}: ${JSON.stringify(v)}`;
            }).join(', ');
            return `{${preview}${Object.keys(obj).length > 3 ? ', …' : ''}}`;
        }

        function createObjectViewer(obj, level = 0, key = '') {
            if (typeof obj !== 'object' || obj === null) {
                return formatJSONValue(obj);
            }

            let content = '';
            const indent = '  '.repeat(level);
            
            if (Array.isArray(obj)) {
                content = `<div class="json-line">
                    ${indent}<span class="json-toggle" onclick="toggleJSON(this)">▶</span>
                    ${key ? `<span class="json-key">${key}</span><span class="json-mark">: </span>` : ''}
                    <span class="json-preview"><span class="preview-content">${createArrayPreview(obj)}</span></span>
                    <div class="json-content" style="display:none">`;
                
                obj.forEach((item, index) => {
                    content += `<div class="json-line">
                        ${indent}  ${index}: ${formatJSONValue(item)}
                    </div>`;
                });
                content += '</div></div>';
            } else {
                content = `<div class="json-line">
                    ${level > 0 ? indent : ''}<span class="json-toggle" onclick="toggleJSON(this)">▶</span>
                    ${key ? `<span class="json-key">${key}</span><span class="json-mark">: </span>` : ''}
                    <span class="json-preview"><span class="preview-content">${createObjectPreview(obj)}</span></span>
                    <div class="json-content" style="display:none">`;
                
                Object.entries(obj).forEach(([propKey, value]) => {
                    if (typeof value === 'object' && value !== null) {
                        content += createObjectViewer(value, level + 1, propKey);
                    } else {
                        content += `<div class="json-line">
                            ${indent}  <span class="json-key">${propKey}</span><span class="json-mark">: </span>${formatJSONValue(value)}
                        </div>`;
                    }
                });
                content += '</div></div>';
            }
            
            return content;
        }

        function toggleJSON(element) {
            const content = element.parentElement.querySelector('.json-content');
            const preview = element.parentElement.querySelector('.json-preview');
            const isExpanded = content.style.display === 'block';
            
            content.style.display = isExpanded ? 'none' : 'block';
            preview.style.display = isExpanded ? 'inline' : 'none';
            element.textContent = isExpanded ? '▶' : '▼';
        }

        function createRequestResponse(requestData, responseData) {
            const id = ++requestCounter;
            const entry = document.createElement('div');
            entry.className = 'entry';
            
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = responseData.status === 304 ? 'status-304' : 'status-200';
            
            let jsonContent = '';
            if (responseData.data) {
                const jsonData = JSON.parse(responseData.data);
                jsonContent = `
                    <div class="section-title">Response Data:</div>
                    <div class="json-viewer">
                        ${createObjectViewer(jsonData)}
                    </div>
                `;
            }
            
            entry.innerHTML = `
                <div class="entry-header" onclick="toggleEntry(${id})">
                    <span>
                        <span class="timestamp">${timestamp}</span>
                        ${requestData.conditional ? 'Conditional' : 'Normal'} Request
                    </span>
                    <span class="status ${statusClass}">
                        ${responseData.status} ${responseData.statusText}
                    </span>
                </div>
                <div id="content-${id}" class="entry-content">
                    <div class="request-panel">
                        <div class="section-title">Request Headers:</div>
                        <div class="headers">
                            ${Array.from(requestData.headers).map(([key, value]) => 
                                `<div class="header-item">${key}: ${value}</div>`
                            ).join('')}
                        </div>
                    </div>
                    <div class="response-panel">
                        <div class="section-title">Response Headers:</div>
                        <div class="headers">
                            ${Array.from(responseData.headers).map(([key, value]) => 
                                `<div class="header-item">${key}: ${value}</div>`
                            ).join('')}
                        </div>
                        ${jsonContent}
                    </div>
                </div>
            `;
            
            return entry;
        }

        function toggleEntry(id) {
            const content = document.querySelector(`#content-${id}`);
            content.classList.toggle('collapsed');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            lastEtag = null;
            requestCounter = 0;
        }

        async function makeRequest(conditional) {
            const headers = new Headers();
            if (conditional && lastEtag) {
                headers.append('If-None-Match', lastEtag);
            }
            
            const requestData = {
                conditional,
                headers
            };

            try {
                const response = await fetch('/api/advanced/conditional', {
                    headers: headers
                });

                const responseData = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: response.headers
                };

                // 更新ETag
                const etag = response.headers.get('ETag');
                if (etag) {
                    lastEtag = etag;
                }

                // 如果不是304，获取响应数据
                if (response.status !== 304) {
                    const data = await response.json();
                    responseData.data = JSON.stringify(data);
                }

                const logDiv = document.getElementById('log');
                const entry = createRequestResponse(requestData, responseData);
                logDiv.insertBefore(entry, logDiv.firstChild);
            } catch (error) {
                console.error('Error:', error);
            }
        }

        // 更新样式
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            body {
                font-family: Arial, sans-serif;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .log {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 15px;
                margin: 10px 0;
                background-color: #fff;
                max-height: 800px;
                overflow-y: auto;
                font-family: 'Consolas', monospace;
                font-size: 13px;
                line-height: 1.4;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .entry {
                margin-bottom: 15px;
                border: 1px solid #eee;
                border-radius: 4px;
            }
            .entry-header {
                padding: 8px 15px;
                background-color: #f8f9fa;
                border-bottom: 1px solid #eee;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .entry-header:hover {
                background-color: #f0f0f0;
            }
            .entry-content {
                display: flex;
                transition: height 0.3s ease;
            }
            .entry-content.collapsed {
                display: none;
            }
            .request-panel, .response-panel {
                width: 600px;
                padding: 15px;
                overflow: hidden;
            }
            .request-panel {
                border-right: 1px solid #eee;
                background-color: #fafbfc;
            }
            .response-panel {
                background-color: #fcfcfc;
            }
            .headers, .data {
                margin: 10px 0;
            }
            .header-item {
                margin: 3px 0;
                padding-left: 15px;
                color: #555;
            }
            .json-data {
                padding-left: 15px;
                color: #555;
                white-space: pre-wrap;
            }
            .timestamp {
                color: #888;
                font-size: 0.9em;
            }
            .status {
                padding: 2px 8px;
                border-radius: 3px;
                font-size: 0.9em;
                font-weight: bold;
            }
            .status-200 {
                background-color: #e3fcef;
                color: #28a745;
            }
            .status-304 {
                background-color: #e3f2fd;
                color: #0066cc;
            }
            button {
                padding: 10px 20px;
                margin: 5px;
                cursor: pointer;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                transition: background-color 0.2s;
            }
            button:hover {
                opacity: 0.9;
            }
            button:first-child {
                background-color: #0066cc;
                color: white;
            }
            button:nth-child(2) {
                background-color: #28a745;
                color: white;
            }
            button:last-child {
                background-color: #dc3545;
                color: white;
            }
            .json-viewer {
                font-family: 'Consolas', monospace;
                font-size: 13px;
                line-height: 1.4;
            }
            .json-line {
                font-family: Consolas, monospace;
                font-size: 14px;
                line-height: 1.2;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .json-toggle {
                cursor: pointer;
                color: rgb(110, 110, 110);
                display: inline-block;
                width: 12px;
                user-select: none;
            }
            .json-preview {
                display: inline;
                color: rgb(110, 110, 110);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .preview-content {
                display: inline;
            }
            .json-content {
                display: none;
            }
            .json-key {
                color: rgb(136, 19, 145);
            }
            .json-string {
                color: rgb(196, 26, 22);
            }
            .json-number {
                color: rgb(28, 0, 207);
            }
            .json-boolean {
                color: rgb(13, 34, 170);
            }
            .json-null {
                color: rgb(13, 34, 170);
            }
            .json-mark {
                color: rgb(110, 110, 110);
            }
            .section-title {
                font-weight: bold;
                color: #666;
                margin-bottom: 8px;
            }
        `;

        // 添加到文档中
        document.head.appendChild(styleElement);
    </script>
</body>
</html> 