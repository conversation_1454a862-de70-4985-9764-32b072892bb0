/* 主题颜色 */
:root {
    --primary: #4361ee;
    --primary-light: #edf2ff;
    --primary-dark: #3651d4;
    --secondary: #6c757d;
    --success: #2ec4b6;
    --danger: #e63946;
    --warning: #ff9f1c;
    --info: #4cc9f0;
    --dark: #2b2d42;
    --gray: #8d99ae;
    --light: #f8f9fa;
    --white: #ffffff;
    
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-sm: 4px;
    
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    
    --transition: all 0.3s ease;
}

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Se<PERSON><PERSON> UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background-color: var(--light);
    color: var(--dark);
    line-height: 1.5;
}

/* 应用容器 */
.app-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    min-height: 100vh;
}

/* 侧边栏 */
.sidebar {
    background: var(--white);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    padding: 2rem;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.sidebar-header i {
    font-size: 2rem;
    color: var(--primary);
}

.sidebar-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.sidebar-nav li:hover {
    background: var(--primary-light);
    color: var(--primary);
}

.sidebar-nav li.active {
    background: var(--primary-light);
    color: var(--primary);
    font-weight: 500;
}

.sidebar-nav i {
    font-size: 1.25rem;
}

.storage-info {
    margin-top: auto;
    padding: 1.5rem;
    background: var(--primary-light);
    border-radius: var(--border-radius);
}

.storage-text {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.storage-used {
    color: var(--primary);
    font-weight: 500;
}

.storage-info .progress {
    height: 6px;
    background: rgba(67, 97, 238, 0.2);
    border-radius: 3px;
    margin-bottom: 0.5rem;
}

.storage-info .progress-bar {
    background: var(--primary);
    border-radius: 3px;
}

.storage-details {
    font-size: 0.875rem;
    color: var(--gray);
}

/* 当存储空间使用超过90%时的警告样式 */
.storage-info.warning .progress-bar {
    background: var(--warning);
}

/* 当存储空间使用超过95%时的危险样式 */
.storage-info.danger .progress-bar {
    background: var(--danger);
}

/* 主内容区 */
.main-content {
    padding: 2rem;
    background: var(--light);
}

/* 工具栏 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.search-box {
    position: relative;
    width: 300px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray);
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: none;
    border-radius: var(--border-radius);
    background: var(--white);
    box-shadow: var(--shadow-sm);
}

.search-box input:focus {
    outline: none;
    box-shadow: var(--shadow);
}

.toolbar-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border: none;
    border-radius: var(--border-radius);
    background: var(--white);
    color: var(--gray);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.btn-icon:hover {
    background: var(--primary-light);
    color: var(--primary);
}

.upload-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--secondary);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    opacity: 0.7;
}

.upload-btn:hover {
    opacity: 0.9;
}

.upload-btn.active {
    background: var(--primary);
    opacity: 1;
}

.upload-btn.active:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* 上传区域 */
.upload-zone {
    border: 2px dashed var(--gray);
    border-radius: var(--border-radius);
    padding: 3rem;
    text-align: center;
    background: var(--white);
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 2rem;
    position: relative;
}

.upload-zone:hover,
.upload-zone.dragover {
    border-color: var(--primary);
    background: var(--primary-light);
}

.upload-content {
    max-width: 400px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary);
    margin-bottom: 1rem;
}

.upload-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.upload-types {
    color: var(--gray);
    font-size: 0.875rem;
    margin-top: 1rem;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 2;
}

/* 上传进度 */
.upload-progress {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.upload-progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.upload-progress-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
}

.upload-progress .progress {
    height: 6px;
    background: var(--light);
    border-radius: 3px;
    margin-bottom: 0.75rem;
}

.upload-progress .progress-bar {
    background: var(--primary);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.upload-progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.upload-progress-details {
    display: flex;
    gap: 1rem;
    color: var(--gray);
}

/* 文件列表 */
.files-container {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
}

.files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.files-header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.files-header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.files-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    position: relative;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.file-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
}

.file-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.file-icon {
    width: 100%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.file-icon i {
    font-size: 2.5rem;
    color: var(--primary);
}

.file-info {
    text-align: center;
}

.file-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-meta {
    font-size: 0.75rem;
    color: var(--gray);
}

.file-actions {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    display: none;
    gap: 0.25rem;
    z-index: 1;
}

.file-card:hover .file-actions {
    display: flex;
}

.file-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--white);
    border: none;
    color: var(--gray);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.file-action-btn:hover {
    background: var(--primary);
    color: var(--white);
}

/* 预览模态框基础样式 */
.preview-modal .modal-dialog {
    max-width: min(1200px, 85vw);
    min-width: min(600px, 80vw);
    margin: 2vh auto;
    height: auto;
}

.preview-modal .modal-content {
    background: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    height: 100%;
    width: 100%;
    min-height: 400px;
    position: relative;
}

/* 图片预览特定样式 */
.preview-modal.image-preview .modal-dialog {
    max-width: min(900px, 80vw);
    min-width: min(500px, 75vw);
    margin: 3vh auto;
}

.preview-modal.image-preview .modal-content {
    background: #1a1a1a;
    display: flex;
    flex-direction: column;
    height: 80vh;
}

/* 视频预览特定样式 */
.preview-modal.video-preview .modal-dialog {
    max-width: min(1000px, 85vw);
    min-width: min(600px, 80vw);
    margin: 3vh auto;
}

.preview-modal.video-preview .modal-content {
    background: #1a1a1a;
    display: flex;
    flex-direction: column;
    height: 80vh;
}

/* PDF预览特定样式 */
.preview-modal.pdf-preview .modal-dialog {
    max-width: min(1200px, 85vw);
    min-width: min(600px, 80vw);
    margin: 2vh auto;
    height: 90vh;
}

.preview-modal.pdf-preview .modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .preview-modal .modal-dialog,
    .preview-modal.image-preview .modal-dialog,
    .preview-modal.video-preview .modal-dialog,
    .preview-modal.pdf-preview .modal-dialog {
        max-width: 95vw;
        min-width: 90vw;
        margin: 1vh auto;
    }
    
    .preview-modal .modal-content,
    .preview-modal.image-preview .modal-content,
    .preview-modal.video-preview .modal-content,
    .preview-modal.pdf-preview .modal-content {
        height: 85vh;
    }
}

.image-preview-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

#imagePreview {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    cursor: zoom-in;
    user-select: none;
    transform-origin: center;
    transition: transform 0.2s ease;
}

#imagePreview.zoomed {
    cursor: grab;
    max-width: none;
    max-height: none;
}

#imagePreview.dragging {
    cursor: grabbing !important;
    transition: none;
}

.preview-modal.image-preview img.preview-item {
    max-width: 100%;
    max-height: 85vh;
    object-fit: contain;
    transition: transform 0.2s ease;
}

#imagePreview {
    cursor: zoom-in;
    user-select: none;
    transform-origin: center;
}

#imagePreview.zoomed {
    cursor: grab;
}

#imagePreview.dragging {
    cursor: grabbing !important;
    transition: none;
}

.preview-modal.image-preview img.preview-item.zoomed {
    cursor: grab;
    max-width: none;
    max-height: none;
    transform-origin: center;
}

.preview-modal.image-preview img.preview-item.zoomed.dragging {
    cursor: grabbing;
    transition: none;
}

/* 图片预览工具栏 */
.image-preview-toolbar {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    border-radius: 20px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-preview-container:hover .image-preview-toolbar {
    opacity: 1;
}

.image-preview-toolbar button {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: #fff;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.image-preview-toolbar button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.image-preview-toolbar button i {
    font-size: 1.1rem;
}

.image-preview-toolbar .zoom-value {
    color: #fff;
    font-size: 0.9rem;
    margin: 0 4px;
    align-self: center;
    margin-top: 2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .preview-modal .modal-dialog {
        max-width: 95vw;
        min-width: 90vw;
        margin: 1vh auto;
    }
    
    .preview-modal.image-preview .modal-body {
        padding: 0.5rem;
    }
    
    .preview-modal.image-preview img.preview-item {
        max-width: 90vw;
        max-height: 85vh;
    }
    
    .image-preview-toolbar {
        bottom: 10px;
        padding: 6px 8px;
    }
    
    .image-preview-toolbar button {
        padding: 4px 8px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .preview-modal .modal-content {
        background: #1a1a1a;
    }
    
    .preview-modal.image-preview .modal-body {
        background: #262626;
    }
}

/* Toast消息 */
.toast-container {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1050;
}

.toast {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-lg);
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideIn 0.3s ease;
}

.toast.success { border-left: 4px solid var(--success); }
.toast.error { border-left: 4px solid var(--danger); }
.toast.warning { border-left: 4px solid var(--warning); }
.toast.info { border-left: 4px solid var(--info); }

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设置 */
@media (max-width: 1440px) {
    .app-container {
        grid-template-columns: 220px 1fr;
    }
    
    .files-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.75rem;
    }
    
    .main-content {
        padding: 1.25rem;
    }

    .sidebar {
        padding: 1.25rem;
    }

    .file-card {
        min-height: 160px;
    }

    .file-icon {
        height: 80px;
        margin-bottom: 0.5rem;
    }

    .file-info {
        padding: 0.25rem 0;
    }

    .file-meta {
        font-size: 0.7rem;
    }

    .file-name {
        font-size: 0.85rem;
    }
}

@media (max-width: 1024px) {
    .app-container {
        grid-template-columns: 200px 1fr;
    }
    
    .sidebar {
        padding: 1rem;
    }
    
    .files-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 0.75rem;
    }
    
    .file-card {
        padding: 0.5rem;
        min-height: 150px;
    }
    
    .file-icon {
        height: 70px;
    }

    .sidebar-nav li {
        padding: 0.75rem;
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }

    .storage-info {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .app-container {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        display: none;
    }
    
    .main-content {
        padding: 0.75rem;
    }
    
    .toolbar {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .search-box {
        width: 100%;
    }
    
    .files-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.5rem;
    }

    .file-card {
        min-height: 140px;
    }
}

/* 已选择文件 */
.selected-file {
    margin: 1rem;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.selected-file-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.selected-file-info i {
    font-size: 2rem;
    color: var(--primary-color);
}

.selected-file-details {
    display: flex;
    flex-direction: column;
}

.selected-file-details span {
    margin: 0.2rem 0;
}

/* 上传进度 */
.upload-progress {
    margin: 1rem;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.upload-progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.upload-progress-content {
    padding: 0.5rem;
}

.upload-progress .progress {
    height: 8px;
    margin-bottom: 0.5rem;
    background-color: var(--bg-lighter);
}

.upload-progress .progress-bar {
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.upload-progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.upload-progress-details {
    display: flex;
    gap: 1rem;
}

/* 加载状态 */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    color: var(--gray);
}

.loading-state .spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
}

.loading-state p {
    margin: 0;
    font-size: 1rem;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    color: var(--gray);
    background: var(--white);
    border-radius: var(--border-radius);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.empty-state.error i {
    color: var(--danger);
}

.empty-state h4 {
    margin: 0 0 0.5rem;
    color: var(--dark);
}

.empty-state p {
    margin: 0;
    color: var(--gray);
}

.empty-state .btn {
    margin-top: 1rem;
}

.files-actions .btn-icon {
    width: 32px;
    height: 32px;
    padding: 0;
    border: none;
    border-radius: var(--border-radius);
    background: transparent;
    color: var(--gray);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.files-actions .btn-icon:hover {
    color: var(--primary);
    background: transparent;
}

.files-actions .btn-icon:focus {
    outline: none;
    box-shadow: none;
    background: transparent;
}

.files-actions .btn-icon:active {
    transform: scale(0.95);
}

/* 列表视图 */
.files-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.file-list-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto auto;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.file-list-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.file-list-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    border-radius: var(--border-radius);
    color: var(--primary);
}

.file-list-icon i {
    font-size: 1.25rem;
}

.file-list-name {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-list-size,
.file-list-date {
    color: var(--gray);
    font-size: 0.875rem;
    white-space: nowrap;
}

.file-list-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.file-list-item:hover .file-list-actions {
    opacity: 1;
}

/* 排序菜单 */
.sort-menu {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem;
    z-index: 1000;
}

.sort-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.sort-item:hover {
    background: var(--primary-light);
    color: var(--primary);
}

.sort-item i {
    font-size: 1.1rem;
}

/* 搜索结果高亮 */
.highlight {
    background-color: var(--warning);
    padding: 0.1em 0.2em;
    border-radius: 2px;
}

/* 下载进度 */
.download-progress {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow);
    margin-bottom: 1rem;
    width: 300px;
}

.download-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.download-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.download-title i {
    color: var(--primary);
}

.download-title span {
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.download-progress .progress {
    height: 6px;
    background: var(--light);
    border-radius: 3px;
    margin-bottom: 0.75rem;
    overflow: hidden;
}

.download-progress .progress-bar {
    background: var(--primary);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--gray);
}

.speed {
    white-space: nowrap;
}

/* 优化文件卡片布局 */
.file-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 160px;
    padding: 0.75rem;
}

.file-icon {
    flex: 1;
    min-height: 80px;
    margin-bottom: 0.5rem;
}

.file-info {
    padding: 0.25rem 0;
}

/* 优化工具栏布局 */
.toolbar {
    gap: 0.75rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.toolbar-actions {
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* 优化搜索框 */
.search-box {
    min-width: 180px;
    max-width: 300px;
    flex: 1;
}

.search-box input {
    padding: 0.5rem 1rem 0.5rem 2rem;
    font-size: 0.9rem;
}

/* 优化上传区域 */
.upload-zone {
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.upload-content {
    max-width: 100%;
    padding: 0 1rem;
}

.upload-icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
}

.upload-content h3 {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
}

.upload-types {
    font-size: 0.8rem;
}

/* 优化预览模态框 */
.preview-container {
    min-height: 250px;
    max-height: 60vh;
    overflow: auto;
}

.preview-item {
    max-height: 60vh;
    object-fit: contain;
}

/* 文件队列 */
.file-queue {
    background: var(--white);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
}

.file-queue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.file-queue-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
}

.file-queue-actions {
    display: flex;
    gap: 0.5rem;
}

.file-queue-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem;
}

.queue-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    background: var(--light);
    margin-bottom: 0.5rem;
}

.queue-item:last-child {
    margin-bottom: 0;
}

.queue-item-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 0;
}

.queue-item-icon {
    font-size: 1.25rem;
    color: var(--primary);
}

.queue-item-details {
    flex: 1;
    min-width: 0;
}

.queue-item-name {
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.queue-item-size {
    font-size: 0.8rem;
    color: var(--gray);
}

.queue-item-actions {
    display: flex;
    gap: 0.5rem;
}

.queue-item-status {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    background: var(--light);
}

.queue-item-status.pending {
    color: var(--primary);
    background: var(--primary-light);
}

.queue-item-status.uploading {
    color: var(--info);
    background: rgba(76, 201, 240, 0.1);
}

.queue-item-status.success {
    color: var(--success);
    background: rgba(46, 196, 182, 0.1);
}

.queue-item-status.error {
    color: var(--danger);
    background: rgba(230, 57, 70, 0.1);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-text {
    background: none;
    border: none;
    color: var(--gray);
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    transition: var(--transition);
}

.btn-text:hover {
    color: var(--dark);
}

/* 文件选择按钮组 */
.file-selection-group {
    display: flex;
    gap: 0.5rem;
    margin: 1rem 0;
}

/* 批量操作按钮 */
.batch-actions {
    display: flex;
    gap: 0.5rem;
    margin-left: auto;
}

/* 文件选择框 */
.file-checkbox {
    width: 18px;
    height: 18px;
    margin: 0;
}

/* 选中状态的文件卡片 */
.file-card.selected {
    background: var(--primary-light);
    border: 1px solid var(--primary);
}

/* 文件卡片选择框容器 */
.file-select-box {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.file-card:hover .file-select-box,
.file-card.selected .file-select-box {
    opacity: 1;
}

/* 选中状态的文件卡片 */
.file-card.selected {
    background: var(--primary-light);
    border: 1px solid var(--primary);
}

.file-card {
    position: relative;
    padding: 0.75rem;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.file-card:hover {
    border-color: var(--primary);
    transform: translateY(-2px);
}

/* 全选复选框样式 */
.select-all-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    user-select: none;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.select-all-checkbox:hover {
    background: var(--primary-light);
}

.select-all-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.checkbox-text {
    font-size: 0.9rem;
    color: var(--dark);
}

/* 批量操作区域 */
.batch-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: var(--primary-light);
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.selected-count {
    font-size: 0.9rem;
    color: var(--primary);
    font-weight: 500;
}

/* 文件卡片选择框样式优化 */
.file-select-box {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.file-select-box input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    border-color: var(--primary);
}

/* 选中状态的文件卡片 */
.file-card.selected {
    background: var(--primary-light);
    border: 1px solid var(--primary);
}

.file-card {
    position: relative;
    padding: 0.75rem;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.file-card:hover {
    border-color: var(--primary);
    transform: translateY(-2px);
}

/* 选择框样式 */
.selection-box {
    position: fixed;
    border: 1px solid var(--primary);
    background: rgba(67, 97, 238, 0.1);
    pointer-events: none;
    z-index: 1000;
}

/* 文件列表容器 */
.files-grid {
    position: relative;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 视频预览关样式 */
.preview-container {
    position: relative;
    width: 100%;
    background: #000;
    border-radius: var(--border-radius);
    aspect-ratio: 16/9;
    max-height: 80vh;
    overflow: hidden;
}

.video-preview-container {
    position: relative;
    width: 100%;
    height: 100%;
    background: #000;
    display: flex;
    flex-direction: column;
}

.players-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    flex: 1;
}

.video-js-wrapper,
.plyr-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.video-player {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: #000;
}

/* Video.js自定义样式 */
.video-js {
    width: 100% !important;
    height: 100% !important;
    font-size: 10px;
}

.video-js .vjs-tech {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.video-js .vjs-big-play-button {
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    border-radius: 50%;
    border: 2px solid #fff;
    width: 60px;
    height: 60px;
    line-height: 56px;
    font-size: 2em;
}

.video-js .vjs-control-bar {
    background-color: rgba(0, 0, 0, 0.7);
    height: 3em;
    font-size: 12px;
}

/* 倍速控制按钮样式 */
.video-js .vjs-playback-rate {
    font-size: 12px;
}

.video-js .vjs-playback-rate .vjs-playback-rate-value {
    font-size: 1.2em;
    line-height: 3;
}

.video-js .vjs-menu-button-popup .vjs-menu {
    bottom: 3em;
}

.video-js .vjs-menu-button-popup .vjs-menu .vjs-menu-content {
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 4px;
    padding: 0;
}

.video-js .vjs-menu li {
    padding: 0.5em;
    font-size: 1.2em;
    text-align: center;
    text-transform: none;
}

.video-js .vjs-menu li.vjs-selected {
    background-color: var(--primary);
    color: #fff;
}

.video-js .vjs-menu li:hover {
    background-color: rgba(67, 97, 238, 0.6);
}

.video-js .vjs-menu li.vjs-menu-title {
    font-size: 1em;
    text-transform: uppercase;
    padding: 0.5em;
}

/* 播放器容器样式 */
.video-js-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
}

/* Plyr自定义样式 */
.plyr {
    width: 100% !important;
    height: 100% !important;
    --plyr-color-main: var(--primary);
}

/* 播放器切换按钮组 */
.player-switcher {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    gap: 8px;
    z-index: 1000;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
    padding: 16px;
    width: 100%;
    justify-content: flex-end;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-preview-container:hover .player-switcher {
    opacity: 1;
}

.player-switcher button {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    backdrop-filter: blur(4px);
}

.player-switcher button:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
}

.player-switcher button.active {
    background: var(--primary);
    border-color: var(--primary);
    color: #fff;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
    .preview-modal.video-preview .modal-dialog {
        max-width: 100vw;
        min-width: 100vw;
        margin: 0;
        height: 100vh;
    }

    .preview-modal.video-preview .modal-content {
        height: 100vh;
        border-radius: 0;
    }
}

/* PDF预览样式 */
.preview-modal.pdf-preview .modal-dialog {
    max-width: min(1200px, 85vw);
    min-width: min(600px, 80vw);
    margin: 2vh auto;
    height: 90vh;
}

.preview-modal.pdf-preview .modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.pdf-preview-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: #f8f9fa;
}

.pdf-toolbar {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4px 12px;
    background: #fff;
    border-bottom: 1px solid #dee2e6;
    gap: 8px;
    width: 100%;
    height: 40px;
    flex-shrink: 0;
    z-index: 1;
}

.pdf-nav {
    display: flex;
    align-items: center;
    gap: 6px;
}

.pdf-nav button {
    padding: 2px 6px;
    border: 1px solid #dee2e6;
    background: #fff;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    height: 24px;
    display: flex;
    align-items: center;
}

.pdf-nav button:hover {
    background: #f8f9fa;
}

.pdf-nav button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pdf-page-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
}

.pdf-page-info input {
    width: 60px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 2px 4px;
    height: 28px;
    font-size: 14px;
}

.pdf-page-info input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.pdf-zoom {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-left: 8px;
}

.pdf-zoom button {
    padding: 2px 6px;
    border: 1px solid #dee2e6;
    background: #fff;
    border-radius: 3px;
    cursor: pointer;
    height: 24px;
}

.pdf-zoom span {
    min-width: 36px;
    text-align: center;
    font-size: 12px;
}

.pdf-main {
    display: flex;
    flex: 1;
    min-height: 0;
    height: calc(100vh - 200px);
    overflow: hidden;
    position: relative;
}

.pdf-sidebar {
    width: 200px;
    height: 100%;
    border-right: 1px solid #dee2e6;
    background: #fff;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.pdf-thumbnails {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: 100%;
}

.pdf-thumbnail {
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 4px;
    transition: all 0.2s;
    width: 100%;
    height: 160px;
    min-height: 160px;
    flex-shrink: 0;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.pdf-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #e9ecef;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

/* 自定义滚动条样式 */
.pdf-thumbnails::-webkit-scrollbar {
    width: 6px;
}

.pdf-thumbnails::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.pdf-thumbnails::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.pdf-thumbnails::-webkit-scrollbar-thumb:hover {
    background: #666;
}

/* Firefox滚动条样式 */
.pdf-thumbnails {
    scrollbar-width: thin;
    scrollbar-color: #888 #f1f1f1;
}

#pdfCanvas {
    max-width: none;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .preview-modal.pdf-preview .modal-content {
        background: #1a1a1a;
    }
    
    .preview-modal.pdf-preview .modal-body {
        background: #262626;
    }
    
    .pdf-toolbar {
        background: #333;
        border-color: #404040;
    }
    
    .pdf-content {
        background: #1a1a1a;
    }
    
    #pdfCanvas {
        background: #262626;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .preview-modal.pdf-preview .modal-dialog {
        max-width: 100%;
        margin: 0;
        height: 100vh;
    }
    
    .pdf-toolbar {
        padding: 4px 8px;
        height: 36px;
    }
    
    .pdf-content {
        padding: 8px;
    }
} 