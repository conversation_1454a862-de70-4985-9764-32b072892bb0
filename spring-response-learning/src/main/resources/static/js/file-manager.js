// 文件管理器类
class FileManager {
    constructor(config) {
        this.config = {
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedPreviewTypes: {
                image: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
                pdf: ['pdf'],
                video: ['mp4', 'webm', 'ogg'],
                audio: ['mp3', 'wav', 'ogg'],
                text: ['txt', 'json', 'xml', 'html', 'css', 'js', 'md']
            },
            defaultVideoPlayer: 'videojs', // 默认视频播放器
            api: {
                upload: '/api/advanced/upload',
                download: '/api/advanced/download',
                streamDownload: '/api/advanced/stream-download',
                list: '/api/advanced/files',
                delete: '/api/advanced/files'
            },
            modals: {},
            ...config
        };

        this.uploadQueue = new Map(); // 上传队列
        this.selectedFiles = new Set(); // 选中的文件
        this.currentFile = null;
        this.fileToDelete = null;
        this.videoPlayers = new Map(); // 存储视频播放器实例
        this.currentVideoPlayer = null;
        this.init();
        
        // 监听预览模态框的事件
        const previewModal = document.getElementById('previewModal');
        if (previewModal) {
            previewModal.addEventListener('hidden.bs.modal', () => this.closePreview());
            previewModal.addEventListener('shown.bs.modal', () => this.onPreviewModalShown());
        }
    }

    init() {
        // 等待DOM完全加载后再初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeComponents());
        } else {
            this.initializeComponents();
        }
    }

    initializeComponents() {
        this.setupDropZone();
        this.setupUploadButton();
        this.setupSidebarFilters();
        this.setupToolbar();
        this.setupDragSelection();
        this.refreshList();
        this.updateStorageInfo();

        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.refreshList();
                this.updateStorageInfo();
            }
        });
    }

    setupDropZone() {
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        
        if (!dropZone || !fileInput) {
            console.error('Required elements not found');
            return;
        }

        // 阻止默认拖放行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        // 添加拖放视觉反馈
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.add('dragover');
            });
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.remove('dragover');
            });
        });

        // 处理文件拖放
        dropZone.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length) {
                this.handleFileSelect(files);
            }
        });

        // 处理文件选择
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length) {
                this.handleFileSelect(e.target.files);
            }
        });
    }

    setupUploadButton() {
        const uploadBtn = document.getElementById('uploadBtn');
        const fileInput = document.getElementById('fileInput');
        
        if (uploadBtn && fileInput) {
            // 点击上传按钮时触发文件选择
            uploadBtn.addEventListener('click', () => {
                if (this.uploadQueue.size > 0) {
                    this.uploadAll();
                } else {
                    fileInput.click();
                }
            });
        }
    }

    handleFileSelect(files) {
        if (!files || files.length === 0) {
            return;
        }

        const fileQueue = document.getElementById('fileQueue');
        const fileQueueList = fileQueue.querySelector('.file-queue-list');
        
        // 显示文件队列
        fileQueue.style.display = 'block';

        // 添加文件到队列
        Array.from(files).forEach(file => {
            if (!this.uploadQueue.has(file.name)) {
                this.uploadQueue.set(file.name, {
                    file: file,
                    status: 'pending'
                });
                
                // 添加到队列显示
                const queueItem = document.createElement('div');
                queueItem.className = 'queue-item';
                queueItem.innerHTML = `
                    <div class="queue-item-info">
                        <i class="bi ${this.getFileIcon(file.name)} queue-item-icon"></i>
                        <div class="queue-item-details">
                            <div class="queue-item-name">${file.name}</div>
                            <div class="queue-item-size">${this.formatSize(file.size)}</div>
                        </div>
                    </div>
                    <div class="queue-item-status pending">等待上传</div>
                    <div class="queue-item-actions">
                        <button class="btn btn-text btn-sm" onclick="fileManager.removeFromQueue('${file.name}')">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                `;
                fileQueueList.appendChild(queueItem);
            }
        });

        // 更新上传按钮状态
        const uploadBtn = document.getElementById('uploadBtn');
        if (uploadBtn) {
            uploadBtn.classList.add('active');
            uploadBtn.onclick = () => this.uploadAll();
        }
    }

    removeFromQueue(filename) {
        this.uploadQueue.delete(filename);
        this.updateQueueDisplay();
    }

    clearQueue() {
        this.uploadQueue.clear();
        this.updateQueueDisplay();
    }

    updateQueueDisplay() {
        const fileQueue = document.getElementById('fileQueue');
        const fileQueueList = fileQueue.querySelector('.file-queue-list');
        
        if (this.uploadQueue.size === 0) {
            fileQueue.style.display = 'none';
            fileQueueList.innerHTML = '';
            return;
        }

        fileQueueList.innerHTML = '';
        this.uploadQueue.forEach((item, filename) => {
            const queueItem = document.createElement('div');
            queueItem.className = 'queue-item';
            queueItem.innerHTML = `
                <div class="queue-item-info">
                    <i class="bi ${this.getFileIcon(filename)} queue-item-icon"></i>
                    <div class="queue-item-details">
                        <div class="queue-item-name">${filename}</div>
                        <div class="queue-item-size">${this.formatSize(item.file.size)}</div>
                    </div>
                </div>
                <div class="queue-item-status ${item.status}">${this.getStatusText(item.status)}</div>
                <div class="queue-item-actions">
                    <button class="btn btn-text btn-sm" onclick="fileManager.removeFromQueue('${filename}')">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;
            fileQueueList.appendChild(queueItem);
        });
    }

    getStatusText(status) {
        const statusMap = {
            pending: '等待上传',
            uploading: '上传中',
            success: '上传成功',
            error: '上传失败'
        };
        return statusMap[status] || status;
    }

    async uploadAll() {
        const totalFiles = this.uploadQueue.size;
        if (totalFiles === 0) {
            this.showToast('请先选择要上传的文件', 'warning');
            return;
        }

        // 创建FormData对象，添加所有文件
        const formData = new FormData();
        this.uploadQueue.forEach((item, filename) => {
            if (item.status === 'pending') {
                formData.append('files', item.file);
            }
        });

        try {
            const response = await fetch(`${this.config.api.upload}/batch`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error('上传失败');
            }

            const result = await response.json();
            
            if (result.status === 200) {
                // 更新每个文件的状态
                result.data.forEach(fileResult => {
                    const item = this.uploadQueue.get(fileResult.filename);
                    if (item) {
                        item.status = fileResult.status;
                        item.message = fileResult.message;
                    }
                });

                // 显示上传结果
                const successCount = result.data.filter(r => r.status === 'success').length;
                const failCount = result.data.filter(r => r.status === 'error').length;

                if (successCount > 0) {
                    this.showToast(`成功上传 ${successCount} 个文件`, 'success');
                }
                if (failCount > 0) {
                    this.showToast(`${failCount} 个文件上传失败`, 'error');
                }

                // 更新队列显示
                this.updateQueueDisplay();

                // 刷新文件列表和存储信息
                await Promise.all([
                    this.refreshList(),
                    this.updateStorageInfo()
                ]);
            } else {
                throw new Error(result.message || '上传失败');
            }
        } catch (error) {
            console.error('上传错误:', error);
            this.showToast(error.message || '上传失败', 'error');
        }
    }

    clearSelectedFile() {
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        
        if (fileInput) fileInput.value = '';
        if (uploadBtn) uploadBtn.classList.remove('active');
        
        this.handleFileSelect(null);
    }

    async uploadFile(file) {
        if (!file) {
            this.showToast('请选择要上传的文件', 'warning');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        const progressBar = document.querySelector('#uploadProgress .progress-bar');
        const progressText = document.querySelector('#uploadProgress .upload-progress-text');
        const uploadSpeed = document.querySelector('#uploadProgress .upload-speed');
        const remainingTime = document.querySelector('#uploadProgress .upload-remaining');
        const uploadProgress = document.getElementById('uploadProgress');

        if (!progressBar || !progressText || !uploadSpeed || !remainingTime || !uploadProgress) {
            console.error('Upload progress elements not found');
            this.showToast('上传组件初始化失败', 'error');
            return;
        }

        uploadProgress.style.display = 'block';
        progressBar.style.width = '0%';
        progressText.textContent = '0%';

        try {
            const startTime = Date.now();
            let lastLoaded = 0;
            let lastTime = startTime;

            const xhr = new XMLHttpRequest();
            xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                    const progress = (event.loaded / event.total) * 100;
                    progressBar.style.width = progress + '%';
                    progressText.textContent = Math.round(progress) + '%';

                    const currentTime = Date.now();
                    const timeElapsed = (currentTime - lastTime) / 1000;
                    if (timeElapsed > 0) {
                        const loadedDiff = event.loaded - lastLoaded;
                        const speed = loadedDiff / timeElapsed;
                        uploadSpeed.textContent = this.formatSize(speed) + '/s';

                        const remaining = event.total - event.loaded;
                        const remainingSecs = remaining / speed;
                        remainingTime.textContent = `剩余时间: ${this.formatTime(remainingSecs)}`;

                        lastLoaded = event.loaded;
                        lastTime = currentTime;
                    }
                }
            };

            const response = await new Promise((resolve, reject) => {
                xhr.onload = () => {
                    try {
                        const result = JSON.parse(xhr.responseText);
                        resolve({
                            status: xhr.status,
                            data: result
                        });
                    } catch (e) {
                        reject(new Error('Invalid response format'));
                    }
                };
                xhr.onerror = () => reject(new Error('Network error'));
                xhr.open('POST', this.config.api.upload);
                xhr.send(formData);
            });

            if (response.status === 409) {
                this.showToast(response.data.message || '文件已存在', 'warning');
            } else if (response.status >= 200 && response.status < 300) {
                this.showToast(response.data.message || '文件上传成功', 'success');
                this.clearSelectedFile();
                // 刷新文件列表和存储信息
                await Promise.all([
                    this.refreshList(),
                    this.updateStorageInfo()
                ]);
            } else {
                throw new Error(response.data.message || '上传失败');
            }
        } catch (error) {
            console.error('上传错误:', error);
            this.showToast(error.message || '上传失败', 'error');
        } finally {
            setTimeout(() => {
                uploadProgress.style.display = 'none';
                progressBar.style.width = '0%';
                progressText.textContent = '0%';
                uploadSpeed.textContent = '0 KB/s';
                remainingTime.textContent = '剩余时间: 计算中...';
            }, 1000);
        }
    }

    resetUploadForm() {
        document.getElementById('uploadForm').reset();
        document.querySelector('.upload-text').textContent = '拖拽文件到此处';
    }

    async refreshList() {
        const fileList = document.getElementById('fileList');
        if (!fileList) return;

        // 显示加载状态
        fileList.innerHTML = `
            <div class="loading-state">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p>正在加载文件列表...</p>
            </div>
        `;

        try {
            const response = await fetch(this.config.api.list, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.status === 200) {
                this.renderFileList(result.data || []);
            } else {
                throw new Error(result.message || '获取文件列表失败');
            }
        } catch (error) {
            console.error('获取文件列表错误:', error);
            fileList.innerHTML = `
                <div class="empty-state error">
                    <i class="bi bi-exclamation-circle text-danger"></i>
                    <h4>加载失败</h4>
                    <p>${error.message || '请稍后重试'}</p>
                    <button class="btn btn-primary mt-3" onclick="fileManager.refreshList()">
                        <i class="bi bi-arrow-clockwise"></i> 重试
                    </button>
                </div>
            `;
        }
    }

    renderFileList(files) {
        const fileList = document.getElementById('fileList');
        if (!fileList) return;
        
        // 应用过滤器
        let filteredFiles = files;
        
        // 应用搜索过滤
        if (this.currentSearchQuery) {
            filteredFiles = filteredFiles.filter(file => 
                file.name.toLowerCase().includes(this.currentSearchQuery)
            );
        }

        // 应用分类过滤
        if (this.currentFilter !== 'all') {
            filteredFiles = filteredFiles.filter(file => 
                this.getFileType(file.name) === this.currentFilter
            );
        }

        // 应用排序
        if (this.currentSortBy) {
            filteredFiles.sort((a, b) => {
                switch (this.currentSortBy) {
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'size':
                        return b.size - a.size;
                    case 'date':
                        return b.lastModified - a.lastModified;
                    default:
                        return 0;
                }
            });
        }
        
        if (!filteredFiles || !filteredFiles.length) {
            fileList.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-folder2-open"></i>
                    <h4>暂无文件</h4>
                    <p>${this.getEmptyStateMessage()}</p>
                </div>
            `;
            return;
        }

        const html = filteredFiles.map(file => this.currentView === 'list' 
            ? this.renderListViewItem(file)
            : this.renderGridViewItem(file)
        ).join('');
        
        fileList.innerHTML = html;
    }

    getEmptyStateMessage() {
        if (this.currentSearchQuery) {
            return '没有找到匹配的文件';
        }
        return this.currentFilter === 'all' 
            ? '上传些文件开始使用吧' 
            : '当前分类下没有文件';
    }

    renderGridViewItem(file) {
        return `
            <div class="file-card ${this.selectedFiles.has(file.name) ? 'selected' : ''}" data-filename="${file.name}">
                <div class="file-select-box">
                    <input type="checkbox" class="file-checkbox" 
                           ${this.selectedFiles.has(file.name) ? 'checked' : ''}
                           onchange="fileManager.toggleFileSelection('${file.name}', this.checked)">
                </div>
                <div class="file-icon">
                    <i class="bi ${this.getFileIcon(file.name)}"></i>
                </div>
                <div class="file-info">
                    <div class="file-name" title="${file.name}">${file.name}</div>
                    <div class="file-meta">
                        <span><i class="bi bi-hdd"></i> ${this.formatSize(file.size)}</span>
                        <span><i class="bi bi-clock"></i> ${this.formatDate(file.lastModified)}</span>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="file-action-btn" onclick="fileManager.preview('${file.name}')" title="预览">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="file-action-btn" onclick="fileManager.download('${file.name}')" title="下载">
                        <i class="bi bi-download"></i>
                    </button>
                    <button class="file-action-btn" onclick="fileManager.showDeleteModal('${file.name}')" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    renderListViewItem(file) {
        return `
            <div class="file-list-item">
                <div class="file-list-icon">
                    <i class="bi ${this.getFileIcon(file.name)}"></i>
                </div>
                <div class="file-list-name" title="${file.name}">${file.name}</div>
                <div class="file-list-size">${this.formatSize(file.size)}</div>
                <div class="file-list-date">${this.formatDate(file.lastModified)}</div>
                <div class="file-list-actions">
                    <button class="file-action-btn" onclick="fileManager.preview('${file.name}')" title="预览">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="file-action-btn" onclick="fileManager.download('${file.name}')" title="下载">
                        <i class="bi bi-download"></i>
                    </button>
                    <button class="file-action-btn" onclick="fileManager.showDeleteModal('${file.name}')" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const icons = {
            // 图片
            jpg: 'bi-file-image',
            jpeg: 'bi-file-image',
            png: 'bi-file-image',
            gif: 'bi-file-image',
            // 文档
            pdf: 'bi-file-pdf',
            doc: 'bi-file-word',
            docx: 'bi-file-word',
            txt: 'bi-file-text',
            // 视频
            mp4: 'bi-file-play',
            webm: 'bi-file-play',
            // 音频
            mp3: 'bi-file-music',
            wav: 'bi-file-music',
            // 压缩文件
            zip: 'bi-file-zip',
            rar: 'bi-file-zip',
            // 代码文件
            js: 'bi-file-code',
            css: 'bi-file-code',
            html: 'bi-file-code',
            json: 'bi-file-code'
        };
        
        return icons[ext] || 'bi-file-earmark';
    }

    renderError() {
        const fileList = document.getElementById('fileList');
        fileList.innerHTML = `
            <div class="empty-state">
                <i class="bi bi-exclamation-circle"></i>
                <h4>加载失败</h4>
                <p>请后重试</p>
                <button class="btn btn-primary" onclick="fileManager.refreshList()">
                    <i class="bi bi-arrow-clockwise"></i> 重试
                </button>
            </div>
        `;
    }

    async preview(filename) {
        // 隐藏所有预览容器
        document.getElementById('imagePreview').hidden = true;
        document.getElementById('pdfPreviewContainer').hidden = true;
        document.getElementById('audioPreview').hidden = true;
        document.getElementById('textPreview').hidden = true;
        document.getElementById('unsupportedPreview').hidden = true;

        // 获取文件扩展名
        const ext = filename.split('.').pop().toLowerCase();
        
        // 根据文件类型显示不同的预览
        if (this.config.allowedPreviewTypes.image.includes(ext)) {
            this.showImagePreview(this.getPreviewUrl({name: filename}));
        } else if (ext === 'pdf') {
            this.showPdfPreview(this.getPreviewUrl({name: filename}));
        } else if (this.config.allowedPreviewTypes.video.includes(ext)) {
            this.showVideoPreview(this.getPreviewUrl({name: filename}));
        } else if (this.config.allowedPreviewTypes.audio.includes(ext)) {
            this.showAudioPreview(this.getPreviewUrl({name: filename}));
        } else if (this.config.allowedPreviewTypes.text.includes(ext)) {
            this.showTextPreview(this.getPreviewUrl({name: filename}));
        } else {
            document.getElementById('unsupportedPreview').hidden = false;
        }
        
        // 显示预览模态框
        const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
        previewModal.show();
    }

    async showImagePreview(url) {
        const img = document.getElementById('imagePreview');
        if (!img) {
            console.error('Image element not found.');
            return;
        }

        // 重置状态
        this.currentZoom = 1;
        this.currentTranslate = { x: 0, y: 0 };
        img.style.transform = 'none';
        img.classList.remove('zoomed', 'dragging');
        
        // 显示图片
        img.hidden = false;
        img.src = url;

        // 初始化拖动功能
        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let startTranslateX = 0;
        let startTranslateY = 0;

        img.onmousedown = (e) => {
            if (this.currentZoom <= 1) return;
            
            isDragging = true;
            img.classList.add('dragging');
            startX = e.clientX;
            startY = e.clientY;
            startTranslateX = this.currentTranslate.x;
            startTranslateY = this.currentTranslate.y;
            e.preventDefault();
        };

        document.onmousemove = (e) => {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            this.currentTranslate.x = startTranslateX + deltaX / this.currentZoom;
            this.currentTranslate.y = startTranslateY + deltaY / this.currentZoom;

            this.updateImageTransform(img);
        };

        document.onmouseup = () => {
            if (!isDragging) return;
            
            isDragging = false;
            img.classList.remove('dragging');
        };

        // 添加滚轮缩放
        img.onwheel = (e) => {
            e.preventDefault();
            if (e.deltaY < 0) {
                this.zoomIn();
            } else {
                this.zoomOut();
            }
        };
    }

    zoomIn() {
        const img = document.getElementById('imagePreview');
        if (!img) return;
        
        this.currentZoom = (this.currentZoom || 1) * 1.2;
        this.updateImageTransform(img);
    }

    zoomOut() {
        const img = document.getElementById('imagePreview');
        if (!img) return;
        
        this.currentZoom = (this.currentZoom || 1) / 1.2;
        if (this.currentZoom < 1) {
            this.currentZoom = 1;
        }
        this.updateImageTransform(img);
    }

    resetZoom() {
        const img = document.getElementById('imagePreview');
        if (!img) return;
        
        this.currentZoom = 1;
        this.currentTranslate = { x: 0, y: 0 };
        this.updateImageTransform(img);
    }

    updateImageTransform(img) {
        if (!img) return;
        
        if (this.currentZoom <= 1) {
            img.style.transform = 'none';
            img.classList.remove('zoomed');
            this.currentTranslate = { x: 0, y: 0 };
        } else {
            img.style.transform = `scale(${this.currentZoom}) translate(${this.currentTranslate.x}px, ${this.currentTranslate.y}px)`;
            img.classList.add('zoomed');
        }
    }

    showPdfPreview(url) {
        const container = document.getElementById('pdfPreviewContainer');
        container.hidden = false;

        // 获取所有控制元素
        const canvas = document.getElementById('pdfCanvas');
        const prevButton = document.getElementById('pdfPrev');
        const nextButton = document.getElementById('pdfNext');
        const currentPageInput = document.getElementById('pdfCurrentPage');
        const totalPagesSpan = document.getElementById('pdfTotalPages');
        const zoomInButton = document.getElementById('pdfZoomIn');
        const zoomOutButton = document.getElementById('pdfZoomOut');
        const zoomLevelSpan = document.getElementById('pdfZoomLevel');
        const thumbnailsContainer = document.getElementById('pdfThumbnails');

        let pdfDoc = null;
        let pageNum = 1;
        let scale = 1.0;
        const ZOOM_STEP = 0.25;

        // 加载PDF文档
        pdfjsLib.getDocument(url).promise.then(pdf => {
            pdfDoc = pdf;
            totalPagesSpan.textContent = pdf.numPages;
            currentPageInput.max = pdf.numPages;
            
            // 渲染第一页
            renderPage(pageNum);
            
            // 生成缩略图
            generateThumbnails();
        }).catch(error => {
            console.error('Error loading PDF:', error);
            this.showToast('PDF加载失败', 'error');
        });

        // 渲染指定页面
        const renderPage = (num) => {
            pdfDoc.getPage(num).then(page => {
                const viewport = page.getViewport({ scale });
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                const renderContext = {
                    canvasContext: canvas.getContext('2d'),
                    viewport: viewport
                };

                page.render(renderContext).promise.then(() => {
                    currentPageInput.value = num;
                    prevButton.disabled = num <= 1;
                    nextButton.disabled = num >= pdfDoc.numPages;
                    
                    // 更新缩略图选中状态
                    const thumbnails = thumbnailsContainer.querySelectorAll('.pdf-thumbnail');
                    thumbnails.forEach(thumb => {
                        thumb.classList.toggle('active', parseInt(thumb.dataset.page) === num);
                    });
                });
            });
        };

        // 生成缩略图
        const generateThumbnails = async () => {
            thumbnailsContainer.innerHTML = '';
            const thumbnailScale = 0.2;

            for (let i = 1; i <= pdfDoc.numPages; i++) {
                const page = await pdfDoc.getPage(i);
                const viewport = page.getViewport({ scale: thumbnailScale });
                
                const thumbCanvas = document.createElement('canvas');
                thumbCanvas.className = 'pdf-thumbnail';
                thumbCanvas.dataset.page = i;
                thumbCanvas.width = viewport.width;
                thumbCanvas.height = viewport.height;
                
                const renderContext = {
                    canvasContext: thumbCanvas.getContext('2d'),
                    viewport: viewport
                };

                await page.render(renderContext).promise;
                thumbnailsContainer.appendChild(thumbCanvas);

                // 添加点击事件
                thumbCanvas.onclick = () => {
                    pageNum = i;
                    renderPage(pageNum);
                };
            }
        };

        // 绑定事件处理
        prevButton.onclick = () => {
            if (pageNum <= 1) return;
            pageNum--;
            renderPage(pageNum);
        };

        nextButton.onclick = () => {
            if (pageNum >= pdfDoc.numPages) return;
            pageNum++;
            renderPage(pageNum);
        };

        currentPageInput.onchange = () => {
            const num = parseInt(currentPageInput.value);
            if (num >= 1 && num <= pdfDoc.numPages) {
                pageNum = num;
                renderPage(pageNum);
            } else {
                currentPageInput.value = pageNum;
            }
        };

        zoomInButton.onclick = () => {
            if (scale >= 3.0) return;
            scale += ZOOM_STEP;
            zoomLevelSpan.textContent = Math.round(scale * 100) + '%';
            renderPage(pageNum);
        };

        zoomOutButton.onclick = () => {
            if (scale <= 0.25) return;
            scale -= ZOOM_STEP;
            zoomLevelSpan.textContent = Math.round(scale * 100) + '%';
            renderPage(pageNum);
        };

        // 支持键盘翻页
        document.addEventListener('keydown', (e) => {
            if (container.hidden) return;
            if (e.key === 'ArrowLeft' && pageNum > 1) {
                pageNum--;
                renderPage(pageNum);
            } else if (e.key === 'ArrowRight' && pageNum < pdfDoc.numPages) {
                pageNum++;
                renderPage(pageNum);
            }
        });
    }

    showVideoPreview(url) {
        // 隐藏其他预览容器
        document.getElementById('imagePreview').hidden = true;
        document.getElementById('pdfPreviewContainer').hidden = true;
        document.getElementById('audioPreview').hidden = true;
        document.getElementById('textPreview').hidden = true;
        document.getElementById('unsupportedPreview').hidden = true;

        // 显示视频预览容器
        const videoContainer = document.querySelector('.video-preview-container');
        if (videoContainer) {
            videoContainer.style.display = 'block';
        }

        // 设置视频源
        const videoSources = document.querySelectorAll('video source');
        videoSources.forEach(source => {
            source.src = url;
        });

        // 初始化所有播放器
        this.initVideoPreview({name: url});
    }

    initVideoPreview(file) {
        const videoUrl = this.getPreviewUrl(file);
        const mimeType = this.getVideoMimeType(file.name);

        // 设置所有播放器的视频源
        const videoSources = document.querySelectorAll('video source');
        videoSources.forEach(source => {
            source.src = videoUrl;
            source.type = mimeType;
        });

        // 初始化Video.js播放器
        const videoJsPlayer = this.initVideoJs();

        // 初始化Plyr播放器
        const plyrPlayer = this.initPlyr();

        // 绑定播放器切换事件
        const playerSwitcher = document.querySelector('.player-switcher');
        if (playerSwitcher) {
            playerSwitcher.addEventListener('click', (e) => {
                const button = e.target.closest('button');
                if (button) {
                    const playerType = button.dataset.player;
                    this.switchVideoPlayer(playerType);
                }
            });
        }

        // 默认使用原生播放器
        this.switchVideoPlayer('native');
    }

    // 获取视频MIME类型
    getVideoMimeType(url) {
        const ext = url.split('.').pop().toLowerCase();
        const mimeTypes = {
            'mp4': 'video/mp4',
            'webm': 'video/webm',
            'ogg': 'video/ogg'
        };
        return mimeTypes[ext] || 'video/mp4';
    }

    switchVideoPlayer(playerType) {
        // 停止所有播放器
        this.stopAllPlayers();

        // 隐藏所有播放器容器
        document.querySelector('.video-js-wrapper').style.display = 'none';
        document.querySelector('.plyr-wrapper').style.display = 'none';
        document.getElementById('videoPreview').style.display = 'none';

        // 移除所有按钮的激活状态
        document.querySelectorAll('.player-switcher button').forEach(btn => {
            btn.classList.remove('active');
        });

        // 激活选中的播放器
        const player = this.videoPlayers.get(playerType);
        if (playerType === 'native') {
            const nativePlayer = document.getElementById('videoPreview');
            nativePlayer.style.display = 'block';
        } else if (playerType === 'videojs') {
            const vjsWrapper = document.querySelector('.video-js-wrapper');
            vjsWrapper.style.display = 'flex';
            if (player) {
                player.load();
                setTimeout(() => {
                    player.fill(true);
                }, 100);
            }
        } else if (playerType === 'plyr') {
            const plyrWrapper = document.querySelector('.plyr-wrapper');
            plyrWrapper.style.display = 'flex';
            if (player) {
                player.restart();
            }
        }

        // 更新按钮状态
        const button = document.querySelector(`.player-switcher button[data-player="${playerType}"]`);
        if (button) {
            button.classList.add('active');
        }

        this.currentVideoPlayer = playerType;
    }

    stopAllPlayers() {
        // 停止原生播放器
        const nativePlayer = document.getElementById('videoPreview');
        if (nativePlayer) {
            nativePlayer.pause();
            nativePlayer.currentTime = 0;
        }

        // 停止Video.js播放器
        const videoJsPlayer = this.videoPlayers.get('videojs');
        if (videoJsPlayer) {
            videoJsPlayer.pause();
            videoJsPlayer.currentTime(0);
        }

        // 停止Plyr播放器
        const plyrPlayer = this.videoPlayers.get('plyr');
        if (plyrPlayer) {
            plyrPlayer.pause();
            plyrPlayer.currentTime = 0;
        }
    }

    destroyVideoPlayers() {
        // 停止所有播放器
        this.stopAllPlayers();

        // 销毁Video.js播放器
        const vjsPlayer = this.videoPlayers.get('videojs');
        if (vjsPlayer) {
            vjsPlayer.dispose();
            // 确保完全清理Video.js实例
            if (videojs.getPlayers()['videoJsPlayer']) {
                delete videojs.getPlayers()['videoJsPlayer'];
            }
        }

        // 销毁Plyr播放器
        const plyrPlayer = this.videoPlayers.get('plyr');
        if (plyrPlayer) {
            plyrPlayer.destroy();
        }

        // 重置原生播放器
        const nativePlayer = document.getElementById('videoPreview');
        if (nativePlayer) {
            nativePlayer.pause();
            nativePlayer.currentTime = 0;
            nativePlayer.src = '';
            nativePlayer.load();
        }

        // 重置所有播放器容器
        const vjsWrapper = document.querySelector('.video-js-wrapper');
        const plyrWrapper = document.querySelector('.plyr-wrapper');
        if (vjsWrapper) vjsWrapper.style.display = 'none';
        if (plyrWrapper) plyrWrapper.style.display = 'none';
        if (nativePlayer) nativePlayer.style.display = 'none';

        this.videoPlayers.clear();
        this.currentVideoPlayer = null;
    }

    showAudioPreview(url) {
        const audio = document.getElementById('audioPreview');
        audio.src = url;
        audio.hidden = false;
    }

    async showTextPreview(filename) {
        const text = document.getElementById('textPreview');
        try {
            // 使用专门的文本预览API
            const response = await fetch(`/api/advanced/preview/text/${filename}`);
            if (!response.ok) {
                throw new Error('无法加载文本内容');
            }
            const content = await response.text();
            
            // 创建pre元素来保持文本格式
            text.innerHTML = '';  // 清空现有内容
            const preElement = document.createElement('pre');
            preElement.style.cssText = 'white-space: pre-wrap; word-wrap: break-word; max-height: 500px; overflow-y: auto; width: 100%; margin: 0; padding: 1rem; background: #f8f9fa; border-radius: 4px;';
            preElement.textContent = content;  // 使用textContent避免XSS
            text.appendChild(preElement);
            
            text.hidden = false;
        } catch (error) {
            console.error('Text preview error:', error);
            throw new Error('文本预览失败：' + error.message);
        }
    }

    showUnsupportedPreview() {
        document.getElementById('unsupportedPreview').hidden = false;
    }

    closePreview() {
        // 重置缩放状态
        this.currentZoom = 1;
        const img = document.getElementById('imagePreview');
        if (img) {
            img.style.transform = `scale(1)`;
            img.classList.remove('zoomed');
        }
        
        // 停止所有播放器
        this.stopAllPlayers();
        
        // 销毁所有视频播放器
        this.destroyVideoPlayers();

        // 隐藏视频预览容器
        const videoContainer = document.getElementById('videoPreviewContainer');
        if (videoContainer) {
            videoContainer.hidden = true;
        }

        // 清除其他预览内容
        const imagePreview = document.getElementById('imagePreview');
        if (imagePreview) {
            imagePreview.src = '';
            imagePreview.hidden = true;
        }

        const pdfPreview = document.getElementById('pdfPreview');
        if (pdfPreview) {
            pdfPreview.src = '';
            pdfPreview.hidden = true;
        }

        const audioPreview = document.getElementById('audioPreview');
        if (audioPreview) {
            audioPreview.pause();
            audioPreview.currentTime = 0;
            audioPreview.src = '';
            audioPreview.load();
            audioPreview.hidden = true;
        }

        const textPreview = document.getElementById('textPreview');
        if (textPreview) {
            textPreview.innerHTML = '';
            textPreview.hidden = true;
        }

        // 隐藏所有预览元素
        document.querySelectorAll('.preview-item').forEach(el => {
            el.hidden = true;
        });

        if (this.config.modals.preview) {
            this.config.modals.preview.hide();
        }
        this.currentFile = null;
    }

    onPreviewModalShown() {
        // 如果是视频预览，确保播放器正在初始化
        if (this.currentVideoPlayer) {
            const player = this.videoPlayers.get(this.currentVideoPlayer);
            if (player) {
                if (this.currentVideoPlayer === 'videojs') {
                    player.play();
                } else if (this.currentVideoPlayer === 'plyr') {
                    player.play();
                }
            }
        }
    }

    download(filename) {
        // 使用流式下载替代普通下载
        this.streamDownload(filename);
    }

    downloadCurrent() {
        if (this.currentFile) {
            this.streamDownload(this.currentFile);
        }
    }

    showDeleteModal(filename) {
        this.fileToDelete = filename;
        document.getElementById('deleteFileName').textContent = filename;
        if (this.config.modals.delete) {
            this.config.modals.delete.show();
        }
    }

    closeDeleteModal() {
        if (this.config.modals.delete) {
            this.config.modals.delete.hide();
        }
        this.fileToDelete = null;
    }

    async confirmDelete() {
        if (!this.fileToDelete) return;

        try {
            const response = await fetch(`${this.config.api.delete}/${this.fileToDelete}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            if (result.status === 200) {
                this.showToast('文件删除成功！', 'success');
                // 刷新文件列表和存储信息
                await Promise.all([
                    this.refreshList(),
                    this.updateStorageInfo()
                ]);
            } else {
                throw new Error(result.message || '删除失败');
            }
        } catch (error) {
            this.showToast('删除失败：' + error.message, 'error');
        } finally {
            this.closeDeleteModal();
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <i class="bi bi-${this.getToastIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }

    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'x-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || icons.info;
    }

    formatSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(2)} ${units[unitIndex]}`;
    }

    formatDate(timestamp) {
        const date = new Date(timestamp);
        const options = { 
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        };
        return date.toLocaleString('zh-CN', options);
    }

    formatTime(seconds) {
        if (seconds < 60) {
            return `${Math.round(seconds)}秒`;
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.round(seconds % 60);
            return `${minutes}分${remainingSeconds}秒`;
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}时${minutes}分`;
        }
    }

    async streamDownload(filename) {
        try {
            // 创建下载进度条UI
            const progressDiv = document.createElement('div');
            progressDiv.className = 'download-progress';
            progressDiv.innerHTML = `
                <div class="download-header">
                    <div class="download-title">
                        <i class="bi bi-download"></i>
                        <span>下载中: ${filename}</span>
                    </div>
                    <button class="btn-close" onclick="this.closest('.download-progress').remove()"></button>
                </div>
                <div class="progress">
                    <div class="progress-bar" style="width: 0%"></div>
                </div>
                <div class="progress-status">
                    <span class="speed">0 KB/s</span>
                    <span class="progress-text">0%</span>
                </div>
            `;
            document.querySelector('.toast-container').appendChild(progressDiv);

            const progressBar = progressDiv.querySelector('.progress-bar');
            const progressText = progressDiv.querySelector('.progress-text');
            const speedText = progressDiv.querySelector('.speed');

            // 使用fetch的no-cors模式避免被IDM拦截
            const response = await fetch(`${this.config.api.streamDownload}/${filename}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) throw new Error('下载失败');

            const contentLength = +response.headers.get('Content-Length');
            if (!contentLength) {
                throw new Error('无法获取文件大小信息');
            }

            const reader = response.body.getReader();
            let receivedLength = 0;
            let lastTime = Date.now();
            let lastReceived = 0;

            const chunks = [];
            while(true) {
                const {done, value} = await reader.read();

                if (done) {
                    if (receivedLength === 0) {
                        throw new Error('文件内容为空');
                    }
                    break;
                }

                chunks.push(value);
                receivedLength += value.length;

                const progress = Math.min((receivedLength / contentLength) * 100, 100);
                progressBar.style.width = progress + '%';
                progressText.textContent = Math.round(progress) + '%';

                const currentTime = Date.now();
                const timeElapsed = (currentTime - lastTime) / 1000;
                if (timeElapsed > 0) {
                    const receivedDiff = receivedLength - lastReceived;
                    const speed = receivedDiff / timeElapsed;
                    speedText.textContent = this.formatSize(speed) + '/s';

                    lastTime = currentTime;
                    lastReceived = receivedLength;
                }
            }

            // 合并数据并下载
            const blob = new Blob(chunks);
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            // 下载完成后移除进度条
            setTimeout(() => {
                if (progressDiv && progressDiv.parentNode) {
                    progressDiv.remove();
                }
            }, 1000);

            this.showToast(`文件 ${filename} 下载完成`, 'success');
        } catch (error) {
            console.error('Download error:', error);
            this.showToast('下载失败：' + error.message, 'error');
            // 确保在发生错误时移除进度条
            const progressDiv = document.querySelector('.download-progress');
            if (progressDiv) {
                progressDiv.remove();
            }
        }
    }

    setupSidebarFilters() {
        // 获取所有分类按钮
        const sidebarItems = document.querySelectorAll('.sidebar-nav li');
        
        // 当前选中的过滤器
        this.currentFilter = 'all';

        // 添加点击事件
        sidebarItems.forEach(item => {
            item.addEventListener('click', () => {
                // 移除所有active类
                sidebarItems.forEach(i => i.classList.remove('active'));
                // 添加active类到当前点击的项目
                item.classList.add('active');
                // 设置过滤器并新列表
                this.filterFiles(item.dataset.filter);
            });
        });
    }

    filterFiles(type) {
        this.currentFilter = type;
        this.refreshList();
    }

    getFileType(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const types = {
            image: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            document: ['txt', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
            video: ['mp4', 'webm', 'ogg', 'avi', 'mov'],
            audio: ['mp3', 'wav', 'ogg', 'aac']
        };

        for (const [type, extensions] of Object.entries(types)) {
            if (extensions.includes(ext)) {
                return type;
            }
        }
        return 'other';
    }

    async updateStorageInfo() {
        try {
            const response = await fetch('/api/advanced/storage-info');
            if (!response.ok) {
                throw new Error('Failed to get storage info');
            }
            const result = await response.json();
            if (result.status === 200) {
                this.renderStorageInfo(result.data);
            } else {
                throw new Error(result.message || '获取存储信息失败');
            }
        } catch (error) {
            console.error('Failed to update storage info:', error);
            this.showToast('获取存储信息失败', 'error');
        }
    }

    renderStorageInfo(info) {
        const storageText = document.querySelector('.storage-text .storage-used');
        const progressBar = document.querySelector('.storage-info .progress-bar');
        const storageDetails = document.querySelector('.storage-details span');
        const storageInfo = document.querySelector('.storage-info');
        
        if (storageText && progressBar && storageDetails && storageInfo) {
            // 更新文本和进度条
            storageText.textContent = `${info.usedPercentage}% 已用`;
            progressBar.style.width = `${info.usedPercentage}%`;
            storageDetails.textContent = `${this.formatSize(info.usedSpace)} / ${this.formatSize(info.totalSpace)}`;
            
            // 根据使用百分比更新样式
            storageInfo.classList.remove('warning', 'danger');
            if (info.usedPercentage >= 95) {
                storageInfo.classList.add('danger');
            } else if (info.usedPercentage >= 90) {
                storageInfo.classList.add('warning');
            }
        }
    }

    setupToolbar() {
        // 搜索功能
        const searchInput = document.querySelector('.search-box input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchFiles(e.target.value);
            });
        }

        // 视图切换
        const gridViewBtn = document.querySelector('[title="网格视图"]');
        const listViewBtn = document.querySelector('[title="列表视图"]');
        if (gridViewBtn && listViewBtn) {
            gridViewBtn.addEventListener('click', () => this.switchView('grid'));
            listViewBtn.addEventListener('click', () => this.switchView('list'));
        }

        // 排序功能
        const sortBtn = document.querySelector('[title="排序"]');
        if (sortBtn) {
            sortBtn.addEventListener('click', () => this.toggleSortMenu());
        }
    }

    searchFiles(query) {
        if (!query) {
            this.currentSearchQuery = '';
            this.refreshList();
            return;
        }

        this.currentSearchQuery = query.toLowerCase();
        this.refreshList();
    }

    switchView(viewType) {
        const fileList = document.getElementById('fileList');
        if (!fileList) return;

        if (viewType === 'list') {
            fileList.classList.remove('files-grid');
            fileList.classList.add('files-list');
        } else {
            fileList.classList.remove('files-list');
            fileList.classList.add('files-grid');
        }

        // 保存当前图类型
        this.currentView = viewType;
        // 重新渲染文件列表以适应新视图
        this.refreshList();
    }

    toggleSortMenu() {
        const sortBtn = document.querySelector('[title="排序"]');
        if (!sortBtn) return;

        // 如果排序菜单已存在，则移除它
        const existingMenu = document.querySelector('.sort-menu');
        if (existingMenu) {
            existingMenu.remove();
            return;
        }

        // 创建排序菜单
        const menu = document.createElement('div');
        menu.className = 'sort-menu';
        menu.innerHTML = `
            <div class="sort-item" data-sort="name">
                <i class="bi bi-sort-alpha-down"></i> 按名称排序
            </div>
            <div class="sort-item" data-sort="size">
                <i class="bi bi-sort-numeric-down"></i> 按大小排序
            </div>
            <div class="sort-item" data-sort="date">
                <i class="bi bi-sort-down"></i> 按日期排序
            </div>
        `;

        // 定位菜单
        const rect = sortBtn.getBoundingClientRect();
        menu.style.position = 'absolute';
        menu.style.top = `${rect.bottom + 5}px`;
        menu.style.right = '20px';

        // 添加点击事件
        menu.addEventListener('click', (e) => {
            const sortItem = e.target.closest('.sort-item');
            if (sortItem) {
                const sortBy = sortItem.dataset.sort;
                this.sortFiles(sortBy);
                menu.remove();
            }
        });

        // 添加到文档中
        document.body.appendChild(menu);

        // 点击其他地方关闭菜单
        document.addEventListener('click', (e) => {
            if (!menu.contains(e.target) && !sortBtn.contains(e.target)) {
                menu.remove();
            }
        }, { once: true });
    }

    sortFiles(sortBy) {
        this.currentSortBy = sortBy;
        this.refreshList();
    }

    toggleSelectAll(checked) {
        const files = document.querySelectorAll('.file-card');
        files.forEach(file => {
            const filename = file.querySelector('.file-name').getAttribute('title');
            const checkbox = file.querySelector('.file-checkbox');
            if (checkbox) {
                checkbox.checked = checked;
            }
            if (checked) {
                this.selectedFiles.add(filename);
                file.classList.add('selected');
            } else {
                this.selectedFiles.delete(filename);
                file.classList.remove('selected');
            }
        });
        
        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checked;
        }
        
        this.updateBatchActions();
    }

    toggleFileSelection(filename, checked) {
        const fileCard = document.querySelector(`.file-card[data-filename="${filename}"]`);
        if (fileCard) {
            if (checked) {
                this.selectedFiles.add(filename);
                fileCard.classList.add('selected');
            } else {
                this.selectedFiles.delete(filename);
                fileCard.classList.remove('selected');
            }
        }
        
        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        if (selectAllCheckbox) {
            const totalFiles = document.querySelectorAll('.file-card').length;
            selectAllCheckbox.checked = this.selectedFiles.size === totalFiles;
        }
        
        this.updateBatchActions();
    }

    updateBatchActions() {
        const batchActions = document.querySelector('.batch-actions');
        const selectedCount = document.querySelector('.selected-count');
        if (!batchActions || !selectedCount) return;

        const count = this.selectedFiles.size;
        if (count > 0) {
            batchActions.style.display = 'flex';
            selectedCount.textContent = `已选择 ${count} 个文件`;
        } else {
            batchActions.style.display = 'none';
            selectedCount.textContent = '';
        }
    }

    async downloadSelected() {
        if (this.selectedFiles.size === 0) {
            this.showToast('请先选择要下载的文件', 'warning');
            return;
        }

        try {
            const response = await fetch(`${this.config.api.download}/batch`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(Array.from(this.selectedFiles))
            });

            if (!response.ok) {
                throw new Error('下载失败');
            }

            // 创建下载进度条UI
            const progressDiv = document.createElement('div');
            progressDiv.className = 'download-progress';
            progressDiv.innerHTML = `
                <div class="download-header">
                    <div class="download-title">
                        <i class="bi bi-download"></i>
                        <span>正在下载 ${this.selectedFiles.size} 个文件</span>
                    </div>
                    <button class="btn-close" onclick="this.closest('.download-progress').remove()"></button>
                </div>
                <div class="progress">
                    <div class="progress-bar" style="width: 0%"></div>
                </div>
                <div class="progress-status">
                    <span class="speed">0 KB/s</span>
                    <span class="progress-text">0%</span>
                </div>
            `;
            document.querySelector('.toast-container').appendChild(progressDiv);

            const progressBar = progressDiv.querySelector('.progress-bar');
            const progressText = progressDiv.querySelector('.progress-text');
            const speedText = progressDiv.querySelector('.speed');

            const reader = response.body.getReader();
            const contentLength = +response.headers.get('Content-Length');

            let receivedLength = 0;
            let lastTime = Date.now();
            let lastReceived = 0;
            const chunks = [];

            while(true) {
                const {done, value} = await reader.read();

                if (done) {
                    break;
                }

                chunks.push(value);
                receivedLength += value.length;

                const progress = Math.min((receivedLength / contentLength) * 100, 100);
                progressBar.style.width = progress + '%';
                progressText.textContent = Math.round(progress) + '%';

                const currentTime = Date.now();
                const timeElapsed = (currentTime - lastTime) / 1000;
                if (timeElapsed > 0) {
                    const receivedDiff = receivedLength - lastReceived;
                    const speed = receivedDiff / timeElapsed;
                    speedText.textContent = this.formatSize(speed) + '/s';

                    lastTime = currentTime;
                    lastReceived = receivedLength;
                }
            }

            // 合并数据并下载
            const blob = new Blob(chunks);
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `files_${new Date().getTime()}.zip`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            // 下载完成后移除进度条
            setTimeout(() => {
                if (progressDiv && progressDiv.parentNode) {
                    progressDiv.remove();
                }
            }, 1000);

            this.showToast('文件下载完成', 'success');
        } catch (error) {
            console.error('Download error:', error);
            this.showToast('下载失败：' + error.message, 'error');
        }
    }

    async deleteSelected() {
        if (this.selectedFiles.size === 0) {
            this.showToast('请先选择要删除的文件', 'warning');
            return;
        }

        if (confirm(`确定要删除选中的 ${this.selectedFiles.size} 个文件吗？`)) {
            let successCount = 0;
            let failCount = 0;

            for (const filename of this.selectedFiles) {
                try {
                    const response = await fetch(`${this.config.api.delete}/${filename}`, {
                        method: 'DELETE'
                    });
                    
                    if (response.ok) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (error) {
                    console.error(`Failed to delete ${filename}:`, error);
                    failCount++;
                }
            }

            if (successCount > 0) {
                this.showToast(`成功删除 ${successCount} 个文件`, 'success');
            }
            if (failCount > 0) {
                this.showToast(`${failCount} 个文件删除失败`, 'error');
            }

            this.selectedFiles.clear();
            this.updateBatchActions();
            await this.refreshList();
            // 更新存储信息
            await this.updateStorageInfo();
        }
    }

    setupDragSelection() {
        const fileList = document.getElementById('fileList');
        if (!fileList) return;

        let isSelecting = false;
        let startX = 0;
        let startY = 0;
        let selectionBox = null;
        let initialSelection = new Set();
        let shiftKey = false;
        let ctrlKey = false;

        // 创建选择框
        const createSelectionBox = () => {
            selectionBox = document.createElement('div');
            selectionBox.className = 'selection-box';
            document.body.appendChild(selectionBox);
        };

        // 更新选择框
        const updateSelectionBox = (e) => {
            if (!selectionBox) return;

            const currentX = e.pageX;
            const currentY = e.pageY;

            const left = Math.min(startX, currentX);
            const top = Math.min(startY, currentY);
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            selectionBox.style.left = left + 'px';
            selectionBox.style.top = top + 'px';
            selectionBox.style.width = width + 'px';
            selectionBox.style.height = height + 'px';

            // 检查文件卡片是否在选择框内
            const cards = fileList.querySelectorAll('.file-card');
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const cardCenter = {
                    x: rect.left + rect.width / 2 + window.scrollX,
                    y: rect.top + rect.height / 2 + window.scrollY
                };

                const inSelection = (
                    cardCenter.x >= left && cardCenter.x <= left + width &&
                    cardCenter.y >= top && cardCenter.y <= top + height
                );

                const filename = card.getAttribute('data-filename');
                const checkbox = card.querySelector('.file-checkbox');

                if (inSelection) {
                    if (ctrlKey) {
                        // Ctrl键按下时，切换选择状态
                        if (initialSelection.has(filename)) {
                            this.selectedFiles.delete(filename);
                            card.classList.remove('selected');
                            if (checkbox) checkbox.checked = false;
                        } else {
                            this.selectedFiles.add(filename);
                            card.classList.add('selected');
                            if (checkbox) checkbox.checked = true;
                        }
                    } else {
                        // 正常选择
                        this.selectedFiles.add(filename);
                        card.classList.add('selected');
                        if (checkbox) checkbox.checked = true;
                    }
                } else if (!ctrlKey && !shiftKey) {
                    // 如果不在选择框内且没有按下Ctrl或Shift键，取消选择
                    this.selectedFiles.delete(filename);
                    card.classList.remove('selected');
                    if (checkbox) checkbox.checked = false;
                }
            });

            this.updateBatchActions();
        };

        // 开始选择
        fileList.addEventListener('mousedown', (e) => {
            // 如果点击的是复选框或操作按钮，不启动框选
            if (e.target.closest('.file-checkbox') || e.target.closest('.file-actions')) {
                return;
            }

            isSelecting = true;
            startX = e.pageX;
            startY = e.pageY;
            shiftKey = e.shiftKey;
            ctrlKey = e.ctrlKey;

            // 保存初始选择状态
            initialSelection = new Set(this.selectedFiles);

            // 如果没有按下Ctrl或Shift键，清除现有选择
            if (!ctrlKey && !shiftKey) {
                this.selectedFiles.clear();
                fileList.querySelectorAll('.file-card').forEach(card => {
                    card.classList.remove('selected');
                    const checkbox = card.querySelector('.file-checkbox');
                    if (checkbox) checkbox.checked = false;
                });
            }

            createSelectionBox();
        });

        // 选择过程中
        document.addEventListener('mousemove', (e) => {
            if (!isSelecting) return;
            e.preventDefault();
            updateSelectionBox(e);
        });

        // 结束选择
        document.addEventListener('mouseup', () => {
            if (!isSelecting) return;
            isSelecting = false;
            if (selectionBox && selectionBox.parentNode) {
                selectionBox.parentNode.removeChild(selectionBox);
            }
            selectionBox = null;
        });

        // 防止文本选择
        fileList.addEventListener('selectstart', (e) => {
            if (isSelecting) {
                e.preventDefault();
            }
        });
    }

    getPreviewUrl(file) {
        const ext = file.name.split('.').pop().toLowerCase();
        if (ext === 'pdf') {
            return `/api/advanced/preview/pdf/${file.name}`;
        } else {
            return `/uploads/${file.name}`;  // 直接访问uploads目录下的文件
        }
    }

    // 初始化Video.js播放器
    initVideoJs() {
        if (this.videoPlayers.has('videojs')) {
            return this.videoPlayers.get('videojs');
        }

        const player = videojs('videoJsPlayer', {
            controls: true,
            fluid: false, // 禁用fluid以便我们手动控制尺寸
            responsive: true,
            fill: true, // 让播放器填充容器
            aspectRatio: '16:9',
            controlBar: {
                children: [
                    'playToggle',
                    'volumePanel',
                    'currentTimeDisplay',
                    'timeDivider',
                    'durationDisplay',
                    'progressControl',
                    'playbackRateMenuButton',
                    'fullscreenToggle'
                ]
            },
            playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
            userActions: {
                hotkeys: true
            }
        });

        // 设置默认播放速度
        player.playbackRate(1.0);

        // 监听窗口大小变化
        const resizeObserver = new ResizeObserver(() => {
            player.fill(true); // 使用fill模式自动适应容器大小
        });

        // 观察播放器容器的大小变化
        const playerContainer = document.querySelector('.video-js-wrapper');
        if (playerContainer) {
            resizeObserver.observe(playerContainer);
        }

        // 监听模态框大小变化事件
        const modalDialog = document.querySelector('.preview-modal .modal-dialog');
        if (modalDialog) {
            resizeObserver.observe(modalDialog);
        }

        this.videoPlayers.set('videojs', player);
        return player;
    }

    // 隐藏所有预览内容
    hideAllPreviews() {
        document.getElementById('imagePreview').hidden = true;
        document.getElementById('pdfPreviewContainer').hidden = true;
        document.getElementById('audioPreview').hidden = true;
        document.getElementById('textPreview').hidden = true;
        document.getElementById('unsupportedPreview').hidden = true;
        
        const videoContainer = document.querySelector('.video-preview-container');
        if (videoContainer) {
            videoContainer.style.display = 'none';
        }
        
        // 销毁视频播放器
        this.destroyVideoPlayers();
    }

    // 初始化Plyr播放器
    initPlyr() {
        if (this.videoPlayers.has('plyr')) {
            return this.videoPlayers.get('plyr');
        }

        const player = new Plyr('#plyrPlayer', {
            controls: [
                'play-large',
                'play',
                'progress',
                'current-time',
                'duration',
                'mute',
                'volume',
                'settings',
                'pip',
                'fullscreen'
            ],
            settings: ['quality', 'speed'],
            speed: {
                selected: 1,
                options: [0.5, 0.75, 1, 1.25, 1.5, 2]
            },
            ratio: '16:9',
            resetOnEnd: true,
            keyboard: { focused: true, global: true }
        });

        // 监听容器大小变化
        const resizeObserver = new ResizeObserver(() => {
            // Plyr会自动处理响应式布局
        });

        // 观察播放器容器的大小变化
        const playerContainer = document.querySelector('.plyr-wrapper');
        if (playerContainer) {
            resizeObserver.observe(playerContainer);
        }

        // 监听模态框大小变化事件
        const modalDialog = document.querySelector('.preview-modal .modal-dialog');
        if (modalDialog) {
            resizeObserver.observe(modalDialog);
        }

        this.videoPlayers.set('plyr', player);
        return player;
    }
} 