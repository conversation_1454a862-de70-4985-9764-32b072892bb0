<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>压缩方法比较测试</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: "Microsoft YaHei", <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .card {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            background-color: white;
        }
        .comparison-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .comparison-card {
            flex: 1;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .chart-container {
            height: 300px;
            margin: 20px 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .results-table th, .results-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .results-table th {
            background-color: #f5f5f5;
        }
        .results-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .test-controls {
            margin-bottom: 20px;
        }
        #testProgress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }
        #progressBar {
            width: 0%;
            height: 100%;
            background-color: #4CAF50;
            transition: width 0.3s ease-in-out;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            margin-right: 10px;
        }
        .input-group input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100px;
        }
    </style>
</head>
<body>
    <h1>压缩方法比较测试</h1>

    <div class="test-controls card">
        <h2>测试配置</h2>
        <div class="input-group">
            <label for="testCount">测试次数：</label>
            <input type="number" id="testCount" value="5" min="1" max="20">
            <label for="dataSize">数据大小倍数：</label>
            <input type="number" id="dataSize" value="1000" min="100" max="10000">
            <button onclick="runComparison()" id="startButton">开始比较</button>
            <button onclick="resetComparison()">重置</button>
        </div>
        <div id="testProgress">
            <div id="progressBar"></div>
        </div>
    </div>

    <div class="comparison-container">
        <div class="comparison-card">
            <h3>自动压缩（Spring Boot）</h3>
            <div id="autoStats"></div>
        </div>
        <div class="comparison-card">
            <h3>手动压缩（GZIP）</h3>
            <div id="manualStats"></div>
        </div>
    </div>

    <div class="card">
        <h2>性能比较</h2>
        <div class="chart-container">
            <canvas id="performanceChart"></canvas>
        </div>
    </div>

    <div class="card">
        <h2>压缩比率比较</h2>
        <div class="chart-container">
            <canvas id="ratioChart"></canvas>
        </div>
    </div>

    <div class="card">
        <h2>详细结果</h2>
        <table class="results-table" id="resultsTable">
            <thead>
                <tr>
                    <th>测试编号</th>
                    <th>压缩方法</th>
                    <th>响应时间</th>
                    <th>原始大小</th>
                    <th>压缩后大小</th>
                    <th>压缩比率</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

    <script>
        let testResults = {
            auto: [],
            manual: []
        };
        let charts = {};

        // 初始化图表
        function initializeCharts() {
            const performanceCtx = document.getElementById('performanceChart').getContext('2d');
            const ratioCtx = document.getElementById('ratioChart').getContext('2d');

            charts.performance = new Chart(performanceCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '自动压缩',
                        data: [],
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }, {
                        label: '手动压缩',
                        data: [],
                        backgroundColor: 'rgba(255, 99, 132, 0.5)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '响应时间比较（毫秒）'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            charts.ratio = new Chart(ratioCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '自动压缩',
                        data: [],
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }, {
                        label: '手动压缩',
                        data: [],
                        backgroundColor: 'rgba(255, 99, 132, 0.5)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '压缩比率（%）'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        // 格式化字节大小
        function formatBytes(bytes) {
            if (bytes === 0) return '0 字节';
            const k = 1024;
            const sizes = ['字节', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 更新进度条
        function updateProgress(current, total) {
            const progress = (current / total) * 100;
            document.getElementById('progressBar').style.width = `${progress}%`;
        }

        // 添加结果行
        function addResultRow(testNum, method, responseTime, originalSize, compressedSize, compressionRatio) {
            const tbody = document.getElementById('resultsTable').getElementsByTagName('tbody')[0];
            const row = tbody.insertRow();
            const methodName = method === 'Auto' ? '自动压缩' : '手动压缩';
            row.innerHTML = `
                <td>${testNum}</td>
                <td>${methodName}</td>
                <td>${responseTime.toFixed(2)} 毫秒</td>
                <td>${formatBytes(originalSize)}</td>
                <td>${formatBytes(compressedSize)}</td>
                <td>${compressionRatio.toFixed(2)}%</td>
            `;
        }

        // 更新图表
        function updateCharts() {
            const labels = Array.from({length: testResults.auto.length}, (_, i) => `测试 ${i + 1}`);
            
            charts.performance.data.labels = labels;
            charts.performance.data.datasets[0].data = testResults.auto.map(r => r.responseTime);
            charts.performance.data.datasets[1].data = testResults.manual.map(r => r.responseTime);
            charts.performance.update();

            charts.ratio.data.labels = labels;
            charts.ratio.data.datasets[0].data = testResults.auto.map(r => r.compressionRatio);
            charts.ratio.data.datasets[1].data = testResults.manual.map(r => r.compressionRatio);
            charts.ratio.update();
        }

        // 更新统计信息
        function updateStats() {
            function calculateStats(results) {
                const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
                const avgCompressionRatio = results.reduce((sum, r) => sum + r.compressionRatio, 0) / results.length;
                const avgOriginalSize = results.reduce((sum, r) => sum + r.originalSize, 0) / results.length;
                const avgCompressedSize = results.reduce((sum, r) => sum + r.compressedSize, 0) / results.length;

                return `
                    <p><strong>平均响应时间：</strong> ${avgResponseTime.toFixed(2)} 毫秒</p>
                    <p><strong>平均压缩比率：</strong> ${avgCompressionRatio.toFixed(2)}%</p>
                    <p><strong>平均原始大小：</strong> ${formatBytes(avgOriginalSize)}</p>
                    <p><strong>平均压缩后大小：</strong> ${formatBytes(avgCompressedSize)}</p>
                `;
            }

            if (testResults.auto.length > 0) {
                document.getElementById('autoStats').innerHTML = calculateStats(testResults.auto);
            }
            if (testResults.manual.length > 0) {
                document.getElementById('manualStats').innerHTML = calculateStats(testResults.manual);
            }
        }

        // 测试压缩方法
        async function testCompression(type) {
            const startTime = performance.now();
            const dataSize = document.getElementById('dataSize').value;
            
            try {
                const response = await fetch(`/api/advanced/compressed/${type}`, {
                    headers: {
                        'Accept-Encoding': 'gzip',
                        'Accept': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误！状态码：${response.status}`);
                }
                
                const endTime = performance.now();
                const data = await response.json();
                
                // 获取原始大小和压缩后大小
                const originalSize = parseInt(response.headers.get('X-Original-Size'));
                const compressedSize = parseInt(response.headers.get('X-Compressed-Size'));
                
                if (!originalSize || !compressedSize) {
                    console.warn('压缩信息缺失:', {
                        type,
                        originalSize,
                        compressedSize,
                        headers: Array.from(response.headers.entries())
                    });
                }
                
                const result = {
                    responseTime: endTime - startTime,
                    originalSize: originalSize || 0,
                    compressedSize: compressedSize || 0,
                    compressionRatio: 0
                };
                
                if (result.originalSize && result.compressedSize) {
                    result.compressionRatio = ((1 - (result.compressedSize / result.originalSize)) * 100);
                }
                
                return result;
            } catch (error) {
                console.error(`${type}压缩测试失败:`, error);
                throw error;
            }
        }

        // 运行比较测试
        async function runComparison() {
            const testCount = parseInt(document.getElementById('testCount').value);
            document.getElementById('startButton').disabled = true;
            document.getElementById('progressBar').style.width = '0%';
            
            // 重置结果
            testResults.auto = [];
            testResults.manual = [];
            document.getElementById('resultsTable').getElementsByTagName('tbody')[0].innerHTML = '';

            try {
                for (let i = 0; i < testCount; i++) {
                    // 测试自动压缩
                    const autoResult = await testCompression('auto');
                    testResults.auto.push(autoResult);
                    addResultRow(i + 1, 'Auto', autoResult.responseTime, autoResult.originalSize, 
                        autoResult.compressedSize, autoResult.compressionRatio);

                    // 测试手动压缩
                    const manualResult = await testCompression('manual');
                    testResults.manual.push(manualResult);
                    addResultRow(i + 1, 'Manual', manualResult.responseTime, manualResult.originalSize, 
                        manualResult.compressedSize, manualResult.compressionRatio);

                    updateProgress(i + 1, testCount);
                    updateCharts();
                    updateStats();
                }
            } catch (error) {
                alert('运行测试时出错：' + error.message);
            } finally {
                document.getElementById('startButton').disabled = false;
            }
        }

        // 重置比较
        function resetComparison() {
            testResults.auto = [];
            testResults.manual = [];
            document.getElementById('resultsTable').getElementsByTagName('tbody')[0].innerHTML = '';
            document.getElementById('autoStats').innerHTML = '';
            document.getElementById('manualStats').innerHTML = '';
            document.getElementById('progressBar').style.width = '0%';
            updateCharts();
        }

        // 页面加载时初始化
        window.onload = function() {
            initializeCharts();
        };
    </script>
</body>
</html> 