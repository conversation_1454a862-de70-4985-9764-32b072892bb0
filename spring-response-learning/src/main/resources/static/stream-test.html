<!DOCTYPE html>
<html>
<head>
    <title>Flux Lifecycle Test</title>
    <style>
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .event-log {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
        }
        .event {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .subscribe { background-color: #e3f2fd; }
        .next { background-color: #e8f5e9; }
        .complete { background-color: #fff3e0; }
        .error { background-color: #ffebee; }
        .cancel { background-color: #f3e5f5; }
        .controls {
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        button {
            padding: 10px 20px;
            cursor: pointer;
        }
        .count-input {
            padding: 8px;
            width: 60px;
            margin-right: 10px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            display: none;
        }
        .status.active {
            display: block;
        }
        .status.streaming { background-color: #e3f2fd; }
        .status.completed { background-color: #e8f5e9; }
        .status.error { background-color: #ffebee; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Flux Lifecycle Events Test</h1>
        
        <div class="controls">
            <label>
                Message Count:
                <input type="number" id="messageCount" class="count-input" value="5" min="1" max="100">
            </label>
            <button onclick="startStream()">Start Stream</button>
            <button onclick="cancelStream()">Cancel Stream</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="status" id="streamStatus"></div>
        <div class="event-log" id="eventLog"></div>
    </div>

    <script>
        let eventSource;
        let messageCount = 0;
        let expectedMessages = 5;
        let isClosing = false;

        function updateStatus(type, message) {
            const status = document.getElementById('streamStatus');
            status.className = `status ${type} active`;
            status.textContent = message;
        }

        function startStream() {
            // 重置状态
            isClosing = false;
            messageCount = 0;
            
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }

            const countInput = document.getElementById('messageCount');
            const count = parseInt(countInput.value, 10);
            
            if (isNaN(count) || count < 1 || count > 100) {
                updateStatus('error', 'Invalid message count. Please enter a number between 1 and 100.');
                return;
            }

            expectedMessages = count;
            
            // 立即显示开始消息
            logEvent('subscribe', '');
            
            // 创建EventSource
            eventSource = new EventSource(`/api/advanced/stream?count=${count}`);
            
            // 消息处理
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    logEvent('next', data);
                } catch (e) {
                    logEvent('next', event.data);
                }
            };

            // 错误处理
            eventSource.onerror = function(event) {
                if (eventSource.readyState === EventSource.CLOSED) {
                    if (!isClosing && messageCount < expectedMessages) {
                        logEvent('error', 'Connection lost unexpectedly');
                    }
                }
            };

            // 连接建立时的处理
            eventSource.onopen = function(event) {
                updateStatus('streaming', `Stream is active, waiting for ${expectedMessages} messages...`);
            };
        }

        function cancelStream() {
            if (!eventSource) return;
            
            isClosing = true;
            
            // 延迟关闭连接和显示取消事件，确保先处理所有已接收的消息
            setTimeout(() => {
                if (eventSource) {
                    logEvent('cancel', '');  // 移到这里，确保在所有消息之后显示
                    eventSource.close();
                    eventSource = null;
                }
            }, 1000);
        }

        function logEvent(type, data) {
            const eventLog = document.getElementById('eventLog');
            const event = document.createElement('div');
            event.className = `event ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            let message = `[${timestamp}] `;
            
            switch(type) {
                case 'subscribe':
                    message += `🔔 Stream Started (Expecting ${expectedMessages} messages)`;
                    updateStatus('streaming', `Stream is active, waiting for ${expectedMessages} messages...`);
                    break;
                case 'next':
                    messageCount++;
                    if (typeof data === 'object') {
                        message += `📝 Data Received: ${JSON.stringify(data)}`;
                    } else {
                        message += `📝 Data Received: ${data}`;
                    }
                    updateStatus('streaming', `Received ${messageCount}/${expectedMessages} messages...`);
                    
                    if (messageCount === expectedMessages && !isClosing) {
                        // 延迟显示完成事件，确保在最后一条消息之后
                        setTimeout(() => {
                            handleStreamComplete();
                        }, 100);
                    }
                    break;
                case 'complete':
                    message += '✅ Stream Completed Successfully';
                    updateStatus('completed', 'Stream completed successfully');
                    break;
                case 'error':
                    message += `❌ Error: ${data}`;
                    updateStatus('error', `Stream error: ${data}`);
                    break;
                case 'cancel':
                    message += '⏹ Stream Cancelled by User';
                    updateStatus('cancelled', 'Stream cancelled by user');
                    break;
            }
            
            event.textContent = message;
            eventLog.appendChild(event);
            eventLog.scrollTop = eventLog.scrollHeight;
        }

        function handleStreamComplete() {
            if (eventSource && !isClosing) {
                // 先关闭连接，再显示完成事件
                eventSource.close();
                eventSource = null;
                // 确保完成事件在最后显示
                setTimeout(() => {
                    logEvent('complete', '');
                }, 50);
            }
        }

        function clearLog() {
            const eventLog = document.getElementById('eventLog');
            eventLog.innerHTML = '';
            const status = document.getElementById('streamStatus');
            status.className = 'status';
            messageCount = 0;
            isClosing = false;
            
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
        }
    </script>
</body>
</html> 