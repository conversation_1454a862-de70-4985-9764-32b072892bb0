<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>高级异步任务测试</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
        }
        .card {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease-in-out;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        .status.error {
            background-color: #ffebee;
            color: #c62828;
        }
        .status.info {
            background-color: #e3f2fd;
            color: #1565c0;
        }
        .stream-data {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .data-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .data-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <h1>高级异步任务测试</h1>

    <div class="section card">
        <h2>1. 带进度报告的异步任务</h2>
        <p>这个示例展示了如何实时报告任务进度。</p>
        <button onclick="startProgressTask()" id="progressButton">启动进度任务</button>
        <button onclick="cancelCurrentTask()" id="cancelButton" disabled>取消任务</button>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div id="progressStatus" class="status info">准备就绪</div>
    </div>

    <div class="section card">
        <h2>2. 响应式数据流</h2>
        <p>这个示例展示了如何使用响应式流接收实时数据。</p>
        <button onclick="startDataStream()" id="streamButton">启动数据流</button>
        <div class="stream-data" id="streamData"></div>
    </div>

    <div class="section card">
        <h2>3. 批量异步任务</h2>
        <p>这个示例展示了如何并行处理多个异步任务。</p>
        <div>
            <label for="taskCount">任务数量（1-10）：</label>
            <input type="number" id="taskCount" min="1" max="10" value="5">
        </div>
        <button onclick="startBatchTasks()" id="batchButton">启动批量任务</button>
        <div id="batchStatus" class="status info">准备就绪</div>
    </div>

    <script>
        let currentTaskId = null;
        let eventSource = null;

        // 1. 带进度报告的异步任务
        async function startProgressTask() {
            try {
                const response = await fetch('/api/advanced/async/progress-task', {
                    method: 'POST'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误！状态码：${response.status}`);
                }
                
                const result = await response.json();
                currentTaskId = result.data;
                
                // 启用取消按钮
                document.getElementById('cancelButton').disabled = false;
                document.getElementById('progressButton').disabled = true;
                
                // 监听任务进度
                connectToEventSource(currentTaskId);
                
            } catch (error) {
                console.error('启动任务失败:', error);
                updateStatus('error', `启动任务失败: ${error.message}`);
            }
        }

        function connectToEventSource(taskId) {
            if (eventSource) {
                eventSource.close();
            }

            eventSource = new EventSource(`/api/advanced/async/task-progress/${taskId}`);
            
            eventSource.addEventListener('progress', (event) => {
                const progress = JSON.parse(event.data);
                updateProgress(progress.progress);
                updateStatus('info', progress.message);
            });
            
            eventSource.addEventListener('complete', (event) => {
                updateStatus('success', event.data);
                cleanupTask();
            });
            
            eventSource.addEventListener('cancel', (event) => {
                updateStatus('error', event.data);
                cleanupTask();
            });
            
            eventSource.onerror = () => {
                console.error('SSE连接错误');
                cleanupTask();
            };
        }

        async function cancelCurrentTask() {
            if (!currentTaskId) return;
            
            try {
                const response = await fetch(`/api/advanced/async/cancel-task/${currentTaskId}`, {
                    method: 'DELETE'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误！状态码：${response.status}`);
                }
                
                const result = await response.json();
                updateStatus('info', result.data);
                
            } catch (error) {
                console.error('取消任务失败:', error);
                updateStatus('error', `取消任务失败: ${error.message}`);
            }
        }

        function cleanupTask() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            currentTaskId = null;
            document.getElementById('cancelButton').disabled = true;
            document.getElementById('progressButton').disabled = false;
        }

        function updateProgress(progress) {
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        function updateStatus(type, message) {
            const statusDiv = document.getElementById('progressStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        // 2. 响应式数据流
        async function startDataStream() {
            const streamDiv = document.getElementById('streamData');
            streamDiv.innerHTML = '';
            document.getElementById('streamButton').disabled = true;

            try {
                const response = await fetch('/api/advanced/async/stream-data');
                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const {value, done} = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const items = chunk.split('\n').filter(item => item.trim());
                    
                    items.forEach(item => {
                        try {
                            const data = JSON.parse(item);
                            const div = document.createElement('div');
                            div.className = 'data-item';
                            div.textContent = `${new Date(data.timestamp).toLocaleTimeString()} - ${data.data}`;
                            streamDiv.appendChild(div);
                            streamDiv.scrollTop = streamDiv.scrollHeight;
                        } catch (e) {
                            console.error('解析数据失败:', e);
                        }
                    });
                }
            } catch (error) {
                console.error('数据流错误:', error);
                const div = document.createElement('div');
                div.className = 'data-item status error';
                div.textContent = `错误: ${error.message}`;
                streamDiv.appendChild(div);
            } finally {
                document.getElementById('streamButton').disabled = false;
            }
        }

        // 3. 批量异步任务
        async function startBatchTasks() {
            const count = document.getElementById('taskCount').value;
            const statusDiv = document.getElementById('batchStatus');
            document.getElementById('batchButton').disabled = true;
            
            try {
                const startTime = Date.now();
                statusDiv.className = 'status info';
                statusDiv.textContent = '正在执行批量任务...';
                
                const response = await fetch(`/api/advanced/async/batch-tasks/${count}`, {
                    method: 'POST'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误！状态码：${response.status}`);
                }
                
                const result = await response.json();
                const duration = Date.now() - startTime;
                
                statusDiv.className = 'status success';
                statusDiv.textContent = `完成 ${result.data.totalTasks} 个任务，耗时 ${result.data.duration}ms`;
                
            } catch (error) {
                console.error('批量任务执行失败:', error);
                statusDiv.className = 'status error';
                statusDiv.textContent = `执行失败: ${error.message}`;
            } finally {
                document.getElementById('batchButton').disabled = false;
            }
        }
    </script>
</body>
</html> 