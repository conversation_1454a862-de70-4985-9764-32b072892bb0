package com.example.demo.controller;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.CacheControl;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.concurrent.TimeUnit;

@Controller
public class ResourceController {
    
    @GetMapping("/fonts/bootstrap-icons.woff2")
    @ResponseBody
    public ResponseEntity<Resource> getBootstrapIcons() {
        try {
            Resource resource = new ClassPathResource("static/fonts/bootstrap-icons.woff2");
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf("font/woff2"))
                    .cacheControl(CacheControl.maxAge(365, TimeUnit.DAYS))
                    .body(resource);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }
} 