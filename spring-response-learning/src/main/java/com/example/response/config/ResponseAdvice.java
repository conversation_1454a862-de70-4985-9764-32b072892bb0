package com.example.response.config;

import com.example.response.model.ApiResponse;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@RestControllerAdvice
public class ResponseAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 如果方法返回的类型是 ResponseEntity<String> 或带有 "text/plain" Content-Type，不进行包装
        if (returnType.getParameterType().equals(ResponseEntity.class)) {
            return false; // 跳过处理
        }
        return !ApiResponse.class.isAssignableFrom(returnType.getParameterType());
    }
    @Override
    public Object beforeBodyWrite(Object body, 
                                MethodParameter returnType,
                                MediaType selectedContentType,
                                Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                ServerHttpRequest request,
                                ServerHttpResponse response) {
        // 如果返回值已经是ApiResponse类型，直接返回
        if (body instanceof ApiResponse) {
            return body;
        }

        // 如果是null，返回成功响应
        if (body == null) {
            return ApiResponse.success(null);
        }

        // 其他情况，包装成功响应
        return ApiResponse.success(body);
    }
} 