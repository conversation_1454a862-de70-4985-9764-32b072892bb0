package com.example.response.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.web.reactive.config.ResourceHandlerRegistry;
import org.springframework.web.reactive.config.WebFluxConfigurer;

/**
 * WebFlux 配置类
 * 配置响应式Web应用的编解码器和资源处理
 */
@Configuration
public class WebFluxConfig implements WebFluxConfigurer {
    
    /**
     * 配置HTTP消息编解码器
     * 设置缓冲区大小和其他编解码参数
     * @param configurer 编解码器配置器
     */
    @Override
    public void configureHttpMessageCodecs(ServerCodecConfigurer configurer) {
        configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024); // 设置缓冲区大小为16MB
    }

    /**
     * 配置静态资源处理
     * 主要用于WebFlux环境下的静态资源访问
     * @param registry 资源处理注册器
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
    }
} 