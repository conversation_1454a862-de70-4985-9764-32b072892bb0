package com.example.response.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通用API响应包装类
 * 用于统一所有接口的响应格式
 * @param <T> 响应数据的类型
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)  // 只序列化非空字段
public class ApiResponse<T> {
    /**
     * 请求ID，用于追踪请求
     */
    private final String requestId;
    
    /**
     * 响应时间戳
     */
    private final LocalDateTime timestamp;
    
    /**
     * HTTP状态码
     */
    private final int status;
    
    /**
     * 响应消息
     */
    private final String message;
    
    /**
     * 响应数据
     */
    private final T data;
    
    /**
     * 元数据，用于传递额外信息
     */
    private final Map<String, Object> metadata;

    /**
     * 创建成功响应
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功的API响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .requestId(java.util.UUID.randomUUID().toString())
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.OK.value())
                .message("Success")
                .data(data)
                .build();
    }

    /**
     * 创建错误响应
     * @param status HTTP状态码
     * @param message 错误信息
     * @param <T> 数据类型
     * @return 错误的API响应
     */
    public static <T> ApiResponse<T> error(HttpStatus status, String message) {
        return ApiResponse.<T>builder()
                .requestId(java.util.UUID.randomUUID().toString())
                .timestamp(LocalDateTime.now())
                .status(status.value())
                .message(message)
                .build();
    }
} 