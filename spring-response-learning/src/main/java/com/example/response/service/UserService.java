package com.example.response.service;

import com.example.response.exception.BusinessException;
import com.example.response.exception.ResourceNotFoundException;
import com.example.response.model.UserDTO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Service
public class UserService {
    private final Map<Long, UserDTO> users = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);

    public UserDTO createUser(UserDTO userDTO) {
        // Simulate business validation
        if (users.values().stream().anyMatch(u -> u.getEmail().equals(userDTO.getEmail()))) {
            throw new BusinessException("Email already exists", "DUPLICATE_EMAIL");
        }

        userDTO.setId(idGenerator.getAndIncrement());
        users.put(userDTO.getId(), userDTO);
        return userDTO;
    }

    public UserDTO getUser(Long id) {
        return users.computeIfAbsent(id, key -> {
            throw new ResourceNotFoundException("User", "id", id);
        });
    }

    public List<UserDTO> getAllUsers() {
        return new ArrayList<>(users.values());
    }

    public UserDTO updateUser(Long id, UserDTO userDTO) {
        if (!users.containsKey(id)) {
            throw new ResourceNotFoundException("User", "id", id);
        }

        userDTO.setId(id);
        users.put(id, userDTO);
        return userDTO;
    }

    public void deleteUser(Long id) {
        if (!users.containsKey(id)) {
            throw new ResourceNotFoundException("User", "id", id);
        }
        users.remove(id);
    }
} 