package com.example.response.controller;

import com.example.response.model.ApiResponse;
import com.example.response.model.TaskProgress;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.time.Duration;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;

@Slf4j
@RestController
@RequestMapping("/api/advanced/async")
public class AdvancedAsyncController {

    private final ConcurrentHashMap<String, CompletableFuture<Void>> taskMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, SseEmitter> emitterMap = new ConcurrentHashMap<>();
    private final ApplicationEventPublisher eventPublisher;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    public AdvancedAsyncController(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    /**
     * 1. 带进度报告的异步任务
     * 使用Server-Sent Events (SSE)实时报告任务进度
     */
    @PostMapping("/progress-task")
    public ApiResponse<String> startProgressTask() {
        String taskId = UUID.randomUUID().toString();
        SseEmitter emitter = new SseEmitter(180_000L); // 3分钟超时
        emitterMap.put(taskId, emitter);

        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                for (int i = 0; i <= 100; i += 10) {
                    if (Thread.interrupted()) {
                        emitter.send(SseEmitter.event()
                            .name("cancel")
                            .data("任务已取消"));
                        return;
                    }
                    
                    TaskProgress progress = new TaskProgress(taskId, i, "处理中...");
                    emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(progress));
                    
                    Thread.sleep(500); // 模拟处理时间
                }
                
                emitter.send(SseEmitter.event()
                    .name("complete")
                    .data("任务完成"));
                
            } catch (Exception e) {
                log.error("Progress task error", e);
            } finally {
                emitter.complete();
                emitterMap.remove(taskId);
            }
        }, executorService);

        taskMap.put(taskId, future);
        return ApiResponse.success(taskId);
    }

    /**
     * 获取任务进度的SSE端点
     */
    @GetMapping(value = "/task-progress/{taskId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter getTaskProgress(@PathVariable String taskId) {
        SseEmitter emitter = emitterMap.get(taskId);
        if (emitter == null) {
            throw new IllegalArgumentException("Task not found: " + taskId);
        }
        return emitter;
    }

    /**
     * 2. 可取消的异步任务
     */
    @DeleteMapping("/cancel-task/{taskId}")
    public ApiResponse<String> cancelTask(@PathVariable String taskId) {
        CompletableFuture<Void> future = taskMap.get(taskId);
        if (future != null) {
            future.cancel(true);
            taskMap.remove(taskId);
            return ApiResponse.success("任务已取消");
        }
        return ApiResponse.error(HttpStatus.NOT_FOUND, "任务不存在");
    }

    /**
     * 3. 带返回值的异步任务
     * 使用Flux实现响应式数据流
     */
    @GetMapping(value = "/stream-data", produces = MediaType.APPLICATION_NDJSON_VALUE)
    public Flux<Map<String, Object>> streamData() {
        return Flux.interval(Duration.ofSeconds(1))
            .take(5)
            .map(i -> {
                String data = "数据包 #" + i;
                return Map.of(
                    "timestamp", System.currentTimeMillis(),
                    "data", data,
                    "sequence", i
                );
            });
    }

    /**
     * 4. 批量异步任务处理
     */
    @PostMapping("/batch-tasks/{count}")
    public ApiResponse<Map<String, Object>> executeBatchTasks(@PathVariable int count) {
        if (count <= 0 || count > 10) {
            return ApiResponse.error(HttpStatus.BAD_REQUEST, "任务数量必须在1-10之间");
        }

        long startTime = System.currentTimeMillis();
        
        CompletableFuture<String>[] futures = new CompletableFuture[count];
        for (int i = 0; i < count; i++) {
            final int taskId = i;
            futures[i] = CompletableFuture.supplyAsync(() -> {
                try {
                    // 模拟不同任务的处理时间
                    Thread.sleep(1000 + (long)(Math.random() * 2000));
                    return "任务" + taskId + "完成";
                } catch (InterruptedException e) {
                    throw new CompletionException(e);
                }
            }, executorService);
        }

        try {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures);
            allOf.get(30, TimeUnit.SECONDS); // 设置30秒超时
            
            long duration = System.currentTimeMillis() - startTime;
            
            return ApiResponse.success(Map.of(
                "totalTasks", count,
                "duration", duration,
                "status", "所有任务已完成"
            ));
        } catch (Exception e) {
            return ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR, "批量任务执行失败: " + e.getMessage());
        }
    }
} 