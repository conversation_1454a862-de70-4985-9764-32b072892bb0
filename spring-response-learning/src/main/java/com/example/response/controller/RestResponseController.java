package com.example.response.controller;

import com.example.response.model.ApiResponse;
import com.example.response.model.UserDTO;
import com.example.response.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class RestResponseController {

    private final UserService userService;

    // Example of using ResponseEntity with custom response wrapper
    @PostMapping
    public ResponseEntity<ApiResponse<UserDTO>> createUser(@Valid @RequestBody UserDTO userDTO) {
        UserDTO createdUser = userService.createUser(userDTO);
        return ResponseEntity
                .status(HttpStatus.CREATED)
                .body(ApiResponse.success(createdUser));
    }

    // Example of using @ResponseStatus with direct return
    @GetMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<UserDTO> getUser(@PathVariable Long id) {
        return ApiResponse.success(userService.getUser(id));
    }

    // Example of using ResponseEntity with collection
    @GetMapping
    public ResponseEntity<ApiResponse<List<UserDTO>>> getAllUsers() {
        List<UserDTO> users = userService.getAllUsers();
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    // Example of using ResponseEntity with no content
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.noContent().build();
    }

    // Example of using ResponseEntity with conditional response
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<UserDTO>> updateUser(
            @PathVariable Long id,
            @Valid @RequestBody UserDTO userDTO) {
        UserDTO updatedUser = userService.updateUser(id, userDTO);
        return ResponseEntity.ok(ApiResponse.success(updatedUser));
    }

    // Example of custom status code response
    @PostMapping("/{id}/activate")
    @ResponseStatus(HttpStatus.ACCEPTED)
    public ApiResponse<String> activateUser(@PathVariable Long id) {
        // Simulate async activation
        return ApiResponse.success("User activation initiated");
    }

    // Example of custom error response
    @GetMapping("/{id}/profile-image")
    public ResponseEntity<byte[]> getUserProfileImage(@PathVariable Long id) {
        // Simulate missing image
        throw new com.example.response.exception.ResourceNotFoundException(
                "ProfileImage", "userId", id);
    }

    // Example of 201 CREATED status
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<UserDTO>> registerUser(@Valid @RequestBody UserDTO userDTO) {
        UserDTO createdUser = userService.createUser(userDTO);
        return ResponseEntity
                .status(HttpStatus.CREATED)
                .body(ApiResponse.success(createdUser));
    }

    // Example of 202 ACCEPTED status
    @PostMapping("/process")
    public ResponseEntity<ApiResponse<String>> processLongRunningTask() {
        return ResponseEntity
                .status(HttpStatus.ACCEPTED)
                .body(ApiResponse.success("Task has been accepted for processing"));
    }

    // Example of 204 NO_CONTENT status
    @DeleteMapping("/{id}/soft")
    public ResponseEntity<Void> softDeleteUser(@PathVariable Long id) {
        // Soft delete implementation
        return ResponseEntity.noContent().build();
    }

    // Example of 400 BAD_REQUEST status
    @PostMapping("/validate")
    public ResponseEntity<ApiResponse<String>> validateUser(@RequestBody UserDTO userDTO) {
        if (userDTO.getUsername() == null || userDTO.getUsername().isEmpty()) {
            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(HttpStatus.BAD_REQUEST, "Username cannot be empty"));
        }
        return ResponseEntity.ok(ApiResponse.success("Validation passed"));
    }

    // Example of 403 FORBIDDEN status
    @GetMapping("/admin")
    public ResponseEntity<ApiResponse<String>> adminOnly() {
        return ResponseEntity
                .status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(HttpStatus.FORBIDDEN, "Access denied"));
    }

    // Example of 409 CONFLICT status
    @PostMapping("/check-conflict")
    public ResponseEntity<ApiResponse<String>> checkConflict(@RequestBody UserDTO userDTO) {
        if (userDTO.getEmail().equals("<EMAIL>")) {
            return ResponseEntity
                    .status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error(HttpStatus.CONFLICT, "Email already exists"));
        }
        return ResponseEntity.ok(ApiResponse.success("No conflict found"));
    }

    // Example of 500 INTERNAL_SERVER_ERROR status
    @GetMapping("/error-test")
    public ResponseEntity<ApiResponse<String>> testError() {
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR, "Something went wrong"));
    }

    // Example of 503 SERVICE_UNAVAILABLE status
    @GetMapping("/maintenance")
    public ResponseEntity<ApiResponse<String>> maintenance() {
        return ResponseEntity
                .status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ApiResponse.error(HttpStatus.SERVICE_UNAVAILABLE, "System is under maintenance"));
    }
} 