package com.example.response.controller;

import com.example.response.model.UserDTO;
import com.example.response.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Controller
@RequestMapping("/users")
@RequiredArgsConstructor
public class ViewResponseController {

    private final UserService userService;

    // Example of returning view name
    @GetMapping
    public String listUsers(Model model) {
        model.addAttribute("users", userService.getAllUsers());
        return "users/list";
    }

    // Example of using ModelAndView
    @GetMapping("/{id}")
    public ModelAndView viewUser(@PathVariable Long id) {
        ModelAndView mav = new ModelAndView("users/view");
        mav.addObject("user", userService.getUser(id));
        return mav;
    }

    // Example of form handling with validation
    @GetMapping("/new")
    public String newUserForm(Model model) {
        model.addAttribute("user", new UserDTO());
        return "users/form";
    }

    // Example of PRG pattern and flash attributes
    @PostMapping("/new")
    public String createUser(
            @Valid @ModelAttribute("user") UserDTO userDTO,
            BindingResult result,
            RedirectAttributes redirectAttributes) {
        
        if (result.hasErrors()) {
            return "users/form";
        }

        UserDTO createdUser = userService.createUser(userDTO);
        redirectAttributes.addFlashAttribute("message", "User created successfully!");
        return "redirect:/users/" + createdUser.getId();
    }

    // Example of handling exceptions with custom error view
    @GetMapping("/{id}/edit")
    public String editUserForm(@PathVariable Long id, Model model) {
        model.addAttribute("user", userService.getUser(id));
        return "users/form";
    }

    // Example of conditional view selection
    @PostMapping("/{id}")
    public String updateUser(
            @PathVariable Long id,
            @Valid @ModelAttribute("user") UserDTO userDTO,
            BindingResult result,
            RedirectAttributes redirectAttributes) {

        if (result.hasErrors()) {
            return "users/form";
        }

        userService.updateUser(id, userDTO);
        redirectAttributes.addFlashAttribute("message", "User updated successfully!");
        return "redirect:/users/" + id;
    }

    // Example of void return type with implicit view name
    @GetMapping("/dashboard")
    public void dashboard(Model model) {
        // Spring will automatically use "dashboard" as the view name
        model.addAttribute("stats", "Some user statistics");
    }

    // Test endpoint to trigger error page
    @GetMapping("/test-error")
    public String testError() {
        throw new RuntimeException("This is a test error message");
    }
} 