package com.example.response.controller;

import com.example.response.model.StorageInfo;
import com.example.response.model.ApiResponse;
import com.example.response.model.FileInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import reactor.core.publisher.Flux;

import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ForkJoinPool;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.zip.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.io.FileNotFoundException;
import java.net.URLEncoder;

/**
 * 高级响应处理控制器
 * 展示Spring中各种高级响应处理方式
 */
@Slf4j
@RestController
@RequestMapping("/api/advanced")
@RequiredArgsConstructor
public class AdvancedResponseController {

    @Value("${file.upload.dir:uploads}")
    private String uploadDir;

    private final ObjectMapper objectMapper;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    /**
     * 1. 异步响应处理示例
     * 使用DeferredResult实现非阻塞异步处理
     * @return 延迟的响应结果
     */
    @GetMapping("/async/deferred")
    public DeferredResult<ApiResponse<String>> getDeferredResult() {
        DeferredResult<ApiResponse<String>> deferredResult = new DeferredResult<>(60000L); // 60秒超时
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(1000); // 模拟耗时操作
                deferredResult.setResult(ApiResponse.success("Deferred result after 1 second"));
            } catch (InterruptedException e) {
                deferredResult.setErrorResult(
                    ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR, "Processing failed")
                );
            }
        });
        return deferredResult;
    }

    /**
     * 2. 响应流处理示例 - Server-Sent Events (SSE)
     * 该方法展示了Flux的完整生命周期和各事件回调
     * 
     * 生命周期顺序：
     * 1. doOnSubscribe: 当客户端订阅时触发
     * 2. doOnNext: 每次发送数据时触发
     * 3. doOnComplete: 当所有数据发送完成时触发
     * 4. doOnCancel: 当客户端取消订阅时触发
     * 5. doOnError: 当发生错误时触发
     * 6. doFinally: 在流结束时触发（无论是完成、取消还是错误）
     *
     * @param count 要发送的消息数量，默认为5
     * @return Flux<ApiResponse<String>> 返回一个包含ApiResponse的响应流
     */
    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ApiResponse<String>> streamData(@RequestParam(defaultValue = "5") int count) {
        // 验证参数
        if (count <= 0) {
            return Flux.error(new IllegalArgumentException("Count must be greater than 0"));
        }
        if (count > 100) {
            return Flux.error(new IllegalArgumentException("Count cannot be greater than 100"));
        }

        return Flux
                // 使用range替代interval，这可以精确控制序号
                .range(0, count)
                // 添加延时
                .delayElements(Duration.ofSeconds(1))
                // 将每个元素转换为所需的响应格式
                .map(i -> {
                    String data = String.format("Event %d of %d at %s", i, count, LocalDateTime.now());
                    System.out.println("Sending: " + data);
                    return ApiResponse.success(data);
                })
                // 订阅事件：当客户端连接时触发
                .doOnSubscribe(subscription -> {
                    System.out.println("Client subscribed to stream at " + LocalDateTime.now() + 
                                     ", requesting " + count + " messages");
                })
                // 数据事件：每次发送数据前触发
                .doOnNext(data -> {
                    System.out.println("Emitting event at " + LocalDateTime.now() + ": " + data.getData());
                })
                // 完成事件：当所有数据发送完毕时触发
                .doOnComplete(() -> {
                    System.out.println("Stream completed at " + LocalDateTime.now() + 
                                     ", sent " + count + " messages");
                })
                // 错误事件：当发生异常时触发
                .doOnError(e -> {
                    System.err.println("Stream error at " + LocalDateTime.now() + ": " + e.getMessage());
                    e.printStackTrace();
                })
                // 取消事件：当客户端断开连接时触发
                .doOnCancel(() -> {
                    System.out.println("Stream cancelled by client at " + LocalDateTime.now());
                })
                // 最终事件：流结束时触发，无论是完成、取消还是错误
                .doFinally(signalType -> {
                    System.out.println("Stream finalized with signal: " + signalType + " at " + LocalDateTime.now());
                });
    }

    /**
     * 3. 条件响应示例
     * 使用ETag实现缓存控制
     * @return 带有ETag的响应
     */
    @GetMapping("/conditional")
    public ResponseEntity<ApiResponse<String>> getWithETag(
            @RequestHeader(value = "If-None-Match", required = false) String ifNoneMatch) {
        // 实际内容 - 在实际应用中，这可能是从数据库获取的数据
        String content = "This response supports caching";
        
        // 使用更稳定的方式生成ETag
        String etag = "\"" + content.hashCode() + "\"";  // 添加引号是HTTP规范要求
        
        // 如果客户端发送ETag匹配，返回304 Not Modified
        if (etag.equals(ifNoneMatch)) {
            return ResponseEntity.status(HttpStatus.NOT_MODIFIED)
                    .eTag(etag)
                    .build();
        }
        
        ApiResponse<String> response = ApiResponse.success(content);
        
        return ResponseEntity.ok()
                .eTag(etag)
                .cacheControl(CacheControl.maxAge(Duration.ofSeconds(30)))
                .body(response);
    }

    /**
     * 计算文件的MD5值
     * @param file 文件
     * @return MD5值
     */
    private String calculateMD5(MultipartFile file) throws IOException {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            try (InputStream is = file.getInputStream()) {
                byte[] buffer = new byte[8192];
                int read;
                while ((read = is.read(buffer)) > 0) {
                    md.update(buffer, 0, read);
                }
            }
            byte[] md5Bytes = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new IOException("Failed to calculate MD5", e);
        }
    }

    /**
     * 检查文件是否已存在
     * @param uploadPath 上传目录
     * @param newFileMd5 新文件的MD5
     * @return 已存在的文件名，如果不存在则返回null
     */
    private String checkFileExists(Path uploadPath, String newFileMd5) throws IOException {
        if (Files.exists(uploadPath)) {
            try (Stream<Path> files = Files.list(uploadPath)) {
                return files.filter(Files::isRegularFile)
                        .filter(path -> {
                            try (InputStream is = Files.newInputStream(path)) {
                                String existingMd5 = calculateMD5(is);
                                return existingMd5.equals(newFileMd5);
                            } catch (IOException e) {
                                return false;
                            }
                        })
                        .map(path -> path.getFileName().toString())
                        .findFirst()
                        .orElse(null);
            }
        }
        return null;
    }

    /**
     * 计算输入流的MD5值
     * @param is 输入流
     * @return MD5值
     */
    private String calculateMD5(InputStream is) throws IOException {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int read;
            while ((read = is.read(buffer)) > 0) {
                md.update(buffer, 0, read);
            }
            byte[] md5Bytes = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new IOException("Failed to calculate MD5", e);
        }
    }

    /**
     * 4. 文件上传处理示例
     * @param file 上传的文件
     * @return 上传结果
     */
    @PostMapping("/upload")
    public ApiResponse<String> handleFileUpload(@RequestParam("file") MultipartFile file) {
        try {
            // 创建上传目录
            Path uploadPath = Paths.get("uploads");
            Files.createDirectories(uploadPath);

            // 计算文件MD5
            String fileMd5 = calculateMD5(file);
            
            // 检查文件是否已存在
            String existingFile = checkFileExists(uploadPath, fileMd5);
            if (existingFile != null) {
                return ApiResponse.error(HttpStatus.CONFLICT, 
                    String.format("文件已存在: %s（MD5: %s）", existingFile, fileMd5));
            }

            // 保存文件
            Path filePath = uploadPath.resolve(file.getOriginalFilename());
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            
            return ApiResponse.success(String.format("文件上传成功: %s（MD5: %s）", 
                file.getOriginalFilename(), fileMd5));
        } catch (IOException e) {
            return ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR, 
                "文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 5. 文件下载示例
     * 支持断点续传
     * @param filename 文件名
     * @param headers 请求头
     * @return 文件资源
     */
    @GetMapping("/download/{filename}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String filename,
                                               @RequestHeader(required = false) HttpHeaders headers) {
        try {
            Path filePath = Paths.get("uploads").resolve(filename);
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists()) {
                HttpHeaders responseHeaders = new HttpHeaders();
                responseHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                responseHeaders.setContentDisposition(
                    ContentDisposition.attachment().filename(filename).build()
                );

                return ResponseEntity.ok()
                        .headers(responseHeaders)
                        .body(resource);
            }
            return ResponseEntity.notFound().build();
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 6. 大文件式下载示例
     * 使用流式处理避免内存溢出
     * @param filename 文件名
     * @return 流式响应
     */
    @GetMapping("/stream-download/{filename}")
    public ResponseEntity<StreamingResponseBody> streamDownload(
            @PathVariable String filename,
            @RequestHeader(value = "Range", required = false) String rangeHeader) {
        
        Path filePath = Paths.get("uploads").resolve(filename);
        
        if (!Files.exists(filePath)) {
            return ResponseEntity.notFound().build();
        }

        try {
            long fileSize = Files.size(filePath);
            if (fileSize == 0) {
                return ResponseEntity.badRequest().build();
            }

            // 解析Range头
            final long startPosition;
            final long endPosition;
            boolean isRangeRequest = false;

            if (rangeHeader != null && rangeHeader.startsWith("bytes=")) {
                isRangeRequest = true;
                String[] ranges = rangeHeader.substring(6).split("-");
                if (ranges.length > 0) {
                    startPosition = Long.parseLong(ranges[0]);
                    if (ranges.length > 1 && !ranges[1].isEmpty()) {
                        endPosition = Long.parseLong(ranges[1]);
                    } else {
                        endPosition = fileSize - 1;
                    }
                } else {
                    startPosition = 0;
                    endPosition = fileSize - 1;
                }
            } else {
                startPosition = 0;
                endPosition = fileSize - 1;
            }

            // 计算实际要传输的内容长度
            final long contentLength = endPosition - startPosition + 1;

            // 创建流式响应
            StreamingResponseBody responseBody = outputStream -> {
                try (var inputStream = Files.newInputStream(filePath)) {
                    // 跳过已下载的部分
                    inputStream.skip(startPosition);
                    
                    byte[] buffer = new byte[8192];
                    long remaining = contentLength;
                    int read;
                    
                    while (remaining > 0 && (read = inputStream.read(buffer, 0, (int) Math.min(buffer.length, remaining))) != -1) {
                        outputStream.write(buffer, 0, read);
                        remaining -= read;
                    }
                }
            };

            // 对文件名进行URL编码，解决中文问题
            String encodedFilename = java.net.URLEncoder.encode(filename, StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.set(HttpHeaders.CONTENT_DISPOSITION, 
                String.format("attachment; filename=\"%s\"; filename*=UTF-8''%s",
                    filename.replaceAll("[^\\x00-\\x7F]", "_"),
                    encodedFilename));
            
            // 添加断点续传相关响应头
            headers.set(HttpHeaders.ACCEPT_RANGES, "bytes");
            if (isRangeRequest) {
                headers.set(HttpHeaders.CONTENT_RANGE, 
                    String.format("bytes %d-%d/%d", startPosition, endPosition, fileSize));
                headers.setContentLength(contentLength);
                return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                        .headers(headers)
                        .body(responseBody);
            } else {
                headers.setContentLength(fileSize);
                return ResponseEntity.ok()
                        .headers(headers)
                        .body(responseBody);
            }

        } catch (IOException e) {
            log.error("Error preparing file for streaming: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 7.1 使用Spring Boot内置压缩功能
     * Spring Boot会自动处理压缩
     * @return 压缩的响应
     */
    @GetMapping(value = "/compressed/auto", produces = "application/json")
    public ResponseEntity<ApiResponse<String>> getAutoCompressedResponse() {
        StringBuilder largeContent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            largeContent.append("This is a large content that will benefit from compression. ");
        }

        ApiResponse<String> response = ApiResponse.success(largeContent.toString());
        
        // 计算原始大小
        String jsonString;
        try {
            jsonString = objectMapper.writeValueAsString(response);
            byte[] originalBytes = jsonString.getBytes(StandardCharsets.UTF_8);
            
            // 手动计算GZIP压缩后的大小
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream) {
                {
                    def.setLevel(Deflater.BEST_COMPRESSION);
                }
            }) {
                gzipOutputStream.write(originalBytes);
            }
            byte[] compressedBytes = byteArrayOutputStream.toByteArray();
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-Original-Size", String.valueOf(originalBytes.length));
            headers.set("X-Compressed-Size", String.valueOf(compressedBytes.length));
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(response);
        } catch (Exception e) {
            log.error("Error in auto compression: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 7.2 手动GZIP压缩处理
     * 手动控制压缩过程
     * @return 压缩的响应
     */
    @GetMapping(value = "/compressed/manual", produces = "application/json")
    public ResponseEntity<byte[]> getManualCompressedResponse() {
        try {
            // 1. 准备大量数据
            StringBuilder largeContent = new StringBuilder();
            for (int i = 0; i < 1000; i++) {
                largeContent.append("This is a large content that will benefit from compression. ");
            }

            ApiResponse<String> response = ApiResponse.success(largeContent.toString());
            
            // 2. 将对象转换为JSON字符串
            String jsonString = objectMapper.writeValueAsString(response);
            
            // 3. 使用GZIP压缩
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream) {
                {
                    def.setLevel(Deflater.BEST_COMPRESSION); // 设置最高压缩级别
                }
            }) {
                gzipOutputStream.write(jsonString.getBytes(StandardCharsets.UTF_8));
            }
            
            // 4. 获取压缩后的字节数组
            byte[] compressedData = byteArrayOutputStream.toByteArray();
            
            // 5. 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set(HttpHeaders.CONTENT_ENCODING, "gzip");
            headers.setContentLength(compressedData.length);
            
            // 6. 添加自定义响应头，用于显示压缩信息
            headers.set("X-Original-Size", String.valueOf(jsonString.getBytes(StandardCharsets.UTF_8).length));
            headers.set("X-Compressed-Size", String.valueOf(compressedData.length));
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(compressedData);
        } catch (Exception e) {
            log.error("Error compressing response: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 8. 异步任务执行示例
     * 使用CompletableFuture实现异步处理
     * @return 异步处理结果
     */
    @PostMapping("/async-task")
    public CompletableFuture<ApiResponse<String>> executeAsyncTask() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(2000); // 模拟耗时操作
                return ApiResponse.success("Async task completed");
            } catch (InterruptedException e) {
                return ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR, "Async task failed");
            }
        }, ForkJoinPool.commonPool());
    }

    /**
     * 获取上传的文件列表
     * @return 文件列表
     */
    @GetMapping("/files")
    public ApiResponse<List<FileInfo>> getFileList() {
        try {
            Path uploadPath = Paths.get("uploads");
            // 如果目录不存在，创建它
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            
            // 获取文件列表
            List<FileInfo> files = Files.list(uploadPath)
                    .filter(Files::isRegularFile)
                    .map(path -> {
                        try {
                            return new FileInfo(
                                path.getFileName().toString(),
                                Files.size(path),
                                Files.getLastModifiedTime(path).toMillis()
                            );
                        } catch (IOException e) {
                            return null;
                        }
                    })
                    .filter(file -> file != null)
                    .collect(Collectors.toList());
            
            return ApiResponse.success(files);
        } catch (IOException e) {
            return ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to get file list");
        }
    }

    /**
     * 删除文件
     * @param filename 要删除的文件名
     * @return 删除结果
     */
    @DeleteMapping("/files/{filename}")
    public ApiResponse<String> deleteFile(@PathVariable String filename) {
        try {
            Path filePath = Paths.get("uploads").resolve(filename);
            if (!Files.exists(filePath)) {
                return ApiResponse.error(HttpStatus.NOT_FOUND, "File not found: " + filename);
            }
            
            Files.delete(filePath);
            return ApiResponse.success("File deleted successfully: " + filename);
        } catch (IOException e) {
            return ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to delete file: " + e.getMessage());
        }
    }

    @GetMapping("/preview/text/{filename}")
    public ResponseEntity<String> previewTextFile(@PathVariable String filename) {
        try {
            Path filePath = Paths.get(uploadDir, filename);
            if (!Files.exists(filePath)) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("File not found");
            }

            // 读取文件内容为字符串，设置为 UTF-8 编码
            String content = Files.readString(filePath, StandardCharsets.UTF_8);

            // 返回原始文本内容，Content-Type 为 text/plain
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_PLAIN)
                    .body(content);
        } catch (IOException e) {
            log.error("Failed to read file content", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to read file content");
        }
    }

    /**
     * 获取存储空间信息
     * @return 存储空间信息
     */
    @GetMapping("/storage-info")
    public ApiResponse<StorageInfo> getStorageInfo() {
        try {
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // 计算已使用空间
            long usedSpace = 0;
            try (Stream<Path> walk = Files.walk(uploadPath)) {
                usedSpace = walk.filter(Files::isRegularFile)
                        .mapToLong(path -> {
                            try {
                                return Files.size(path);
                            } catch (IOException e) {
                                return 0L;
                            }
                        })
                        .sum();
            }

            // 获取总空间
            File store = uploadPath.toFile();
            long totalSpace = store.getTotalSpace();
            
            // 计算使用百分比
            double usedPercentage = totalSpace > 0 
                ? ((double) usedSpace / totalSpace) * 100 
                : 0;

            StorageInfo info = new StorageInfo();
            info.setTotalSpace(totalSpace);
            info.setUsedSpace(usedSpace);
            info.setUsedPercentage(Math.round(usedPercentage * 100.0) / 100.0);

            return ApiResponse.success(info);
        } catch (IOException e) {
            return ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to get storage info");
        }
    }

    /**
     * 多文件上传处理
     * @param files 上传的文件列表
     * @return 上传结果
     */
    @PostMapping("/upload/batch")
    public ApiResponse<List<Map<String, String>>> handleBatchFileUpload(@RequestParam("files") List<MultipartFile> files) {
        List<Map<String, String>> results = new ArrayList<>();
        
        try {
            // 创建上传目录
            Path uploadPath = Paths.get("uploads");
            Files.createDirectories(uploadPath);

            for (MultipartFile file : files) {
                Map<String, String> result = new HashMap<>();
                result.put("filename", file.getOriginalFilename());
                
                try {
                    // 计算文件MD5
                    String fileMd5 = calculateMD5(file);
                    
                    // 检查文件是否已存在
                    String existingFile = checkFileExists(uploadPath, fileMd5);
                    if (existingFile != null) {
                        result.put("status", "error");
                        result.put("message", String.format("文件已存在: %s（MD5: %s）", existingFile, fileMd5));
                    } else {
                        // 保存文件
                        Path filePath = uploadPath.resolve(file.getOriginalFilename());
                        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
                        
                        result.put("status", "success");
                        result.put("message", String.format("上传成功（MD5: %s）", fileMd5));
                    }
                } catch (IOException e) {
                    result.put("status", "error");
                    result.put("message", "上传失败: " + e.getMessage());
                }
                
                results.add(result);
            }
            
            return ApiResponse.success(results);
        } catch (IOException e) {
            return ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR, "批量上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量下载文件
     * 将多个文件打包成ZIP文件下载
     * @param filenames 要下载的文件名列表
     * @return ZIP文件流
     */
    @PostMapping("/download/batch")
    public ResponseEntity<StreamingResponseBody> batchDownload(@RequestBody List<String> filenames) {
        Path uploadPath = Paths.get("uploads");
        
        // 验证所有文件是否存在
        for (String filename : filenames) {
            if (!Files.exists(uploadPath.resolve(filename))) {
                return ResponseEntity.notFound().build();
            }
        }

        StreamingResponseBody responseBody = outputStream -> {
            try (ZipOutputStream zipOut = new ZipOutputStream(outputStream)) {
                for (String filename : filenames) {
                    Path filePath = uploadPath.resolve(filename);
                    
                    // 创建ZIP条目
                    ZipEntry zipEntry = new ZipEntry(filename);
                    zipOut.putNextEntry(zipEntry);

                    // 写入文件内容
                    Files.copy(filePath, zipOut);
                    zipOut.closeEntry();
                }
            } catch (IOException e) {
                log.error("Error creating zip file", e);
                throw new RuntimeException("Failed to create zip file", e);
            }
        };

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String zipFilename = "files_" + timestamp + ".zip";
        
        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDisposition(ContentDisposition.attachment()
            .filename(zipFilename)
            .build());

        return ResponseEntity.ok()
            .headers(headers)
            .body(responseBody);
    }

    /**
     * PDF文件预览专用接口
     */
    @GetMapping("/preview/pdf/{filename}")
    public ResponseEntity<Resource> previewPdf(@PathVariable String filename) throws IOException {
        Path filePath = Paths.get(uploadDir, filename);
        Resource resource = new UrlResource(filePath.toUri());
        
        if (resource.exists() && resource.isReadable()) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            
            // URL encode the filename for Content-Disposition header
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString())
                    .replace("+", "%20");
            
            // Set Content-Disposition with both ASCII and UTF-8 encoded filenames
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                    String.format("inline; filename=\"%s\"; filename*=UTF-8''%s",
                            filename.replaceAll("[^\\x00-\\x7F]", "_"),
                            encodedFilename));
            
            headers.add("X-Frame-Options", "SAMEORIGIN");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
        } else {
            throw new FileNotFoundException("文件不存在或无法读取");
        }
    }
} 