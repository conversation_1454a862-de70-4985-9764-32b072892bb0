# Web Learning Project

## 项目结构
- web-learning: Web服务模块
- user-center: 用户中心模块
- common-logging-spring-boot-starter: 公共日志模块

## 功能说明
1. 用户服务 (UserClient)
   - 用户注册: register()
   - 用户登录: login()
   - Token验证: validateToken()

## 使用说明
1. 启动服务：运行 WebLearningApplication 类
2. API接口：
   - 注册：POST /api/user/register
   - 登录：POST /api/user/login
   - Token验证：POST /api/user/validate

## 开发环境
- JDK 17
- Spring Boot 3.1.0
- Maven
- Docker

## CI/CD 说明

### Jenkins 自动化构建流程

1. 构建触发方式
   - 代码提交：自动构建变更模块
   - Tag创建：构建所有模块并发布新版本

2. 模块构建规则
   - 自动检测项目中的所有 *-learning 和 common-* 模块
   - 支持通过 .jenkinsignore 文件排除不需要构建的模块
   - 增量构建：只构建发生变更的模块
   - Tag构建：构建所有未忽略的模块

3. Docker镜像管理
   - 自动构建Docker镜像
   - 保留最新3个版本镜像
   - 自动清理旧版本镜像
   - 使用docker-compose进行服务编排

4. 构建配置说明
   - 构建超时：1小时
   - Maven本地仓库：${JENKINS_HOME}/.m2/repository
   - 缓存清理：支持定期清理Maven缓存

### 开发指南

1. 日常开发流程
   ```bash
   # 1. 开发代码
   git checkout -b feature/xxx
   
   # 2. 提交代码
   git add .
   git commit -m "feat: 添加新功能"
   git push
   
   # 3. 发布版本
   git tag v1.0.0
   git push origin v1.0.0
   ```

2. 忽略模块构建
   创建 .jenkinsignore 文件：
   ```
   # 忽略示例
   module-to-ignore
   test-module
   ```

3. 环境变量配置
   - CLEAN_CACHE: 是否清理Maven缓存（true/false）
   - CACHE_DAYS: 缓存保留天数（默认30天）

### 注意事项

1. 版本发布
   - 使用语义化版本号（v1.0.0）
   - Tag会触发完整构建和部署
   - 确保代码已完成充分测试

2. 构建优化
   - 及时清理不需要的Docker镜像
   - 定期清理Maven缓存
   - 模块化依赖，减少构建时间

3. 问题排查
   - 查看Jenkins构建日志
   - 检查Docker容器状态
   - 验证服务健康检查