# Kcptun GUI Client

这是一个用Python/Tkinter开发的Kcptun客户端GUI程序。

## 功能特性

- 图形化界面操作Kcptun客户端
- 保存和加载配置
- 实时状态监控
- 自动验证输入
- 自动安装依赖包

## 安装步骤

1. 确保已安装Python 3.6或更高版本
2. 下载或克隆本项目
3. 创建bin目录并将Kcptun客户端程序(client_windows_amd64.exe)放入bin目录
4. 运行程序:   ```bash
   python run.py   ```
   程序首次运行时会自动安装所需的依赖包

## 目录结构

- Kcptun客户端程序放在bin目录下
- 配置文件放在resources目录下
- 源代码放在src目录下
- 运行脚本放在run.py文件中

## 配置说明

### 基本设置
- Kcp IP: 服务器IP地址
- Kcp Port: 服务器端口
- Kcp Password: 连接密码
- Local Port: 本地监听端口

### 高级设置
- MTU: 最大传输单元(默认1400)
- 发送窗口(sndwnd): 发送窗口大小(默认1024)
- 接收窗口(rcvwnd): 接收窗口大小(默认1024)
- 传输模式(mode): 
  - normal: 正常模式
  - fast: 快速模式
  - fast2: 快速模式2
  - fast3: 快速模式3
- 加密方式(crypt): 支持多种加密算法
- 数据片段(datashard): 前向纠错数据片段数(默认10)
- 校验片段(parityshard): 前向纠错校验片段数(默认3)
- DSCP: 差分服务代码点(默认46)
- 心跳包时间(keepalive): 连接保活间隔(默认10秒)
- 禁用数据压缩: 是否禁用数据压缩

## 注意事项

- 程序会自动保存配置到resources/config.json
- 需要管理员权限运行(如果使用特权端口) 