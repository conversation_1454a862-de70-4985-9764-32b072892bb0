# Windows 计算器

一个基于 PyQt6 的现代化计算器应用，支持基础的数学运算，界面简洁美观。

## 下载使用

- [点击这里下载最新版本](https://github.com/YOUR_USERNAME/calculator-win/releases/latest)
- 下载 calculator.exe 后直接双击运行即可

## 功能特点

- 支持基本的加减乘除运算
- 自动格式化数字（添加千位分隔符）
- 实时显示计算过程
- 支持提取文本中的数字功能
- 优雅的动画效果

## 快捷键支持

- Enter: 计算结果
- Shift + 8: 输入乘号(×)
- /: 输入除号(÷)
- Shift + =: 输入加号(+)
- -: 输入减号
- 双击 Backspace: 删除运算符

## 安装依赖

```bash
pip install -r requirements.txt