@echo off
echo Cleaning old files...
del /Q resources\icon.png
del /Q resources\icon.ico
rmdir /S /Q build
rmdir /S /Q dist
rmdir /S /Q release
del /Q *.spec

echo Installing build dependencies...
pip install -r requirements_build.txt

echo Building executable...
python build.py

echo Moving files...
xcopy /E /I /Y "dist\*" "release\"

echo Cleaning up...
rmdir /S /Q build
rmdir /S /Q dist
del /Q *.spec

echo Build complete!
pause 