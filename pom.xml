<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
      
  <modelVersion>4.0.0</modelVersion>
      
    
  <parent>
            
    <groupId>org.springframework.boot</groupId>
            
    <artifactId>spring-boot-starter-parent</artifactId>
            
    <version>3.1.0</version>
        
  </parent>
      
  <groupId>com.example</groupId>
      
  <artifactId>parent-project</artifactId>
      
  <version>1.0-SNAPSHOT</version>
      
  <packaging>pom</packaging>
      
  <modules>
            
    <module>web-learning</module>
            
    <module>user-center</module>
            
    <module>camel-learning</module>
            
    <module>redis-learning</module>
            
    <module>algorithm-learning</module>
          
    <module>spring-annotation-learning</module>
    
    <module>spring-response-learning</module>

    <module>mybatis-learning</module>

    <module>common</module>
    
    <module>common-starter</module>
      
  </modules>
      
  <properties>
            
    <java.version>17</java.version>
            
    <spring-boot.version>3.1.0</spring-boot.version>
            
    <spring-cloud.version>2022.0.3</spring-cloud.version>
            
    <camel.version>4.0.0</camel.version>
            
    <lombok.version>1.18.26</lombok.version>
            
    <mysql.version>8.0.33</mysql.version>
            
    <jwt.version>0.9.1</jwt.version>
        
  </properties>
      
  <dependencyManagement>
            
    <dependencies>
                  
      <!-- Spring Boot Dependencies -->
                  
      <dependency>
                        
        <groupId>org.springframework.boot</groupId>
                        
        <artifactId>spring-boot-dependencies</artifactId>
                        
        <version>${spring-boot.version}</version>
                        
        <type>pom</type>
                        
        <scope>import</scope>
                    
      </dependency>
                  
      <!-- Spring Cloud Dependencies -->
                  
      <dependency>
                        
        <groupId>org.springframework.cloud</groupId>
                        
        <artifactId>spring-cloud-dependencies</artifactId>
                        
        <version>${spring-cloud.version}</version>
                        
        <type>pom</type>
                        
        <scope>import</scope>
                    
      </dependency>
                  
            
      <!-- Project Modules -->
                  
      <dependency>
                        
        <groupId>com.example</groupId>
                        
        <artifactId>user-center-api</artifactId>
                        
        <version>${project.version}</version>
                    
      </dependency>
              
    </dependencies>
        
  </dependencyManagement>
      
  <build>
            
    <plugins>
                  
      <plugin>
                        
        <groupId>org.apache.maven.plugins</groupId>
                        
        <artifactId>maven-compiler-plugin</artifactId>
                        
        <version>3.11.0</version>
                        
        <configuration>
                              
          <source>${java.version}</source>
                              
          <target>${java.version}</target>
                              
          <encoding>UTF-8</encoding>
                          
        </configuration>
                    
      </plugin>
              
    </plugins>
        
  </build>
  
</project>
