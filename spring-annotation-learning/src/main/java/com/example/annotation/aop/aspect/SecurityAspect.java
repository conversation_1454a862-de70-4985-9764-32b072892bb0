package com.example.annotation.aop.aspect;

import com.example.annotation.aop.annotation.RequiresPermission;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.core.annotation.Order;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;

/**
 * 安全切面
 * 用于处理权限检查
 */
@Aspect
@Component
@Slf4j
@Order(0) // 安全检查应该最先执行
public class SecurityAspect {

    /**
     * 前置通知
     * 在方法执行前进行权限检查
     */
    @Before("@annotation(requiresPermission)")
    public void checkPermission(JoinPoint joinPoint, RequiresPermission requiresPermission) {
        String permission = requiresPermission.value();
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getSignature().getDeclaringTypeName();
        
        log.info("检查权限: {}.{} - 所需权限: {}", className, methodName, permission);
        
        // 这里应该实现实际的权限检查逻辑
        // 示例仅作演示用途
        if (!hasPermission(permission)) {
            log.warn("权限不足: {}.{} - 所需权限: {}", className, methodName, permission);
            throw new AccessDeniedException("没有权限执行此操作: " + permission);
        }
        
        log.info("权限检查通过: {}.{}", className, methodName);
    }
    
    /**
     * 检查是否具有指定权限
     * 这里仅作示例，实际应用中应该根据具体的权限系统实现
     */
    private boolean hasPermission(String permission) {
        // TODO: 实现实际的权限检查逻辑
        // 示例：始终返回true
        return true;
    }
} 