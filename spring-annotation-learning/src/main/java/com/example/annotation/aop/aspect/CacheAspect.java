package com.example.annotation.aop.aspect;

import com.example.annotation.aop.annotation.Cacheable;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存切面
 * 实现方法结果的缓存功能
 */
@Aspect
@Component
@Slf4j
public class CacheAspect {
    
    // 使用ConcurrentHashMap作为简单的缓存实现
    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    
    @Around("@annotation(cacheable)")
    public Object cache(ProceedingJoinPoint joinPoint, Cacheable cacheable) throws Throwable {
        // 获取方法签名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String methodName = methodSignature.getName();
        String className = methodSignature.getDeclaringType().getSimpleName();
        
        // 构建缓存键
        String cacheKey = buildCacheKey(cacheable.prefix(), className, methodName, joinPoint.getArgs());
        
        // 检查缓存
        CacheEntry entry = cache.get(cacheKey);
        if (entry != null && !entry.isExpired()) {
            log.info("缓存命中: {}", cacheKey);
            return entry.getValue();
        }
        
        // 执行方法
        Object result = joinPoint.proceed();
        
        // 更新缓存
        cache.put(cacheKey, new CacheEntry(result, cacheable.expireSeconds()));
        log.info("更新缓存: {}", cacheKey);
        
        return result;
    }
    
    private String buildCacheKey(String prefix, String className, String methodName, Object[] args) {
        StringBuilder key = new StringBuilder();
        if (!prefix.isEmpty()) {
            key.append(prefix).append(":");
        }
        key.append(className).append(":").append(methodName);
        if (args != null && args.length > 0) {
            key.append(":").append(Arrays.deepToString(args));
        }
        return key.toString();
    }
    
    /**
     * 缓存条目，包含值和过期时间
     */
    private static class CacheEntry {
        private final Object value;
        private final long expireTime;
        
        public CacheEntry(Object value, int expireSeconds) {
            this.value = value;
            this.expireTime = System.currentTimeMillis() + expireSeconds * 1000L;
        }
        
        public Object getValue() {
            return value;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
    }
} 