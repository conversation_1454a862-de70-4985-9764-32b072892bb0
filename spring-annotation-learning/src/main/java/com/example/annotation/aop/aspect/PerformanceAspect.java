package com.example.annotation.aop.aspect;

import com.example.annotation.aop.annotation.LogExecutionTime;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * 性能监控切面
 * 用于记录方法执行时间
 */
@Aspect
@Component
@Slf4j
public class PerformanceAspect {
    
    /**
     * 环绕通知，记录方法执行时间
     * @param joinPoint 连接点
     * @param logExecutionTime 注解实例
     * @return 方法执行结果
     * @throws Throwable 执行异常
     */
    @Around("@annotation(logExecutionTime)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint, LogExecutionTime logExecutionTime) throws Throwable {
        // 获取方法签名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String methodName = methodSignature.getName();
        String className = methodSignature.getDeclaringType().getSimpleName();
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        try {
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 记录执行时间
            String description = logExecutionTime.value();
            if (description.isEmpty()) {
                log.info("方法 [{}.{}] 执行耗时: {}ms", className, methodName, executionTime);
            } else {
                log.info("{} - 方法 [{}.{}] 执行耗时: {}ms", 
                    description, className, methodName, executionTime);
            }
            
            return result;
        } catch (Throwable throwable) {
            // 记录异常信息
            log.error("方法 [{}.{}] 执行异常", className, methodName, throwable);
            throw throwable;
        }
    }
} 