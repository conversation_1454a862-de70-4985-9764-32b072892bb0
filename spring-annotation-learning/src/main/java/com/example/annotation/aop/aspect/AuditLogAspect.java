package com.example.annotation.aop.aspect;

import com.example.annotation.aop.annotation.AuditLog;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 审计日志切面
 * 记录方法的调用信息，包括参数、返回值和执行时间
 */
@Aspect
@Component
@Slf4j
public class AuditLogAspect {
    
    @Around("@annotation(auditLog)")
    public Object logAround(ProceedingJoinPoint joinPoint, AuditLog auditLog) throws Throwable {
        // 获取方法信息
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String methodName = methodSignature.getName();
        String className = methodSignature.getDeclaringType().getSimpleName();
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // 记录操作开始
        log.info("=== 审计日志 ===");
        log.info("操作类型: {}", auditLog.operation());
        if (!auditLog.description().isEmpty()) {
            log.info("操作描述: {}", auditLog.description());
        }
        log.info("调用方法: {}.{}", className, methodName);
        
        // 记录参数
        if (auditLog.logParams()) {
            log.info("方法参数: {}", Arrays.toString(joinPoint.getArgs()));
        }
        
        try {
            // 执行方法
            Object result = joinPoint.proceed();
            
            // 记录返回值
            if (auditLog.logResult()) {
                log.info("返回结果: {}", result);
            }
            
            // 记录执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            log.info("执行时间: {}ms", executionTime);
            
            return result;
        } catch (Throwable throwable) {
            // 记录异常信息
            log.error("执行异常: {}", throwable.getMessage());
            throw throwable;
        } finally {
            log.info("=== 审计结束 ===");
        }
    }
} 