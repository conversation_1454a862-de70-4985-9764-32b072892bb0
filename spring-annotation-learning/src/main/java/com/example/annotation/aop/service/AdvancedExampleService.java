package com.example.annotation.aop.service;

import com.example.annotation.aop.annotation.AuditLog;
import com.example.annotation.aop.annotation.Cacheable;
import com.example.annotation.aop.annotation.ValidateParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 高级示例服务
 * 用于演示各种AOP功能的综合使用
 */
@Service
@Slf4j
public class AdvancedExampleService {

    /**
     * 演示缓存功能
     * 模拟一个耗时的数据查询操作
     */
    @Cacheable(prefix = "user", expireSeconds = 300)
    public String getUserInfo(String userId) {
        log.info("查询用户信息: {}", userId);
        try {
            // 模拟耗时操作
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("操作被中断", e);
        }
        return "用户" + userId + "的详细信息";
    }

    /**
     * 演示参数验证功能
     * 验证用户名的有效性
     */
    @ValidateParams(allowNull = false, minLength = 3, maxLength = 20)
    public void validateUsername(String username) {
        log.info("用户名验证通过: {}", username);
    }

    /**
     * 演示审计日志功能
     * 模拟用户更新操作
     */
    @AuditLog(
        operation = "更新用户",
        description = "更新用户基本信息",
        logParams = true,
        logResult = true
    )
    public String updateUser(String userId, String newName) {
        log.info("更新用户信息: userId={}, newName={}", userId, newName);
        return "用户信息更新成功";
    }

    /**
     * 演示多个切面的组合使用
     * 包含参数验证、缓存和审计日志
     */
    @ValidateParams(allowNull = false, minLength = 2)
    @Cacheable(prefix = "user_role", expireSeconds = 3600)
    @AuditLog(operation = "查询角色", description = "查询用户角色信息")
    public String getUserRole(String userId) {
        log.info("查询用户角色: {}", userId);
        return "管理员";
    }
} 