package com.example.annotation.aop.service;

import com.example.annotation.aop.annotation.LogExecutionTime;
import com.example.annotation.aop.annotation.RequiresPermission;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 示例服务类
 * 用于演示AOP注解的使用
 */
@Service
@Slf4j
public class ExampleService {

    /**
     * 演示方法执行时间记录
     * 使用@LogExecutionTime注解记录方法执行时间
     */
    @LogExecutionTime("耗时操作示例")
    public void performTimeConsumingOperation() {
        log.info("开始执行耗时操作...");
        try {
            // 模拟耗时操作
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("操作被中断", e);
        }
        log.info("耗时操作执行完成");
    }

    /**
     * 演示权限检查
     * 使用@RequiresPermission注解进行权限控制
     */
    @RequiresPermission("admin")
    public void performSecureOperation() {
        log.info("执行需要管��员权限的操作");
    }

    /**
     * 演示异常处理
     * 这个方法会被LoggingAspect记录
     */
    public void performOperationWithException() {
        log.info("执行可能抛出异常的操作");
        throw new RuntimeException("操作失败");
    }

    /**
     * 演示正常方法调用
     * 这个方法会被LoggingAspect记录参数和返回值
     */
    public String performNormalOperation(String input) {
        log.info("执行普通操作");
        return "处理结果: " + input;
    }
} 