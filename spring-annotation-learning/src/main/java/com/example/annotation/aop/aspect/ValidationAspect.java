package com.example.annotation.aop.aspect;

import com.example.annotation.aop.annotation.ValidateParams;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 参数验证切面
 * 在方法执行前验证参数
 */
@Aspect
@Component
@Slf4j
@Order(1)  // 验证应该最先执行
public class ValidationAspect {
    
    @Before("@annotation(validateParams)")
    public void validate(JoinPoint joinPoint, ValidateParams validateParams) {
        Object[] args = joinPoint.getArgs();
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String methodName = methodSignature.getName();
        String className = methodSignature.getDeclaringType().getSimpleName();
        
        log.info("验证参数: {}.{}", className, methodName);
        
        // 验证参数不为null
        if (!validateParams.allowNull()) {
            for (Object arg : args) {
                if (arg == null) {
                    throw new IllegalArgumentException("参数不能为null");
                }
            }
        }
        
        // 验证字符串长度
        for (Object arg : args) {
            if (arg instanceof String) {
                String strArg = (String) arg;
                int length = strArg.length();
                
                if (length < validateParams.minLength()) {
                    throw new IllegalArgumentException(
                        String.format("字符串长度不能小于%d", validateParams.minLength()));
                }
                
                if (length > validateParams.maxLength()) {
                    throw new IllegalArgumentException(
                        String.format("字符串长度不能大于%d", validateParams.maxLength()));
                }
            }
        }
        
        log.info("参数验证通过: {}.{}", className, methodName);
    }
} 