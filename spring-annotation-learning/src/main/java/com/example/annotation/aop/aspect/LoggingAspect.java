package com.example.annotation.aop.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 日志切面
 * 用于记录方法调用的详细信息
 */
@Aspect
@Component
@Slf4j
@Order(1) // 定义切面的优先级，数字越小优先级越高
public class LoggingAspect {

    /**
     * 定义切点
     * 匹配service包下的所有方法
     */
    @Pointcut("execution(* com.example.annotation.*.service.*.*(..))")
    public void serviceLayer() {}

    /**
     * 前置通知
     * 在方法执行前记录调用信息
     */
    @Before("serviceLayer()")
    public void logBefore(JoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getSignature().getDeclaringTypeName();
        Object[] args = joinPoint.getArgs();
        
        log.info("开始执行: {}.{}", className, methodName);
        log.info("方法参数: {}", Arrays.toString(args));
    }

    /**
     * 后置通知
     * 在方法执行后记录信息（无论是否发生异常）
     */
    @After("serviceLayer()")
    public void logAfter(JoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getSignature().getDeclaringTypeName();
        
        log.info("结束执行: {}.{}", className, methodName);
    }

    /**
     * 返回通知
     * 在方法成功执行后记录返回值
     */
    @AfterReturning(pointcut = "serviceLayer()", returning = "result")
    public void logAfterReturning(JoinPoint joinPoint, Object result) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getSignature().getDeclaringTypeName();
        
        log.info("方法返回: {}.{} - 返回值: {}", className, methodName, result);
    }

    /**
     * 异常通知
     * 在方法抛出异常时记录异常信息
     */
    @AfterThrowing(pointcut = "serviceLayer()", throwing = "error")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable error) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getSignature().getDeclaringTypeName();
        
        log.error("方法异常: {}.{} - 异常信息: {}", 
            className, methodName, error.getMessage());
    }
} 