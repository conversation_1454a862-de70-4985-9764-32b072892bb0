# Spring AOP注解学习

本模块专门演示Spring AOP（面向切面编程）相关注解的使用，通过实际示例展示如何使用AOP实现横切关注点（如日志、性能监控、安全等）。

## 项目结构

```
aop/
├── annotation/     # 自定义注解
│   ├── LogExecutionTime.java
│   └── RequiresPermission.java
├── aspect/         # 切面类
│   ├── LoggingAspect.java
│   ├── PerformanceAspect.java
│   └── SecurityAspect.java
└── config/         # AOP配置
    └── AopConfig.java
```

## 核心注解说明

### 1. 启用AOP
- `@EnableAspectJAutoProxy`
  - 启用AspectJ自动代理
  - 通常在配置类上使用
  - 支持配置代理模式（JDK动态代理或CGLIB）

### 2. 切面定义
- `@Aspect`
  - 声明一个类为切面
  - 包含切点和通知
  - 示例：
    ```java
    @Aspect
    @Component
    public class LoggingAspect {
        // 切面逻辑
    }
    ```

### 3. 切点表达式
- `@Pointcut`
  - 定义切点表达式
  - 可重用的切点定义
  - 支持多种匹配方式：
    ```java
    // 匹配所有service包下的方法
    @Pointcut("execution(* com.example.*.service.*.*(..))")
    public void serviceLayer() {}
    
    // 匹配特定注解标记的方法
    @Pointcut("@annotation(com.example.annotation.LogExecutionTime)")
    public void logExecutionTime() {}
    ```

### 4. 通知类型
- `@Before`
  - 前置通知
  - 在目标方法执行前执行
  - 示例：参数验证、权限检查

- `@After`
  - 后置通知
  - 在目标方法执行后执行（无论是否异常）
  - 示例：清理资源、日志记录

- `@AfterReturning`
  - 返回通知
  - 在目标方法成功执行后执行
  - 可以访问返回值
  - 示例：结果处理、缓存更新

- `@AfterThrowing`
  - 异常通知
  - 在目标方法抛出异常时执行
  - 可以访问异常信息
  - 示例：异常处理、日志记录

- `@Around`
  - 环绕通知
  - 可以完全控制目标方法的执行
  - 最强大的通知类型
  - 示例：性能监控、事务控制

### 5. 自定义注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LogExecutionTime {
    String value() default "";
}
```

## 实际应用示例

### 1. 性能监控切面
```java
@Aspect
@Component
@Slf4j
public class PerformanceAspect {
    
    @Around("@annotation(LogExecutionTime)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long endTime = System.currentTimeMillis();
        
        log.info("方法 [{}] 执行耗时：{}ms",
            joinPoint.getSignature().getName(),
            (endTime - startTime));
            
        return result;
    }
}
```

### 2. 日志切面
```java
@Aspect
@Component
@Slf4j
public class LoggingAspect {
    
    @Before("execution(* com.example.*.service.*.*(..))")
    public void logBefore(JoinPoint joinPoint) {
        log.info("开始执行: {}.{}",
            joinPoint.getSignature().getDeclaringTypeName(),
            joinPoint.getSignature().getName());
    }
    
    @AfterReturning(
        pointcut = "execution(* com.example.*.service.*.*(..))",
        returning = "result")
    public void logAfterReturning(JoinPoint joinPoint, Object result) {
        log.info("方法返回: {}.{} - 返回值: {}",
            joinPoint.getSignature().getDeclaringTypeName(),
            joinPoint.getSignature().getName(),
            result);
    }
}
```

### 3. 安全切面
```java
@Aspect
@Component
public class SecurityAspect {
    
    @Before("@annotation(requiresPermission)")
    public void checkPermission(JoinPoint joinPoint, 
                              RequiresPermission requiresPermission) {
        String permission = requiresPermission.value();
        if (!hasPermission(permission)) {
            throw new AccessDeniedException("没有权限执行此操作");
        }
    }
}
```

## 常见使用场景

1. 日志记录
   - 方法调用日志
   - 参数和返回值记录
   - 异常日志

2. 性能监控
   - 方法执行时间统计
   - 慢方法检测
   - 性能分析

3. 安全控制
   - 权限检查
   - 访问控制
   - 安全审计

4. 事务管理
   - 事务边界控制
   - 事务传播行为
   - 异常回滚

5. 缓存处理
   - 缓存结果
   - 缓存失效
   - 缓存更新

## 最佳实践建议

1. 切点设计
   - 使用清晰的命名
   - 合理的粒度控制
   - 避免过于复杂的表达式

2. 通知选择
   - 根据场景选择合适的通知类型
   - 优先使用简单的通知类型
   - 必要时才使用@Around

3. 性能考虑
   - 避免过多的切面
   - 切点表达式要精确
   - 注意通知的执行效率

4. 异常处理
   - 合理处理通知中的异常
   - 不影响业务逻辑
   - 做好日志记录

5. 测试建议
   - 编写切面的单元测试
   - 验证切面的执行顺序
   - 测试异常场景

## 注意事项

1. 代理限制
   - 内部方法调用不会触发切面
   - 需要注意自调用问题
   - 考虑使用AopContext

2. 切点顺序
   - 使用@Order控制切面顺序
   - 理解切面的执行顺序
   - 避免切面间的相互干扰

3. 性能影响
   - 合理使用切面
   - 避免过多的切点匹配
   - 注意通知的性能开销 