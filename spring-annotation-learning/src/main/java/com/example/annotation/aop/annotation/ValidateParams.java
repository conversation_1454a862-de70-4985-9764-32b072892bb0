package com.example.annotation.aop.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 参数验证注解
 * 用于标记需要进行参数验证的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidateParams {
    /**
     * 是否允许参数为null
     */
    boolean allowNull() default false;
    
    /**
     * 字符串最小长度
     */
    int minLength() default 0;
    
    /**
     * 字符串最大长度
     */
    int maxLength() default Integer.MAX_VALUE;
} 