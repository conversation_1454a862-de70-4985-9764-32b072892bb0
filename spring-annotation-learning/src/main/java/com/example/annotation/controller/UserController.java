package com.example.annotation.controller;

import com.example.annotation.component.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户控制器
 * 用于演示Spring Web相关注解的使用
 */
@RestController
@RequestMapping("/api/users")
public class UserController {

    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    /**
     * @GetMapping: 处理GET请求
     */
    @GetMapping("/info")
    public String getUserInfo() {
        return userService.getServiceInfo();
    }
} 