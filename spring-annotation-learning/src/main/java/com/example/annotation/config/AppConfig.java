package com.example.annotation.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.annotation.Scope;

/**
 * 应用配置类
 * 用于演示Spring配置相关注解的使用
 */
@Configuration
public class AppConfig {

    /**
     * @Bean: 注册一个bean到Spring容器
     * @Scope: 定义bean的作用域（singleton单例/prototype多例）
     */
    @Bean
    @Scope("singleton")
    public MyService myService() {
        return new MyService();
    }

    /**
     * @Profile: 指定在特定的环境下才会创建这个bean
     */
    @Bean
    @Profile("dev")
    public MyDevService myDevService() {
        return new MyDevService();
    }

    /**
     * 服务类
     */
    public static class MyService {
        public String hello() {
            return "Hello from MyService!";
        }
    }

    /**
     * 开发环境服务类
     */
    public static class MyDevService {
        public String hello() {
            return "Hello from MyDevService!";
        }
    }
} 