package com.example.annotation.component;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 用户服务类
 * 用于演示Spring组件相关注解的使用
 */
@Service
public class UserService {

    private final UserRepository userRepository;

    /**
     * @Value: 注入配置属性
     */
    @Value("${app.service.name:defaultName}")
    private String serviceName;

    /**
     * @Autowired: 自动注入依赖
     * 构造器注入是推荐的注入方式
     */
    @Autowired
    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    /**
     * @PostConstruct: 在构造方法执行后执行
     */
    @PostConstruct
    public void init() {
        System.out.println("UserService is initialized with name: " + serviceName);
    }

    /**
     * @PreDestroy: 在bean销毁前执行
     */
    @PreDestroy
    public void cleanup() {
        System.out.println("UserService is being destroyed");
    }

    public String getServiceInfo() {
        return "UserService[" + serviceName + "] with " + userRepository.getRepositoryInfo();
    }
} 