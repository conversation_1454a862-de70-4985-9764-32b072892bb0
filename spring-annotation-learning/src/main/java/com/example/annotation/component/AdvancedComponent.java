package com.example.annotation.component;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 高级组件示例
 * 展示更多Spring注解的使用方法
 */
@Slf4j                          // Lombok注解：自动创建log变量用于日志记录
@Component                      // Spring核心注解：标记这个类为Spring管理的组件
@Scope("singleton")             // Spring作用域注解：指定组件的作用域，singleton表示单例（默认值）
public class AdvancedComponent {
    
    // ===== 属性注入示例 =====
    
    @Value("${app.name:测试应用}")              // 从配置文件注入值，如果找不到则使用默认值
    private String appName;
    
    @Value("${app.version:1.0}")              // 注入简单类型��值
    private String version;
    
    @Value("${app.servers:server1,server2}")   // 注入数组或列表
    private List<String> serverList;
    
    // ===== 依赖注入示例 =====
    
    private final SimpleComponent simpleComponent;  // 推荐使用final，表明依赖不可变
    
    /**
     * 构造器注入（推荐的注入方式）
     * @Autowired 可以省略，因为这是唯一的构造函数
     */
    @Autowired
    public AdvancedComponent(SimpleComponent simpleComponent) {
        this.simpleComponent = simpleComponent;
        log.info("正在创建AdvancedComponent实例");
    }
    
    // ===== 生命周期回调示例 =====
    
    /**
     * 初始化回调方法
     * 在所有依赖注入完成后执行
     */
    @PostConstruct
    public void init() {
        log.info("AdvancedComponent初始化完成");
        log.info("应用名称: {}", appName);
        log.info("版本: {}", version);
        log.info("服务器列表: {}", serverList);
    }
    
    /**
     * 销毁回调方法
     * 在容器销毁bean之前执行
     */
    @PreDestroy
    public void cleanup() {
        log.info("AdvancedComponent即将被销毁");
    }
    
    // ===== 业务方法示例 =====
    
    /**
     * 组合SimpleComponent的功能
     */
    public String getFullInfo() {
        String simpleMessage = simpleComponent.sayHello();
        return String.format("%s (版本: %s)", simpleMessage, version);
    }
    
    /**
     * 获取服务器信息
     */
    public List<String> getServerList() {
        log.info("获取服务器列表");
        return serverList;
    }
} 