package com.example.annotation.component;

import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;

/**
 * 简单组件示例
 * 用来演示Spring基础注解的使用方法
 */
@Slf4j  // 自动创建日志对象
@Component  // 标记这个类为Spring管理的组件
public class SimpleComponent {
    
    @Value("${app.name:测试应用}")  // 从配置文件注入值，默认值为"测试应用"
    private String appName;
    
    // 构造方法
    public SimpleComponent() {
        log.info("正在创建SimpleComponent实例");
    }
    
    // 测试方法
    public String sayHello() {
        String message = "你好，这里是 " + appName;
        log.info(message);
        return message;
    }
} 