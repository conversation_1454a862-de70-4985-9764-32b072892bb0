package com.example.annotation.student.entity;

import lombok.Data;

/**
 * 学生实体类
 */
@Data  // Lombok注解：自动生成getter、setter、toString等方法
public class Student {
    private String name;    // 学生姓名
    private int age;        // 学生年龄
    private String grade;   // 学生年级
    
    // 构造方法
    public Student(String name, int age, String grade) {
        this.name = name;
        this.age = age;
        this.grade = grade;
    }
} 