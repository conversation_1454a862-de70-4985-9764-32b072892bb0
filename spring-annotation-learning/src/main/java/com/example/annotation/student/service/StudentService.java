package com.example.annotation.student.service;

import com.example.annotation.student.entity.Student;
import com.example.annotation.student.repository.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 学生服务层
 * @Service: 表示这是一个服务层的组件
 * @Slf4j: 自动创建日志对象
 */
@Slf4j
@Service
public class StudentService {
    
    // 从配置文件中读取学校名称，如果没有配置，使用默认值
    @Value("${school.name:示例中学}")
    private String schoolName;
    
    // 注入学生数据访问层
    private final StudentRepository studentRepository;
    
    /**
     * 构造方法注入
     * Spring会自动找到StudentRepository的实例注入进来
     */
    @Autowired
    public StudentService(StudentRepository studentRepository) {
        this.studentRepository = studentRepository;
        log.info("创建StudentService，学校名称：{}", schoolName);
    }
    
    /**
     * 获取所有学生信息
     */
    public List<Student> getAllStudents() {
        log.info("获取{}的所有学生信息", schoolName);
        return studentRepository.getAllStudents();
    }
    
    /**
     * 查找指定学生
     */
    public Student findStudent(String name) {
        log.info("在{}中查找学生：{}", schoolName, name);
        return studentRepository.findByName(name);
    }
    
    /**
     * 添加新学生
     */
    public void addStudent(Student student) {
        log.info("向{}添加新学生：{}", schoolName, student.getName());
        studentRepository.addStudent(student);
    }
    
    /**
     * 获取学校名称
     */
    public String getSchoolName() {
        return schoolName;
    }
} 