package com.example.annotation.student.repository;

import com.example.annotation.student.entity.Student;
import org.springframework.stereotype.Repository;
import java.util.ArrayList;
import java.util.List;

/**
 * 学生数据访问层
 * @Repository: 表示这是一个数据访问层的组件
 */
@Repository
public class StudentRepository {
    
    // 模拟数据库中的学生列表
    private final List<Student> students = new ArrayList<>();
    
    // 构造方法，初始化一些测试数据
    public StudentRepository() {
        students.add(new Student("张三", 18, "高一"));
        students.add(new Student("李四", 17, "高一"));
        students.add(new Student("王五", 16, "初三"));
    }
    
    // 获取所有学生
    public List<Student> getAllStudents() {
        return students;
    }
    
    // 添加学生
    public void addStudent(Student student) {
        students.add(student);
    }
    
    // 根据名字查找学生
    public Student findByName(String name) {
        return students.stream()
                .filter(student -> student.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
} 