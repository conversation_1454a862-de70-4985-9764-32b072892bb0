package com.example.annotation.student.controller;

import com.example.annotation.student.entity.Student;
import com.example.annotation.student.service.StudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 学生控制器
 * @RestController: 表示这是一个REST API的控制器
 * @RequestMapping: 指定接口的基础路径
 */
@RestController
@RequestMapping("/api/students")
public class StudentController {
    
    // 注入学生服务
    private final StudentService studentService;
    
    @Autowired
    public StudentController(StudentService studentService) {
        this.studentService = studentService;
    }
    
    /**
     * 获取所有学生
     * @GetMapping: 处理GET请求
     */
    @GetMapping
    public List<Student> getAllStudents() {
        return studentService.getAllStudents();
    }
    
    /**
     * 根据名字查找学生
     * @PathVariable: 获取URL路径中的参数
     */
    @GetMapping("/{name}")
    public Student getStudent(@PathVariable String name) {
        return studentService.findStudent(name);
    }
    
    /**
     * 添加新学生
     * @PostMapping: 处理POST请求
     * @RequestBody: 从请求体中获取参数
     */
    @PostMapping
    public String addStudent(@RequestBody Student student) {
        studentService.addStudent(student);
        return "添加成功";
    }
    
    /**
     * 获取学校信息
     */
    @GetMapping("/school")
    public String getSchoolInfo() {
        return "欢迎访问" + studentService.getSchoolName();
    }
} 