# Spring注解学习 - 学生管理模块

本模块展示了Spring框架中基础注解的用法，通过一个完整的学生管理系统来演示常用注解的使用场景和基本实践。

## 项目结构

```
student/
├── entity/          # 实体层
│   └── Student.java
├── repository/      # 数据访问层
│   └── StudentRepository.java
├── service/         # 服务层
│   └── StudentService.java
└── controller/      # 控制器层
    └── StudentController.java
```

## 核心注解说明

### 1. 实体层注解
- `@Data`（Lombok）
  - 自动生成getter、setter、equals、hashCode和toString方法
  - 简化实体类的编写
  - 减少模板代码

### 2. 数据访问层注解
- `@Repository`
  - 标记类为数据访问层组件
  - 自动注册到Spring容器
  - 自动转换数据访问异常

### 3. 服务层注解
- `@Service`
  - 标记类为服务层组件
  - 表明业务逻辑层的职责
  - 自动注册到Spring容器

- `@Autowired`
  - 自动注入依赖对象
  - 支持构造方法注入
  - 支持字段注入（不推荐）

- `@Value`
  - 注入配置文件中的值
  - 支持默认值设置
  - 示例：`@Value("${school.name:示例中学}")`

### 4. 控制器层注解
- `@RestController`
  - 组合了@Controller和@ResponseBody
  - 标记类为Web API控制器
  - 自动将返回值转换为JSON

- `@RequestMapping`
  - 映射Web请求URL
  - 定义基础访问路径
  - 示例：`@RequestMapping("/api/students")`

- `@GetMapping`, `@PostMapping`
  - 处理HTTP GET/POST请求
  - @RequestMapping的便捷方式
  - 更清晰地表达API语义

- `@PathVariable`
  - 获取URL路径中的参数
  - 用于RESTful API
  - 示例：`@PathVariable String name`

- `@RequestBody`
  - 接收请求体中的JSON数据
  - 自动转换为Java对象
  - 用于处理POST请求

## 基本功能实现

### 1. 数据存储
```java
private final List<Student> students = new ArrayList<>();
```
- 使用ArrayList存储学生数据
- 适用于简单的数据管理
- 方便演示基本操作

### 2. 业务逻辑
```java
@Service
public class StudentService {
    @Value("${school.name:示例中学}")
    private String schoolName;
    
    private final StudentRepository studentRepository;
}
```
- 使用构造方法注入依赖
- 通过配置注入学校名称
- 实现基本的CRUD操作

### 3. API接口
```java
@RestController
@RequestMapping("/api/students")
public class StudentController {
    private final StudentService studentService;
}
```
- RESTful API设计
- 统一的返回格式
- 基本的错误处理

## API接口说明

### 1. 获取所有学生
```http
GET /api/students
```

### 2. 获取指定学生
```http
GET /api/students/{name}
```

### 3. 添加学生
```http
POST /api/students
Content-Type: application/json

{
    "name": "张三",
    "age": 18,
    "grade": "高一"
}
```

### 4. 获取学校信息
```http
GET /api/students/school
```

## 配置示例

```yaml
# application.yml
school:
  name: 示例中学
```

## 最佳实践建议

1. 使用构造方法注入依赖
2. 为配置值提供默认值
3. 合理使用日志记录
4. 统一的API响应格式
5. 清晰的代码分层
6. 完整的注释文档

## 与教师模块的区别

相比教师管理模块，学生管理模块更加简单和基础：

1. 数据存储
   - 学生模块：使用ArrayList，适合简单场景
   - 教师模块：使用ConcurrentHashMap，支持并发

2. 功能特性
   - 学生模块：基本的CRUD操作
   - 教师模块：异步操作、环境配置等高级特性

3. 注解使用
   - 学生模块：基础的Spring注解
   - 教师模块：更多高级注解（@Async, @Profile等）

4. 错误处理
   - 学生模块：简单的返回值处理
   - 教师模块：使用ResponseEntity，更完善的状态处理

这种渐进式的学习方式有助于更好地理解Spring注解的使用。建议先掌握学生模块的基础用法，再学习教师模块的高级特性。 