package com.example.annotation.teacher.controller;

import com.example.annotation.teacher.entity.Teacher;
import com.example.annotation.teacher.service.TeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 教师控制器
 * 
 * 这个类演示了以下Spring注解的用法：
 * 1. @RestController - 组合了@Controller和@ResponseBody，用于REST API
 * 2. @RequestMapping - 定义请求URL的基础路径
 * 3. @CrossOrigin - 允许跨域请求
 * 4. @GetMapping, @PostMapping, @PutMapping, @DeleteMapping - HTTP方法映射
 * 5. @PathVariable - 获取URL路径参数
 * 6. @RequestBody - 获取请求体参数
 * 7. @Slf4j (Lombok) - 自动创建日志对象
 * 
 * 本类演示了RESTful API的最佳实践：
 * - 使用合适的HTTP方法
 * - 返回合适的HTTP状态码
 * - 使用ResponseEntity包装响应
 * - 异步处理支持
 * - 统一的错误处理
 */
@Slf4j
@RestController
@RequestMapping("/api/teachers")
@CrossOrigin(origins = "*")  // 允许所有来源的跨域请求
public class TeacherController {
    
    /**
     * 教师服务对象
     * final修饰确保引用不被修改
     */
    private final TeacherService teacherService;
    
    /**
     * 构造方法注入
     * Spring推荐使用构造方法注入而不是字段注入
     * 
     * @param teacherService 教师服务对象
     */
    @Autowired
    public TeacherController(TeacherService teacherService) {
        this.teacherService = teacherService;
    }
    
    /**
     * 异步获取所有教师
     * 使用Spring的异步支持，提高系统响应性能
     * URL: GET /api/teachers/async
     * 
     * @return 包含教师列表的CompletableFuture对象
     */
    @GetMapping("/async")
    public CompletableFuture<List<Teacher>> getAllTeachersAsync() {
        return teacherService.getAllTeachersAsync();
    }
    
    /**
     * 获取所有教师（同步方法）
     * URL: GET /api/teachers
     * 
     * @return 教师列表
     */
    @GetMapping
    public List<Teacher> getAllTeachers() {
        return teacherService.getAllTeachers();
    }
    
    /**
     * 根据姓名查找教师
     * URL: GET /api/teachers/{name}
     * 
     * @param name 教师姓名
     * @return 如果找到返回200和教师信息，否则返回404
     */
    @GetMapping("/{name}")
    public ResponseEntity<Teacher> getTeacher(@PathVariable String name) {
        Teacher teacher = teacherService.findTeacher(name);
        if (teacher != null) {
            return ResponseEntity.ok(teacher);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 添加新教师
     * URL: POST /api/teachers
     * Content-Type: application/json
     * 
     * @param teacher 要添加的教师对象（从请求体获取）
     * @return 包含成功消息的ResponseEntity
     */
    @PostMapping
    public ResponseEntity<String> addTeacher(@RequestBody Teacher teacher) {
        teacherService.addTeacher(teacher);
        return ResponseEntity.ok("教师添加成功");
    }
    
    /**
     * 更新教师信息
     * URL: PUT /api/teachers/{name}
     * Content-Type: application/json
     * 
     * @param name 要更新的教师姓名（从URL路径获取）
     * @param teacher 更新后的教师信息（从请求体获取）
     * @return 成功返回200，如果姓名不匹配返回400
     */
    @PutMapping("/{name}")
    public ResponseEntity<String> updateTeacher(
            @PathVariable String name,
            @RequestBody Teacher teacher) {
        if (!name.equals(teacher.getName())) {
            return ResponseEntity.badRequest().body("教师姓名不匹配");
        }
        teacherService.updateTeacher(teacher);
        return ResponseEntity.ok("教师信息更新成功");
    }
    
    /**
     * 删除教师
     * URL: DELETE /api/teachers/{name}
     * 
     * @param name 要删除的教师姓名
     * @return 包含成功消息的ResponseEntity
     */
    @DeleteMapping("/{name}")
    public ResponseEntity<String> deleteTeacher(@PathVariable String name) {
        teacherService.deleteTeacher(name);
        return ResponseEntity.ok("教师删除成功");
    }
} 