package com.example.annotation.teacher.service;

import com.example.annotation.teacher.entity.Teacher;
import com.example.annotation.teacher.repository.TeacherRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 教师服务层
 * 
 * 这个类演示了以下Spring注解的用法：
 * 1. @Service - 标记类为服务层组件
 * 2. @Lazy - 延迟初始化，第一次使用时才创建实例
 * 3. @Async - 标记方法为异步执行
 * 4. @Value - 注入配置属性
 * 5. @Autowired - 自动注入依赖
 * 6. @Slf4j (Lombok) - 自动创建日志对象
 * 
 * 本类演示了Spring中服务层的最佳实践：
 * - 构造方法注入
 * - 异步方法处理
 * - 配置值注入
 * - 日志记录
 */
@Slf4j
@Service
@Lazy  // 延迟初始化，提高启动速度
public class TeacherService {
    
    /**
     * 部门名称
     * 使用@Value注解注入配置值
     * 如果配置文件中没有指定，默认使用"教务处"
     */
    @Value("${school.department:教务处}")
    private String department;
    
    /**
     * 教师数据访问对象
     * final修饰确保引用不被修改
     */
    private final TeacherRepository teacherRepository;
    
    /**
     * 构造方法注入
     * 使用@Autowired进行依赖注入（在只有一个构造方法时可以省略）
     * 构造方法注入是Spring推荐的注入方式，可以：
     * 1. 确保依赖不可变
     * 2. 确保必要的依赖被注入
     * 3. 支持final字段
     * 
     * @param teacherRepository 教师数据访问对象
     */
    @Autowired
    public TeacherService(TeacherRepository teacherRepository) {
        this.teacherRepository = teacherRepository;
        log.info("创建TeacherService，所属部门：{}", department);
    }
    
    /**
     * 异步获取教师列表
     * 使用@Async注解标记为异步方法
     * 返回CompletableFuture支持异步操作
     * 注意：需要在配置类上添加@EnableAsync才能启用异步
     * 
     * @return 包含教师列表的CompletableFuture对象
     */
    @Async
    public CompletableFuture<List<Teacher>> getAllTeachersAsync() {
        log.info("异步获取教师列表");
        List<Teacher> teachers = teacherRepository.getAllTeachers();
        return CompletableFuture.completedFuture(teachers);
    }
    
    /**
     * 获取所有教师（同步方法）
     * 
     * @return 教师列表
     */
    public List<Teacher> getAllTeachers() {
        log.info("获取所有教师信息");
        return teacherRepository.getAllTeachers();
    }
    
    /**
     * 添加新教师
     * 
     * @param teacher 要添加的教师对象
     */
    public void addTeacher(Teacher teacher) {
        log.info("添加新教师到{}", department);
        teacherRepository.addTeacher(teacher);
    }
    
    /**
     * 查找指定教师
     * 
     * @param name 教师姓名
     * @return 如果找到返回教师对象，否则返回null
     */
    public Teacher findTeacher(String name) {
        log.info("在{}中查找教师：{}", department, name);
        return teacherRepository.findByName(name);
    }
    
    /**
     * 更新教师信息
     * 
     * @param teacher 要更新的教师对象
     */
    public void updateTeacher(Teacher teacher) {
        log.info("更新{}的教师信息：{}", department, teacher.getName());
        teacherRepository.updateTeacher(teacher);
    }
    
    /**
     * 删除教师
     * 
     * @param name 要删除的教师姓名
     */
    public void deleteTeacher(String name) {
        log.info("从{}删除教师：{}", department, name);
        teacherRepository.deleteTeacher(name);
    }
} 