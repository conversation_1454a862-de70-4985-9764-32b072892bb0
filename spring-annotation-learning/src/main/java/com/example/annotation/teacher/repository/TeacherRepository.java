package com.example.annotation.teacher.repository;

import com.example.annotation.teacher.entity.Teacher;
import org.springframework.stereotype.Repository;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.Profile;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 教师数据访问层
 * 
 * 这个类演示了以下Spring注解的用法：
 * 1. @Repository - 标记类为数据访问层组件，自动处理数据访问异常
 * 2. @Scope - 定义bean的作用域（singleton/prototype/request/session）
 * 3. @Profile - 指定bean在哪些环境下被创建
 * 4. @PostConstruct - 在依赖注入完成后执行初始化
 * 5. @Slf4j (Lombok) - 自动创建SLF4J日志对象
 * 
 * 本类使用ConcurrentHashMap作为内存数据库，演示线程安全的数据访问操作
 */
@Slf4j
@Repository
@Scope("singleton")         // 显式指定为单例模式（Spring默认就是单例）
@Profile({"dev", "prod"})   // 只在开发和生产环境中创建这个bean
public class TeacherRepository {
    
    /**
     * 使用ConcurrentHashMap存储教师数据
     * - 线程安全，适合并发访问
     * - 键为教师姓名，值为教师对象
     * - final修饰确保引用不被修改
     */
    private final Map<String, Teacher> teacherMap = new ConcurrentHashMap<>();
    
    /**
     * 初始化方法
     * 使用@PostConstruct注解标记
     * - 在构造方法执行后自动调用
     * - 在依赖注入完成后执行
     * - 适合进行初始化工作
     */
    @PostConstruct
    public void init() {
        log.info("正在初始化TeacherRepository...");
        // 添加测试数据
        addTeacher(new Teacher("王老师", "数学", 5));
        addTeacher(new Teacher("李老师", "语文", 3));
        addTeacher(new Teacher("张老师", "英语", 8));
        log.info("TeacherRepository初始化完成，已加载{}名教师", teacherMap.size());
    }
    
    /**
     * 添加教师信息
     * 使用ConcurrentHashMap的线程安全方法保存数据
     * 
     * @param teacher 要添加的教师对象
     */
    public void addTeacher(Teacher teacher) {
        log.info("添加教师: {}", teacher.getName());
        teacherMap.put(teacher.getName(), teacher);
    }
    
    /**
     * 根据姓名查找教师
     * 
     * @param name 教师姓名
     * @return 如果找到返回教师对象，否则返回null
     */
    public Teacher findByName(String name) {
        log.info("查找教师: {}", name);
        return teacherMap.get(name);
    }
    
    /**
     * 获取所有教师列表
     * 返回一个新的ArrayList，避免对原始数据的修改
     * 
     * @return 所有教师的列表
     */
    public List<Teacher> getAllTeachers() {
        return new ArrayList<>(teacherMap.values());
    }
    
    /**
     * 更新教师信息
     * 如果教师不存在，则相当于新增
     * 
     * @param teacher 要更新的教师对象
     */
    public void updateTeacher(Teacher teacher) {
        log.info("更新教师信息: {}", teacher.getName());
        teacherMap.put(teacher.getName(), teacher);
    }
    
    /**
     * 删除教师信息
     * 
     * @param name 要删除的教师姓名
     */
    public void deleteTeacher(String name) {
        log.info("删除教师: {}", name);
        teacherMap.remove(name);
    }
} 