# Spring注解学习 - 教师管理模块

本模块展示了Spring框架中常用注解的高级用法，通过一个完整的教师管理系统来演示各种注解的使用场景和最佳实践。

## 项目结构

```
teacher/
├── entity/          # 实体层
│   └── Teacher.java
├── repository/      # 数据访问层
│   └── TeacherRepository.java
├── service/         # 服务层
│   └── TeacherService.java
└── controller/      # 控制器层
    └── TeacherController.java
```

## 核心注解说明

### 1. 实体层注解
- `@Data`（Lombok）
  - 自动生成getter、setter、equals、hashCode和toString方法
  - 减少样板代码，提高开发效率

- `@Component`
  - 标记类为Spring管理的组件
  - 使得实体类也可以被Spring容器管理，便于注入配置值

- `@Value("${property:defaultValue}")`
  - 注入配置文件中的值
  - 支持默认值设置
  - 示例：`@Value("${teacher.title:讲师}")`

### 2. 数据访问层注解
- `@Repository`
  - 标记类为数据访问层组件
  - Spring会自动处理数据访问异常

- `@Scope("singleton")`
  - 定义bean的作用域
  - singleton：单例模式，整个应用共享一个实例
  - prototype：原型模式，每次注入都创建新实例
  - request：每个HTTP请求创建一个新实例
  - session：每个HTTP会话创建一个新实例

- `@Profile({"dev", "prod"})`
  - 指定bean在哪些环境下被创建
  - 可以配置多个环境
  - 用于区分开发、测试、生产环境

- `@PostConstruct`
  - 在构造方法执行后自动调用
  - 用于初始化操作
  - 比构造方法更适合进行依赖注入后的初始化

### 3. 服务层注解
- `@Service`
  - 标记类为服务层组件
  - 语义化注解，表明类的作用

- `@Lazy`
  - 延迟初始化
  - 第一次使用时才创建实例
  - 可以提高应用启动速度

- `@Async`
  - 标记方法为异步执行
  - 方法将在单独的线程中运行
  - 需要在启动类上添加@EnableAsync

- `@Autowired`
  - 自动注入依赖
  - 建议使用构造方法注入
  - 更好的实现依赖不可变性

### 4. 控制器层注解
- `@RestController`
  - 组合了@Controller和@ResponseBody
  - 将返回值自动转换为JSON

- `@RequestMapping("/api/teachers")`
  - 定义请求URL的基础路径
  - 可以指定请求方法、参数等

- `@CrossOrigin`
  - 允许跨域请求
  - 可以指定���许的源、方法等

- `@GetMapping`, `@PostMapping`, `@PutMapping`, `@DeleteMapping`
  - REST API的HTTP方法映射
  - 是@RequestMapping的便捷方式

- `@PathVariable`
  - 获取URL路径中的参数
  - 示例：`/api/teachers/{name}`

- `@RequestBody`
  - 将请求体转换为对象
  - 通常用于处理POST/PUT请求

## 高级特性

### 1. 异步处理
```java
@Async
public CompletableFuture<List<Teacher>> getAllTeachersAsync() {
    return CompletableFuture.completedFuture(teacherRepository.getAllTeachers());
}
```
- 使用@Async实现异步操作
- 返回CompletableFuture支持异步结果处理
- 提高系统响应性能

### 2. 响应式编程
```java
public ResponseEntity<Teacher> getTeacher(@PathVariable String name) {
    Teacher teacher = teacherService.findTeacher(name);
    return teacher != null ? ResponseEntity.ok(teacher) 
                         : ResponseEntity.notFound().build();
}
```
- 使用ResponseEntity灵活控制响应
- 支持HTTP状态码、头信息等设置
- 更好的REST API实践

### 3. 线程安全
```java
private final Map<String, Teacher> teacherMap = new ConcurrentHashMap<>();
```
- 使用ConcurrentHashMap确保线程安全
- 适用于高并发场景
- 无需额外同步处理

## 配置示例

```yaml
# application.yml
school:
  department: 教务处
teacher:
  title: 讲师
```

## API接口说明

### 1. 获取所有教师（异步）
```http
GET /api/teachers/async
```

### 2. 获取所有教师（同步）
```http
GET /api/teachers
```

### 3. 获取指定教师
```http
GET /api/teachers/{name}
```

### 4. 添加教师
```http
POST /api/teachers
Content-Type: application/json

{
    "name": "新老师",
    "subject": "物理",
    "experience": 3
}
```

### 5. 更新教师信息
```http
PUT /api/teachers/{name}
Content-Type: application/json

{
    "name": "王老师",
    "subject": "高等数学",
    "experience": 6
}
```

### 6. 删除教师
```http
DELETE /api/teachers/{name}
```

## 最佳实践建议

1. 依赖注入优先使用构造方法注入
2. 合理使用@Lazy注解避免循环依赖
3. 异步方法注意异常处理
4. 使用ResponseEntity规范API响应
5. 适当使用日志记录关键操作
6. 配置值使用默认值增加健壮性 