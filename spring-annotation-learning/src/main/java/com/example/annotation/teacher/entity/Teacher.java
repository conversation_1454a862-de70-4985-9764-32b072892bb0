package com.example.annotation.teacher.entity;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 教师实体类
 * 
 * 这个类演示了以下Spring注解的用法：
 * 1. @Data (Lombok) - 自动生成getter、setter、equals、hashCode和toString方法
 * 2. @Component - 将类标记为Spring管理的组件，使其可以被自动扫描和注册
 * 3. @Value - 从配置文件注入值，支持默认值设置
 * 
 * 注意：通常实体类不需要@Component注解，这里使用它主要是为了演示Spring注解的功能
 */
@Data
@Component  
public class Teacher {
    
    /**
     * 教师职称
     * 使用@Value注解从配置文件中读取值
     * ${teacher.title:讲师} 表示：
     * - 优先读取配置文件中的teacher.title属性
     * - 如果配置文件中没有该属性，则使用默认值"讲师"
     */
    @Value("${teacher.title:讲师}")  
    private String title;            

    /**
     * 教师姓名
     * 这是教师的唯一标识符，用于在Map中存储和检索教师信息
     */
    private String name;            

    /**
     * 教师任教的科目
     */
    private String subject;         

    /**
     * 教师的教龄（年）
     */
    private int experience;         
    
    /**
     * 无参构造方法
     * Spring框架需要无参构造方法来创建bean实例
     */
    public Teacher() {
    }
    
    /**
     * 全参数构造方法
     * 用于手动创建教师实例，方便测试和初始化数据
     * 
     * @param name 教师姓名
     * @param subject 任教科目
     * @param experience 教龄
     */
    public Teacher(String name, String subject, int experience) {
        this.name = name;
        this.subject = subject;
        this.experience = experience;
    }
    
    /**
     * 获取教师的完整信息
     * 将教师的所有信息格式化为一个字符串
     * 
     * @return 格式化的教师信息，例如："讲师张三 - 数学（教龄：5年）"
     */
    public String getFullInfo() {
        return String.format("%s%s - %s（教龄：%d年）", 
                title, name, subject, experience);
    }
} 