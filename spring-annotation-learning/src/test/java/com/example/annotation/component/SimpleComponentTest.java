package com.example.annotation.component;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 简单组件测试类
 * 演示如何测试Spring组件
 */
@SpringBootTest  // 创建Spring应用上下文
public class SimpleComponentTest {

    @Autowired  // 注入要测试的组件
    private SimpleComponent simpleComponent;

    @Test
    public void testSayHello() {
        // 调用测试方法
        String result = simpleComponent.sayHello();
        
        // 验证结果不为空
        assertNotNull(result);
        
        // 验证结果包含预期文本
        assertTrue(result.contains("你好"));
    }
} 