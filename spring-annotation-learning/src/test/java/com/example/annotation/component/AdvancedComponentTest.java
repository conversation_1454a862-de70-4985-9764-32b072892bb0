package com.example.annotation.component;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import static org.junit.jupiter.api.Assertions.*;
import java.util.List;

/**
 * 高级组件测试类
 * 展示如何测试包含多个注解的组件
 */
@SpringBootTest                  // 创建Spring应用上下文
@ActiveProfiles("test")         // 激活test配置文件
public class AdvancedComponentTest {

    @Autowired                  // 注入要测试的组件
    private AdvancedComponent advancedComponent;

    @Test
    public void testGetFullInfo() {
        // 测试组合功能
        String result = advancedComponent.getFullInfo();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("你好"));
        assertTrue(result.contains("版本"));
    }

    @Test
    public void testGetServerList() {
        // 测试配置注入
        List<String> servers = advancedComponent.getServerList();
        
        // 验证结果
        assertNotNull(servers);
        assertFalse(servers.isEmpty());
        assertEquals(2, servers.size());  // 默认值中包含两个服务器
    }
} 