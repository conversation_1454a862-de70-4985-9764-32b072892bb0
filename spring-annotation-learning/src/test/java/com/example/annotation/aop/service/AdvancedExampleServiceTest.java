package com.example.annotation.aop.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ContextConfiguration;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 高级示例服务测试类
 * 用于验证各种AOP功能
 */
@SpringBootTest
@ContextConfiguration(classes = AdvancedExampleServiceTest.TestConfig.class)
@Slf4j
class AdvancedExampleServiceTest {

    @Configuration
    @ComponentScan(basePackages = "com.example.annotation.aop")
    static class TestConfig {
    }

    @Autowired
    private AdvancedExampleService advancedExampleService;

    /**
     * 测试缓存功能
     * 多次调用应该只执行一次实际查询
     */
    @Test
    void testGetUserInfo() {
        log.info("=== 测试缓存功能 ===");
        
        // 第一次调用，应该执行实际查询
        String result1 = advancedExampleService.getUserInfo("123");
        assertNotNull(result1);
        
        // 第二次调用，应该直接返回缓存结果
        String result2 = advancedExampleService.getUserInfo("123");
        assertEquals(result1, result2);
    }

    /**
     * 测试参数验证功能
     */
    @Test
    void testValidateUsername() {
        log.info("=== 测试参数验证功能 ===");
        
        // 测试有效用户名
        assertDoesNotThrow(() -> {
            advancedExampleService.validateUsername("validuser");
        });
        
        // 测试无效用户名（太短）
        assertThrows(IllegalArgumentException.class, () -> {
            advancedExampleService.validateUsername("ab");
        });
        
        // 测试无效用户名（太长）
        assertThrows(IllegalArgumentException.class, () -> {
            advancedExampleService.validateUsername("thisusernameistoolong123456");
        });
    }

    /**
     * 测试审计日志功能
     */
    @Test
    void testUpdateUser() {
        log.info("=== 测试审计日志功能 ===");
        
        String result = advancedExampleService.updateUser("123", "新用户名");
        assertEquals("用户信息更新成功", result);
    }

    /**
     * 测试多个切面的组合使用
     */
    @Test
    void testGetUserRole() {
        log.info("=== 测试多个切面组合 ===");
        
        // 第一次调用
        String role1 = advancedExampleService.getUserRole("123");
        assertNotNull(role1);
        
        // 第二次调用（应该使用缓存）
        String role2 = advancedExampleService.getUserRole("123");
        assertEquals(role1, role2);
        
        // 测试参数验证
        assertThrows(IllegalArgumentException.class, () -> {
            advancedExampleService.getUserRole("1");  // 长度小于最小值
        });
    }
} 