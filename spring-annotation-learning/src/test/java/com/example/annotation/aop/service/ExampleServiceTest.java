package com.example.annotation.aop.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.access.AccessDeniedException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 示例服务测试类
 * 用于验证AOP功能
 */
@SpringBootTest
@Slf4j
class ExampleServiceTest {

    @Autowired
    private ExampleService exampleService;

    /**
     * 测试方法执行时间记录
     */
    @Test
    void testPerformTimeConsumingOperation() {
        log.info("测试耗时操作记录");
        assertDoesNotThrow(() -> {
            exampleService.performTimeConsumingOperation();
        });
    }

    /**
     * 测试权限检查
     */
    @Test
    void testPerformSecureOperation() {
        log.info("测试权限检查");
        // 由于示例中hasPermission总是返回true，所以不会抛出异常
        assertDoesNotThrow(() -> {
            exampleService.performSecureOperation();
        });
        
        // 如果要测试权限检查失败的情况，需要修改SecurityAspect中的hasPermission方法
        // assertThrows(AccessDeniedException.class, () -> {
        //     exampleService.performSecureOperation();
        // });
    }

    /**
     * 测试异常处理
     */
    @Test
    void testPerformOperationWithException() {
        log.info("测试异常处理");
        Exception exception = assertThrows(RuntimeException.class, () -> {
            exampleService.performOperationWithException();
        });
        assertEquals("操作失败", exception.getMessage());
    }

    /**
     * 测试正常方法调用
     */
    @Test
    void testPerformNormalOperation() {
        log.info("测试正常方法调用");
        String result = exampleService.performNormalOperation("测试输入");
        assertEquals("处理结果: 测试输入", result);
    }
} 