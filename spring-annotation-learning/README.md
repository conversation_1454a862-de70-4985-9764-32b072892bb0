# Spring 注解学习模块

这个模块专门用于学习和演示 Spring Framework 中常用的注解。通过实际的代码示例，帮助你理解各种 Spring 注解的用法和最佳实践。

## 项目结构

```
spring-annotation-learning
├── src/main/java/com/example/annotation
│   ├── SpringAnnotationApplication.java  # 主应用类
│   ├── config                           # 配置相关
│   │   └── AppConfig.java               # 配置类示例
│   ├── component                        # 组件相关
│   │   ├── UserService.java            # 服务层示例
│   │   └── UserRepository.java         # 数据访问层示例
│   └── controller                       # 控制器相关
│       └── UserController.java         # REST控制器示例
└── src/main/resources
    └── application.yml                  # 应用配置文件
```

## 演示的注解

### 1. 核心注解
- `@SpringBootApplication`: 标记主应用类，组合了 `@Configuration`、`@EnableAutoConfiguration` 和 `@ComponentScan`
- `@Configuration`: 标记配置类
- `@Bean`: 注册 bean 到 Spring 容器
- `@Component`: 通用组件标记
- `@Service`: 服务层组件标记
- `@Repository`: 数据访问层组件标记
- `@Controller`/`@RestController`: 控制器组件标记

### 2. 依赖注入相关
- `@Autowired`: 自动注入依赖
- `@Value`: 注入配置属性
- `@Qualifier`: 指定注入的 bean 名称
- `@Primary`: 设置首选 bean

### 3. Web 相关
- `@RequestMapping`: 映射 Web 请求
- `@GetMapping`: 处理 GET 请求
- `@PostMapping`: 处理 POST 请求
- `@RequestBody`: 接收请求体
- `@RequestParam`: 接收请求参数
- `@PathVariable`: 接收路径参数

### 4. 配置相关
- `@Profile`: 指定在特定环境下激活
- `@Scope`: 定义 bean 的作用域
- `@PropertySource`: 指定配置文件位置
- `@ConfigurationProperties`: 批量注入配置属性

### 5. 生命周期相关
- `@PostConstruct`: 在构造后执行
- `@PreDestroy`: 在销毁前执行

## 快速开始

1. 构建项目
```bash
mvn clean install
```

2. 运行应用
```bash
mvn spring-boot:run
```

3. 测试接口
```bash
curl http://localhost:8080/api/users/info
```

## 使用示例

### 1. 配置类示例
```java
@Configuration
public class AppConfig {
    @Bean
    @Scope("singleton")
    public MyService myService() {
        return new MyService();
    }
}
```

### 2. ���务类示例
```java
@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;
    
    @Value("${app.service.name}")
    private String serviceName;
}
```

### 3. 控制器示例
```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    @GetMapping("/{id}")
    public User getUser(@PathVariable Long id) {
        return userService.getUser(id);
    }
}
```

## 最佳实践

1. 依赖注入优先使用构造器注入
2. 配置属性使用 `@ConfigurationProperties` 而不是多个 `@Value`
3. 使用 `@Slf4j` 进行日志记录
4. 合理使用 `@Profile` 进行环境配置
5. 使用 `@Validated` 进行参数校验

## 环境要求

- JDK 17 或以上
- Maven 3.6 或以上
- Spring Boot 2.7.x

## 参考文档

- [Spring Framework Documentation](https://docs.spring.io/spring-framework/docs/current/reference/html/)
- [Spring Boot Documentation](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [Spring Annotations Cheat Sheet](https://springframework.guru/spring-framework-annotations/) 