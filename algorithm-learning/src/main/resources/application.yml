spring:
  application:
    name: algorithm-learning

common:
  logging:
    enabled: true
    log-path: logs
    level: INFO
    module-name: ${spring.application.name}
    pattern: "%d{MM-dd HH:mm:ss} [%-5thread] %-5level %logger{36} [%X{traceId}] >>> %msg%n"
    max-history: 30
    max-file-size: 100MB
    enable-console: true
    package-levels:
      com.example.algorithm: DEBUG
    mdc:
      id-generator: snowflake
      max-length: 32
      enable-prefix: true
      prefix: "ALG-"
      include-timestamp: true
      timestamp-format: "MMddHHmmss"
    snowflake:
      worker-id: 1
      data-center-id: 1
      sequence: 0 