package com.example.algorithm.sorting;

import lombok.extern.slf4j.Slf4j;

/**
 * 排序算法可视化工具
 * 用于展示排序算法的执行过程
 */
@Slf4j
public class SortingVisualizer {

    /**
     * 打印数组
     */
    private static void printArray(int[] arr, String message) {
        log.info(message + ": {}", java.util.Arrays.toString(arr));
    }

    /**
     * 可视化冒泡排序过程
     */
    public static void visualizeBubbleSort(int[] arr) {
        log.info("\n========= 冒泡排序过程演示 =========");
        int[] array = arr.clone();
        printArray(array, "原始数组");
        
        int n = array.length;
        int step = 1;
        
        for (int i = 0; i < n - 1; i++) {
            boolean swapped = false;
            log.info("\n第{}轮冒泡：", step);
            
            for (int j = 0; j < n - i - 1; j++) {
                // 打印比较的元素
                log.info("比较 {} 和 {}: ", array[j], array[j + 1]);
                
                if (array[j] > array[j + 1]) {
                    // 交换元素
                    int temp = array[j];
                    array[j] = array[j + 1];
                    array[j + 1] = temp;
                    swapped = true;
                    log.info("需要交换");
                    printArray(array, "交换后");
                } else {
                    log.info("不需要交换");
                }
            }
            
            if (!swapped) {
                log.info("本轮没有发生交换，数组已经有序");
                break;
            }
            step++;
        }
        
        printArray(array, "\n最终结果");
    }

    /**
     * 可视化选择排序过程
     */
    public static void visualizeSelectionSort(int[] arr) {
        log.info("\n========= 选择排序过程演示 =========");
        int[] array = arr.clone();
        printArray(array, "原始数组");
        
        int n = array.length;
        
        for (int i = 0; i < n - 1; i++) {
            log.info("\n第{}轮选择：", i + 1);
            int minIndex = i;
            log.info("当前位置 {}，值为 {}", i, array[i]);
            
            // 找最小值
            for (int j = i + 1; j < n; j++) {
                log.info("比较 {} 和 {}: ", array[minIndex], array[j]);
                if (array[j] < array[minIndex]) {
                    minIndex = j;
                    log.info("找到新的最小值 {}", array[j]);
                } else {
                    log.info("保持原最小值");
                }
            }
            
            // 交换到正确位置
            if (minIndex != i) {
                log.info("交换位置 {} 和位置 {} 的元素", i, minIndex);
                int temp = array[i];
                array[i] = array[minIndex];
                array[minIndex] = temp;
                printArray(array, "交换后");
            } else {
                log.info("当前元素已经是最小值，无需交换");
            }
        }
        
        printArray(array, "\n最终结果");
    }

    /**
     * 可视化插入排序过程
     */
    public static void visualizeInsertionSort(int[] arr) {
        log.info("\n========= 插入排序过程演示 =========");
        int[] array = arr.clone();
        printArray(array, "原始数组");
        
        for (int i = 1; i < array.length; i++) {
            log.info("\n处理第{}个元素({})：", i + 1, array[i]);
            int key = array[i];
            int j = i - 1;
            
            log.info("将 {} 插入到已排序区间 ", key);
            printArray(java.util.Arrays.copyOfRange(array, 0, i), "");
            
            while (j >= 0 && array[j] > key) {
                log.info("将 {} 向右移动一位", array[j]);
                array[j + 1] = array[j];
                j--;
            }
            
            array[j + 1] = key;
            printArray(array, "插入后");
        }
        
        printArray(array, "\n最终结果");
    }

    public static void main(String[] args) {
        int[] testArray = {64, 34, 25, 12, 22};
        
        // 演示冒泡排序
        visualizeBubbleSort(testArray.clone());
        
        // 演示选择排序
        visualizeSelectionSort(testArray.clone());
        
        // 演示插入排序
        visualizeInsertionSort(testArray.clone());
    }
} 