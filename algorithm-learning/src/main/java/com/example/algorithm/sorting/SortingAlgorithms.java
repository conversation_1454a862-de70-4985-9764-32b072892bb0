package com.example.algorithm.sorting;

import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 基础排序算法实现类
 * 包含了常见的排序算法实现，每个算法都有详细的说明、时间复杂度和空间复杂度分析
 */
@Slf4j
public class SortingAlgorithms {

    /**
     * 冒泡排序
     * 原理：通过相邻元素的比较和交换，将最大的元素逐步"冒泡"到数组末尾
     * 特点：
     * 1. 稳定排序
     * 2. 原地排序
     * 3. 适合小规模数据
     * 时间复杂度: O(n^2)
     * 空间复杂度: O(1)
     *
     * @param arr 待排序数组
     * @return 排序后的数组
     */
    public static int[] bubbleSort(int[] arr) {
        log.debug("开始冒泡排序，数组长度：{}", arr.length);
        int n = arr.length;
        boolean swapped;

        for (int i = 0; i < n - 1; i++) {
            swapped = false;
            // 每次循环将未排序部分的最大值冒泡到末尾
            for (int j = 0; j < n - i - 1; j++) {
                if (arr[j] > arr[j + 1]) {
                    // 交换 arr[j] 和 arr[j+1]
                    int temp = arr[j];
                    arr[j] = arr[j + 1];
                    arr[j + 1] = temp;
                    swapped = true;
                }
            }
            // 如果内层循环未发生交换，则数组已经有序
            if (!swapped) {
                break;
            }
        }
        log.debug("冒泡排序完成");
        return arr;
    }

    /**
     * 选择排序
     * 原理：每次从未排序区间选择最小的元素，放到已排序区间的末尾
     * 特点：
     * 1. 不稳定排序
     * 2. 原地排序
     * 3. 交换次数少于冒泡排序
     * 时间复杂度: O(n^2)
     * 空间复杂度: O(1)
     *
     * @param arr 待排序数组
     * @return 排序后的数组
     */
    public static int[] selectionSort(int[] arr) {
        log.debug("开始选择排序，数组长度：{}", arr.length);
        int n = arr.length;

        for (int i = 0; i < n - 1; i++) {
            // 找到未排序区间中的最小值
            int minIndex = i;
            for (int j = i + 1; j < n; j++) {
                if (arr[j] < arr[minIndex]) {
                    minIndex = j;
                }
            }
            // 将最小值放到已排序区间的末尾
            if (minIndex != i) {
                int temp = arr[i];
                arr[i] = arr[minIndex];
                arr[minIndex] = temp;
            }
        }
        log.debug("选择排序完成");
        return arr;
    }

    /**
     * 插入排序
     * 原理：将未排序区间的元素逐个插入到已排序区间的合适位置
     * 特点：
     * 1. 稳定排序
     * 2. 原地排序
     * 3. 适合小规模数据和基本有序的数据
     * 时间复杂度: O(n^2)，最好情况O(n)
     * 空间复杂度: O(1)
     *
     * @param arr 待排序数组
     * @return 排序后的数组
     */
    public static int[] insertionSort(int[] arr) {
        log.debug("开始插入排序，数组长度：{}", arr.length);
        int n = arr.length;

        for (int i = 1; i < n; i++) {
            int key = arr[i];
            int j = i - 1;
            // 将大于key的元素都向后移动一位
            while (j >= 0 && arr[j] > key) {
                arr[j + 1] = arr[j];
                j--;
            }
            arr[j + 1] = key;
        }
        log.debug("插入排序完成");
        return arr;
    }

    /**
     * 归并排序
     * 原理：将数组分成两半，分别排序后再合并
     * 特点：
     * 1. 稳定排序
     * 2. 非原地排序
     * 3. 分治思想的典型应用
     * 时间复杂度: O(n log n)
     * 空间复杂度: O(n)
     *
     * @param arr 待排序数组
     * @return 排序后的数组
     */
    public static int[] mergeSort(int[] arr) {
        log.debug("开始归并排序，数组长度：{}", arr.length);
        if (arr.length <= 1) {
            return arr;
        }
        mergeSort(arr, 0, arr.length - 1);
        log.debug("归并排序完成");
        return arr;
    }

    private static void mergeSort(int[] arr, int left, int right) {
        if (left < right) {
            int mid = left + (right - left) / 2;
            // 分别排序左右两半
            mergeSort(arr, left, mid);
            mergeSort(arr, mid + 1, right);
            // 合并两个有序数组
            merge(arr, left, mid, right);
        }
    }

    private static void merge(int[] arr, int left, int mid, int right) {
        // 创建临时数组
        int[] temp = new int[right - left + 1];
        int i = left;     // 左半部分起始索引
        int j = mid + 1;  // 右半部分起始索引
        int k = 0;        // 临时数组索引

        // 合并两个有序数组
        while (i <= mid && j <= right) {
            if (arr[i] <= arr[j]) {
                temp[k++] = arr[i++];
            } else {
                temp[k++] = arr[j++];
            }
        }

        // 处理剩余元素
        while (i <= mid) {
            temp[k++] = arr[i++];
        }
        while (j <= right) {
            temp[k++] = arr[j++];
        }

        // 将临时数组中的元素复制回原数组
        for (i = 0; i < temp.length; i++) {
            arr[left + i] = temp[i];
        }
    }

    /**
     * 堆排序
     * 原理：利用堆这种数据结构所设计的排序算法
     * 特点：
     * 1. 不稳定排序
     * 2. 原地排序
     * 3. 适合大规模数据排序
     * 时间复杂度: O(n log n)
     * 空间复杂度: O(1)
     *
     * @param arr 待排序数组
     * @return 排序后的数组
     */
    public static int[] heapSort(int[] arr) {
        log.debug("开始堆排序，数组长度：{}", arr.length);
        int n = arr.length;

        // 构建最大堆
        for (int i = n / 2 - 1; i >= 0; i--) {
            heapify(arr, n, i);
        }

        // 逐个从堆中取出最大值
        for (int i = n - 1; i > 0; i--) {
            // 将堆顶元素（最大值）与末尾元素交换
            int temp = arr[0];
            arr[0] = arr[i];
            arr[i] = temp;

            // 对剩余元素重新构建最大堆
            heapify(arr, i, 0);
        }
        log.debug("堆排序完成");
        return arr;
    }

    private static void heapify(int[] arr, int n, int i) {
        int largest = i;      // 初始化最大值为根节点
        int left = 2 * i + 1;  // 左子节点
        int right = 2 * i + 2; // 右子节点

        // 如果左子节点大于根节点
        if (left < n && arr[left] > arr[largest]) {
            largest = left;
        }

        // 如果右子节点大于最大值
        if (right < n && arr[right] > arr[largest]) {
            largest = right;
        }

        // 如果最大值不是根节点
        if (largest != i) {
            int swap = arr[i];
            arr[i] = arr[largest];
            arr[largest] = swap;

            // 递归地构建受影响的子堆
            heapify(arr, n, largest);
        }
    }

    /**
     * 快速排序
     * 原理：选择一个基准值，将数组分成小于基准值和大于基准值的两部分
     * 特点：
     * 1. 不稳定排序
     * 2. 原地排序
     * 3. 平均情况下最快的排序算法
     * 时间复杂度: 平均O(n log n)，最坏O(n^2)
     * 空间复杂度: O(log n)
     *
     * @param arr 待排序数组
     * @return 排序后的数组
     */
    public static int[] quickSort(int[] arr) {
        log.debug("开始快速排序，数组长度：{}", arr.length);
        quickSort(arr, 0, arr.length - 1);
        log.debug("快速排序完成");
        return arr;
    }

    private static void quickSort(int[] arr, int low, int high) {
        if (low < high) {
            // 获取分区点
            int pi = partition(arr, low, high);
            // 分别对分区点左右两部分进行排序
            quickSort(arr, low, pi - 1);
            quickSort(arr, pi + 1, high);
        }
    }

    private static int partition(int[] arr, int low, int high) {
        // 选择最右边的元素作为基准值
        int pivot = arr[high];
        int i = (low - 1); // 小于基准值的区域边界

        // 将小于基准值的元素移到左边
        for (int j = low; j < high; j++) {
            if (arr[j] <= pivot) {
                i++;
                int temp = arr[i];
                arr[i] = arr[j];
                arr[j] = temp;
            }
        }

        // 将基准值放到正确的位置
        int temp = arr[i + 1];
        arr[i + 1] = arr[high];
        arr[high] = temp;

        return i + 1;
    }

    /**
     * 希尔排序
     * 原理：是插入排序的一种更高效的改进版本，先将整个待排序的记录序列分割成为若干子序列分别进行直接插入排序
     * 特点：
     * 1. 不稳定排序
     * 2. 原地排序
     * 3. 对于中等规模数据性能好
     * 时间复杂度: 平均O(n^1.3)
     * 空间复杂度: O(1)
     *
     * @param arr 待排序数组
     * @return 排序后的数组
     */
    public static int[] shellSort(int[] arr) {
        log.debug("开始希尔排序，数组长度：{}", arr.length);
        int n = arr.length;
        
        // 初始增量为数组长度的一半，每次减半
        for (int gap = n/2; gap > 0; gap /= 2) {
            // 对每个子序列进行插入排序
            for (int i = gap; i < n; i++) {
                int temp = arr[i];
                int j;
                // 对子序列进行插入排序
                for (j = i; j >= gap && arr[j - gap] > temp; j -= gap) {
                    arr[j] = arr[j - gap];
                }
                arr[j] = temp;
            }
        }
        log.debug("希尔排序完成");
        return arr;
    }

    /**
     * 计数排序
     * 原理：统计小于等于每个元素的个数，根据统计结果将元素放到正确的位置
     * 特点：
     * 1. 稳定排序
     * 2. 非原地排序
     * 3. 适用于已知整数范围的排序
     * 时间复杂度: O(n + k)，k为整数范围
     * 空间复杂度: O(k)
     *
     * @param arr 待排序数组
     * @return 排序后的数组
     */
    public static int[] countingSort(int[] arr) {
        log.debug("开始计数排序，数组长度：{}", arr.length);
        if (arr.length == 0) return arr;
        
        // 找出数组中的最大值和最小值
        int max = arr[0], min = arr[0];
        for (int i = 1; i < arr.length; i++) {
            if (arr[i] > max) max = arr[i];
            if (arr[i] < min) min = arr[i];
        }
        
        // 创建计数数组
        int range = max - min + 1;
        int[] count = new int[range];
        
        // 统计每个元素出现的次数
        for (int num : arr) {
            count[num - min]++;
        }
        
        // 计算每个元素的最终位置
        for (int i = 1; i < range; i++) {
            count[i] += count[i - 1];
        }
        
        // 创建结果数组
        int[] result = new int[arr.length];
        
        // 从后向前遍历原数组，保证稳定性
        for (int i = arr.length - 1; i >= 0; i--) {
            result[count[arr[i] - min] - 1] = arr[i];
            count[arr[i] - min]--;
        }
        
        // 将结果复制回原数组
        System.arraycopy(result, 0, arr, 0, arr.length);
        
        log.debug("计数排序完成");
        return arr;
    }

    /**
     * 基数排序
     * 原理：按照个位、十位、百位等逐位排序
     * 特点：
     * 1. 稳定排序
     * 2. 非原地排序
     * 3. 适用于整数或字符串
     * 时间复杂度: O(d * (n + k))，d为位数，k为基数（通常为10）
     * 空间复杂度: O(n + k)
     *
     * @param arr 待排序数组
     * @return 排序后的数组
     */
    public static int[] radixSort(int[] arr) {
        log.debug("开始基数排序，数组长度：{}", arr.length);
        if (arr.length == 0) return arr;
        
        // 找出最大值
        int max = arr[0];
        for (int i = 1; i < arr.length; i++) {
            if (arr[i] > max) max = arr[i];
        }
        
        // 对每一位进行计数排序
        for (int exp = 1; max/exp > 0; exp *= 10) {
            countingSortByDigit(arr, exp);
        }
        
        log.debug("基数排序完成");
        return arr;
    }
    
    private static void countingSortByDigit(int[] arr, int exp) {
        int[] output = new int[arr.length];
        int[] count = new int[10];  // 0-9的计数器
        
        // 统计当前位上每个数字出现的次数
        for (int num : arr) {
            count[(num/exp) % 10]++;
        }
        
        // 计算每个数字的最终位置
        for (int i = 1; i < 10; i++) {
            count[i] += count[i - 1];
        }
        
        // 构建输出数组
        for (int i = arr.length - 1; i >= 0; i--) {
            output[count[(arr[i]/exp) % 10] - 1] = arr[i];
            count[(arr[i]/exp) % 10]--;
        }
        
        // 将排序结果复制回原数组
        System.arraycopy(output, 0, arr, 0, arr.length);
    }

    /**
     * 桶排序
     * 原理：将数组分到有限数量的桶中，每个桶再分别排序
     * 特点：
     * 1. 稳定排序（取决于桶内排序算法）
     * 2. 非原地排序
     * 3. 适用于均匀分布的数据
     * 时间复杂度: 平均O(n + k)，k为桶的数量
     * 空间复杂度: O(n + k)
     *
     * @param arr 待排序数组
     * @param bucketSize 桶的大小
     * @return 排序后的数组
     */
    public static int[] bucketSort(int[] arr, int bucketSize) {
        log.debug("开始桶排序，数组长度：{}，桶大小：{}", arr.length, bucketSize);
        if (arr.length == 0) return arr;
        
        // 找出最大值和最小值
        int minValue = arr[0];
        int maxValue = arr[0];
        for (int i = 1; i < arr.length; i++) {
            if (arr[i] < minValue) {
                minValue = arr[i];
            } else if (arr[i] > maxValue) {
                maxValue = arr[i];
            }
        }
        
        // 创建桶
        int bucketCount = (maxValue - minValue) / bucketSize + 1;
        List<List<Integer>> buckets = new ArrayList<>(bucketCount);
        for (int i = 0; i < bucketCount; i++) {
            buckets.add(new ArrayList<>());
        }
        
        // 将数组元素分配到桶中
        for (int num : arr) {
            int bucketIndex = (num - minValue) / bucketSize;
            buckets.get(bucketIndex).add(num);
        }
        
        // 对每个桶内部进行排序
        int currentIndex = 0;
        for (List<Integer> bucket : buckets) {
            if (bucket.isEmpty()) continue;
            
            // 使用Collections.sort()对桶内元素排序
            Collections.sort(bucket);
            
            // 将桶内元素放回原数组
            for (int value : bucket) {
                arr[currentIndex++] = value;
            }
        }
        
        log.debug("桶排序完成");
        return arr;
    }
}