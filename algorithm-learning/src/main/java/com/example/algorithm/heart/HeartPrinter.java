package com.example.algorithm.heart;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HeartPrinter {

    private static final String LOVE_TEXT = "I LOVE YOU! ";
    private static final String PINK_COLOR = "\u001B[38;5;205m"; // 粉红色
    private static final String RESET_COLOR = "\u001B[0m";       // 重置颜色
    private static final double Y_STEP = 0.04;                  // 控制心形高度精度（小步长更精细）
    private static final double X_STEP = 0.02;                  // 控制心形宽度精度（小步长更精细）

    /**
     * 判断点是否在爱心形状内
     *
     * @param x 横坐标
     * @param y 纵坐标
     * @return 如果点位于爱心区域内，返回 true，否则返回 false
     */
    private static boolean isInHeartShape(double x, double y) {
        double x2 = x * x;
        double y2 = y * y;
        return Math.pow(x2 + y2 - 1, 3) - x2 * Math.pow(y, 3) <= 0.0;
    }

    /**
     * 打印爱心图案
     */
    public static void printHeart() {
        log.info("开始打印爱心图案");

        StringBuilder result = new StringBuilder();
        int textIndex = 0;

        for (double y = 1.2; y > -1.2; y -= Y_STEP) { // 控制纵向精细度
            for (double x = -1.5; x < 1.5; x += X_STEP) { // 控制横向比例
                if (isInHeartShape(x, y)) {
                    // 爱心内区域打印字符
                    result.append(LOVE_TEXT.charAt(textIndex % LOVE_TEXT.length()));
                    textIndex++;
                } else {
                    // 非爱心区域打印空格
                    result.append(" ");
                }
            }
            result.append("\n");
        }

        // 设置文本颜色为粉红色并打印
        System.out.print(PINK_COLOR);
        System.out.println(result);
        System.out.print(RESET_COLOR); // 重置颜色

        log.info("爱心图案打印完成");
    }

    public static void main(String[] args) {
        printHeart();
    }
}
