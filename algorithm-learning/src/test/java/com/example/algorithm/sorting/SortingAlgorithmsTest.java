package com.example.algorithm.sorting;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@DisplayName("排序算法测试类")
class SortingAlgorithmsTest {

    // 提供各种测试场景的数组
    private static Stream<Arguments> provideArraysForSorting() {
        return Stream.of(
            Arguments.of("空数组", new int[]{}),
            Arguments.of("单元素数组", new int[]{1}),
            Arguments.of("已排序数组", new int[]{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}),
            Arguments.of("逆序数组", new int[]{10, 9, 8, 7, 6, 5, 4, 3, 2, 1}),
            Arguments.of("随机数组", new int[]{64, 34, 25, 12, 22, 11, 90}),
            Arguments.of("包含重复元素", new int[]{3, 1, 4, 1, 5, 9, 2, 6, 5, 3}),
            Arguments.of("包含负数", new int[]{-5, -10, 0, 5, 10, -15, 15})
        );
    }

    @ParameterizedTest(name = "冒泡排序：{0}")
    @MethodSource("provideArraysForSorting")
    @DisplayName("测试冒泡排序在不同场景下的表现")
    void testBubbleSort(String scenario, int[] input) {
        int[] expected = input.clone();
        Arrays.sort(expected);
        int[] actual = SortingAlgorithms.bubbleSort(input.clone());
        assertArrayEquals(expected, actual, "冒泡排序失败：" + scenario);
    }

    @ParameterizedTest(name = "选择排序：{0}")
    @MethodSource("provideArraysForSorting")
    @DisplayName("测试选择排序在不同场景下的表现")
    void testSelectionSort(String scenario, int[] input) {
        int[] expected = input.clone();
        Arrays.sort(expected);
        int[] actual = SortingAlgorithms.selectionSort(input.clone());
        assertArrayEquals(expected, actual, "选择排序失败：" + scenario);
    }

    @ParameterizedTest(name = "插入排序：{0}")
    @MethodSource("provideArraysForSorting")
    @DisplayName("测试插入排序在不同场景下的表现")
    void testInsertionSort(String scenario, int[] input) {
        int[] expected = input.clone();
        Arrays.sort(expected);
        int[] actual = SortingAlgorithms.insertionSort(input.clone());
        assertArrayEquals(expected, actual, "插入排序失败：" + scenario);
    }

    @ParameterizedTest(name = "归并排序：{0}")
    @MethodSource("provideArraysForSorting")
    @DisplayName("测试归并排序在不同场景下的表现")
    void testMergeSort(String scenario, int[] input) {
        int[] expected = input.clone();
        Arrays.sort(expected);
        int[] actual = SortingAlgorithms.mergeSort(input.clone());
        assertArrayEquals(expected, actual, "归并排序失败：" + scenario);
    }

    @ParameterizedTest(name = "堆排序：{0}")
    @MethodSource("provideArraysForSorting")
    @DisplayName("测试堆排序在不同场景下的表现")
    void testHeapSort(String scenario, int[] input) {
        int[] expected = input.clone();
        Arrays.sort(expected);
        int[] actual = SortingAlgorithms.heapSort(input.clone());
        assertArrayEquals(expected, actual, "堆排序失败：" + scenario);
    }

    @ParameterizedTest(name = "快速排序：{0}")
    @MethodSource("provideArraysForSorting")
    @DisplayName("测试快速排序在不同场景下的表现")
    void testQuickSort(String scenario, int[] input) {
        int[] expected = input.clone();
        Arrays.sort(expected);
        int[] actual = SortingAlgorithms.quickSort(input.clone());
        assertArrayEquals(expected, actual, "快速排序失败：" + scenario);
    }

    @ParameterizedTest(name = "希尔排序：{0}")
    @MethodSource("provideArraysForSorting")
    @DisplayName("测试希尔排序在不同场景下的表现")
    void testShellSort(String scenario, int[] input) {
        int[] expected = input.clone();
        Arrays.sort(expected);
        int[] actual = SortingAlgorithms.shellSort(input.clone());
        assertArrayEquals(expected, actual, "希尔排序失败：" + scenario);
    }

    @ParameterizedTest(name = "计数排序：{0}")
    @MethodSource("provideArraysForSorting")
    @DisplayName("测试计数排序在不同场景下的表现")
    void testCountingSort(String scenario, int[] input) {
        int[] expected = input.clone();
        Arrays.sort(expected);
        int[] actual = SortingAlgorithms.countingSort(input.clone());
        assertArrayEquals(expected, actual, "计数排序失败：" + scenario);
    }

    @ParameterizedTest(name = "基数排序：{0}")
    @MethodSource("provideArraysForSorting")
    @DisplayName("测试基数排序在不同场景下的表现")
    void testRadixSort(String scenario, int[] input) {
        // 基数排序只能处理非负数
        if (Arrays.stream(input).anyMatch(x -> x < 0)) {
            return;
        }
        int[] expected = input.clone();
        Arrays.sort(expected);
        int[] actual = SortingAlgorithms.radixSort(input.clone());
        assertArrayEquals(expected, actual, "基数排序失败：" + scenario);
    }

    @ParameterizedTest(name = "桶排序：{0}")
    @MethodSource("provideArraysForSorting")
    @DisplayName("测试桶排序在不同场景下的表现")
    void testBucketSort(String scenario, int[] input) {
        int[] expected = input.clone();
        Arrays.sort(expected);
        int[] actual = SortingAlgorithms.bucketSort(input.clone(), 5); // 使用大小为5的桶
        assertArrayEquals(expected, actual, "桶排序失败：" + scenario);
    }

    @Test
    @DisplayName("测试特殊场景下的计数排序")
    void testCountingSortSpecialCases() {
        // 测试大范围的数据
        int[] largeRange = {1000, 2, 5000, 1, 3000};
        int[] expected = largeRange.clone();
        Arrays.sort(expected);
        assertArrayEquals(expected, SortingAlgorithms.countingSort(largeRange.clone()), 
            "计数排序处理大范围数据失败");

        // 测试全部相同的数据
        int[] allSame = {5, 5, 5, 5, 5};
        assertArrayEquals(allSame, SortingAlgorithms.countingSort(allSame.clone()), 
            "计数排序处理相同数据失败");
    }

    @Test
    @DisplayName("测试特殊场景下的基数排序")
    void testRadixSortSpecialCases() {
        // 测试不同位数的数据
        int[] mixedDigits = {1, 10, 100, 1000, 10000};
        int[] expected = mixedDigits.clone();
        Arrays.sort(expected);
        assertArrayEquals(expected, SortingAlgorithms.radixSort(mixedDigits.clone()), 
            "基数排序处理不同位数数据失败");

        // 测试全部个位数
        int[] singleDigit = {9, 5, 2, 7, 1, 3};
        expected = singleDigit.clone();
        Arrays.sort(expected);
        assertArrayEquals(expected, SortingAlgorithms.radixSort(singleDigit.clone()), 
            "基数排序处理个位数数据失败");
    }

    @Test
    @DisplayName("测试特殊场景下的桶排序")
    void testBucketSortSpecialCases() {
        // 测试不同桶大小的效果
        int[] data = {29, 25, 3, 49, 9, 37, 21, 43};
        int[] expected = data.clone();
        Arrays.sort(expected);

        // 使用不同的桶大小
        assertArrayEquals(expected, SortingAlgorithms.bucketSort(data.clone(), 5), 
            "桶排序（桶大小=5）失败");
        assertArrayEquals(expected, SortingAlgorithms.bucketSort(data.clone(), 10), 
            "桶排序（桶大小=10）失败");
        assertArrayEquals(expected, SortingAlgorithms.bucketSort(data.clone(), 25), 
            "桶排序（桶大小=25）失败");
    }

    @Test
    @DisplayName("测试大规模数据排序性能")
    void testSortingPerformance() {
        // 创建一个大规模随机数组
        int size = 10000;
        int[] largeArray = new Random().ints(size).toArray();
        int[] expected = largeArray.clone();
        Arrays.sort(expected);

        // 测试各种排序算法的性能
        long startTime, endTime;

        // 快速排序
        startTime = System.nanoTime();
        int[] quickSorted = SortingAlgorithms.quickSort(largeArray.clone());
        endTime = System.nanoTime();
        assertArrayEquals(expected, quickSorted, "快速排序结果错误");
        System.out.printf("快速排序耗时: %.2f ms%n", (endTime - startTime) / 1_000_000.0);

        // 归并排序
        startTime = System.nanoTime();
        int[] mergeSorted = SortingAlgorithms.mergeSort(largeArray.clone());
        endTime = System.nanoTime();
        assertArrayEquals(expected, mergeSorted, "归并排序结果错误");
        System.out.printf("归并排序耗时: %.2f ms%n", (endTime - startTime) / 1_000_000.0);

        // 堆排序
        startTime = System.nanoTime();
        int[] heapSorted = SortingAlgorithms.heapSort(largeArray.clone());
        endTime = System.nanoTime();
        assertArrayEquals(expected, heapSorted, "堆排序结果错误");
        System.out.printf("堆排序耗时: %.2f ms%n", (endTime - startTime) / 1_000_000.0);
    }

    /**
     * 用于稳定性测试的内部类
     */
    private static class StabilityTestElement implements Comparable<StabilityTestElement> {
        final int value;
        final int originalIndex;

        StabilityTestElement(int value, int originalIndex) {
            this.value = value;
            this.originalIndex = originalIndex;
        }

        @Override
        public int compareTo(StabilityTestElement other) {
            return Integer.compare(this.value, other.value);
        }
    }

    @Test
    @DisplayName("测试冒泡排序的稳定性")
    void testBubbleSortStability() {
        // 创建测试数据：[5, 3, 5, 1, 3]
        StabilityTestElement[] arr = new StabilityTestElement[]{
            new StabilityTestElement(5, 0),
            new StabilityTestElement(3, 1),
            new StabilityTestElement(5, 2),
            new StabilityTestElement(1, 3),
            new StabilityTestElement(3, 4)
        };

        // 记录相等元素的原始位置
        String originalOrder = getStabilityOrder(arr);

        // 使用冒泡排序
        bubbleSortWithStability(arr);

        // 检查排序后相等元素的相对位置是否保持不变
        String sortedOrder = getStabilityOrder(arr);
        assertEquals(originalOrder, sortedOrder, "冒泡排序的稳定性测试失败");
        
        // 验证排序结果的正确性
        for (int i = 0; i < arr.length - 1; i++) {
            assertTrue(arr[i].value <= arr[i + 1].value, "排序结果不正确");
        }
    }

    @Test
    @DisplayName("测试插入排序的稳定性")
    void testInsertionSortStability() {
        StabilityTestElement[] arr = new StabilityTestElement[]{
            new StabilityTestElement(5, 0),
            new StabilityTestElement(3, 1),
            new StabilityTestElement(5, 2),
            new StabilityTestElement(1, 3),
            new StabilityTestElement(3, 4)
        };

        String originalOrder = getStabilityOrder(arr);
        insertionSortWithStability(arr);
        String sortedOrder = getStabilityOrder(arr);
        
        assertEquals(originalOrder, sortedOrder, "插入排序的稳定性测试失败");
        
        // 验证排序结果的正确性
        for (int i = 0; i < arr.length - 1; i++) {
            assertTrue(arr[i].value <= arr[i + 1].value, "排序结果不正确");
        }
    }

    @Test
    @DisplayName("测试归并排序的稳定性")
    void testMergeSortStability() {
        StabilityTestElement[] arr = new StabilityTestElement[]{
            new StabilityTestElement(5, 0),
            new StabilityTestElement(3, 1),
            new StabilityTestElement(5, 2),
            new StabilityTestElement(1, 3),
            new StabilityTestElement(3, 4)
        };

        String originalOrder = getStabilityOrder(arr);
        mergeSortWithStability(arr);
        String sortedOrder = getStabilityOrder(arr);
        
        assertEquals(originalOrder, sortedOrder, "归并排序的稳定性测试失败");
        
        // 验证排序结果的正确性
        for (int i = 0; i < arr.length - 1; i++) {
            assertTrue(arr[i].value <= arr[i + 1].value, "排序结果不正确");
        }
    }

    private String getStabilityOrder(StabilityTestElement[] arr) {
        StringBuilder order = new StringBuilder();
        // 先按值分组记录索引
        Map<Integer, List<Integer>> valueToIndices = new TreeMap<>();
        
        // 收集所有相同值的索引
        for (int i = 0; i < arr.length; i++) {
            valueToIndices.computeIfAbsent(arr[i].value, k -> new ArrayList<>())
                         .add(arr[i].originalIndex);
        }
        
        // 对每组相同的值，记录其原始索引对
        for (List<Integer> indices : valueToIndices.values()) {
            if (indices.size() > 1) {
                for (int i = 0; i < indices.size() - 1; i++) {
                    order.append(indices.get(i))
                         .append("-")
                         .append(indices.get(i + 1))
                         .append(";");
                }
            }
        }
        
        return order.toString();
    }

    // 为稳定性测试实现的排序方法
    private void bubbleSortWithStability(StabilityTestElement[] arr) {
        int n = arr.length;
        for (int i = 0; i < n - 1; i++) {
            for (int j = 0; j < n - i - 1; j++) {
                if (arr[j].compareTo(arr[j + 1]) > 0) {
                    StabilityTestElement temp = arr[j];
                    arr[j] = arr[j + 1];
                    arr[j + 1] = temp;
                }
            }
        }
    }

    private void insertionSortWithStability(StabilityTestElement[] arr) {
        int n = arr.length;
        for (int i = 1; i < n; i++) {
            StabilityTestElement key = arr[i];
            int j = i - 1;
            while (j >= 0 && arr[j].compareTo(key) > 0) {
                arr[j + 1] = arr[j];
                j--;
            }
            arr[j + 1] = key;
        }
    }

    private void mergeSortWithStability(StabilityTestElement[] arr) {
        if (arr.length <= 1) return;
        
        int mid = arr.length / 2;
        StabilityTestElement[] left = new StabilityTestElement[mid];
        StabilityTestElement[] right = new StabilityTestElement[arr.length - mid];
        
        System.arraycopy(arr, 0, left, 0, mid);
        System.arraycopy(arr, mid, right, 0, arr.length - mid);
        
        mergeSortWithStability(left);
        mergeSortWithStability(right);
        
        merge(arr, left, right);
    }

    private void merge(StabilityTestElement[] arr, StabilityTestElement[] left, StabilityTestElement[] right) {
        int i = 0, j = 0, k = 0;
        
        while (i < left.length && j < right.length) {
            if (left[i].compareTo(right[j]) <= 0) {
                arr[k++] = left[i++];
            } else {
                arr[k++] = right[j++];
            }
        }
        
        while (i < left.length) {
            arr[k++] = left[i++];
        }
        
        while (j < right.length) {
            arr[k++] = right[j++];
        }
    }

    @Test
    @DisplayName("测试排序算法的稳定性")
    void testSortingStability() {
        // 这个测试用例已被新的专门的稳定性测试替代
        assertTrue(true);
    }

    @Test
    @DisplayName("比较所有排序算法的性能")
    void compareAllSortingAlgorithms() {
        // 创建一个较大的随机数组
        int size = 10000;
        int[] largeArray = new Random().ints(size, 0, 1000000).toArray();
        int[] expected = largeArray.clone();
        Arrays.sort(expected);

        // 测试各种排序算法的性能
        long startTime, endTime;

        // 快速排序
        startTime = System.nanoTime();
        int[] quickSorted = SortingAlgorithms.quickSort(largeArray.clone());
        endTime = System.nanoTime();
        assertArrayEquals(expected, quickSorted);
        System.out.printf("快速排序耗时: %.2f ms%n", (endTime - startTime) / 1_000_000.0);

        // 希尔排序
        startTime = System.nanoTime();
        int[] shellSorted = SortingAlgorithms.shellSort(largeArray.clone());
        endTime = System.nanoTime();
        assertArrayEquals(expected, shellSorted);
        System.out.printf("希尔排序耗时: %.2f ms%n", (endTime - startTime) / 1_000_000.0);

        // 计数排序
        startTime = System.nanoTime();
        int[] countingSorted = SortingAlgorithms.countingSort(largeArray.clone());
        endTime = System.nanoTime();
        assertArrayEquals(expected, countingSorted);
        System.out.printf("计数排序耗时: %.2f ms%n", (endTime - startTime) / 1_000_000.0);

        // 基数排序
        startTime = System.nanoTime();
        int[] radixSorted = SortingAlgorithms.radixSort(largeArray.clone());
        endTime = System.nanoTime();
        assertArrayEquals(expected, radixSorted);
        System.out.printf("基数排序耗时: %.2f ms%n", (endTime - startTime) / 1_000_000.0);

        // 桶排序
        startTime = System.nanoTime();
        int[] bucketSorted = SortingAlgorithms.bucketSort(largeArray.clone(), 1000);
        endTime = System.nanoTime();
        assertArrayEquals(expected, bucketSorted);
        System.out.printf("桶排序耗时: %.2f ms%n", (endTime - startTime) / 1_000_000.0);
    }
} 