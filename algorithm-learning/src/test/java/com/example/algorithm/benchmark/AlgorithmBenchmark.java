package com.example.algorithm.benchmark;

import com.example.algorithm.sorting.SortingAlgorithms;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@Slf4j
@SpringBootTest
public class AlgorithmBenchmark {

    private static final int[] SIZES = {100, 1000, 10000, 100000};
    private final Random random = new Random();

    @Test
    void benchmarkSortingAlgorithms() {
        for (int size : SIZES) {
            log.info("测试数组大小: {}", size);

            // 生成随机数组
            int[] arr = generateRandomArray(size);

            // 测试多次并计算平均耗时
            long bubbleTime = benchmarkSortingAlgorithm(arr.clone(), SortingAlgorithms::bubbleSort);
            long quickTime = benchmarkSortingAlgorithm(arr.clone(), SortingAlgorithms::quickSort);

            log.info("冒泡排序平均耗时: {}ms, 快速排序平均耗时: {}ms", bubbleTime, quickTime);

            // 验证排序结果
            Arrays.sort(arr); // 用Java自带排序作为基准
            log.info("排序结果验证 - 冒泡排序: {}, 快速排序: {}",
                    Arrays.equals(arr, SortingAlgorithms.bubbleSort(arr.clone())),
                    Arrays.equals(arr, SortingAlgorithms.quickSort(arr.clone())));

            log.info("----------------------------------------");
        }
    }

    /**
     * 基准测试排序算法的方法，执行多次并计算平均耗时
     */
    private long benchmarkSortingAlgorithm(int[] arr, SortingAlgorithm sortingAlgorithm) {
        final int iterations = 5;
        long totalTime = 0;

        for (int i = 0; i < iterations; i++) {
            long startTime = System.nanoTime();
            sortingAlgorithm.sort(arr.clone()); // 排序每次用副本，避免排序污染
            long endTime = System.nanoTime();
            totalTime += (endTime - startTime);
        }

        // 计算平均耗时并转换为毫秒
        return TimeUnit.NANOSECONDS.toMillis(totalTime / iterations);
    }

    /**
     * 生成随机数组
     */
    private int[] generateRandomArray(int size) {
        int[] arr = new int[size];
        for (int i = 0; i < size; i++) {
            arr[i] = random.nextInt(1000000); // 生成0-999999之间的随机数
        }
        return arr;
    }

    /**
     * 排序算法的接口
     */
    @FunctionalInterface
    interface SortingAlgorithm {
        void sort(int[] array);
    }
}
