# Algorithm Learning Module

这是一个用于学习和实践各种算法的模块。包含了常见算法的实现、测试用例和性能分析。

## 算法分类

### 1. 基础算法
- 排序算法
  - 冒泡排序
  - 选择排序
  - 插入排序
  - 快速排序
  - 归并排序
  - 堆排序
- 查找算法
  - 二分查找
  - 顺序查找
  - 哈希查找

### 2. 数据结构
- 链表
- 栈
- 队列
- 树
  - 二叉树
  - 平衡二叉树
  - 红黑树
- 图
- 堆

### 3. 高级算法
- 动态规划
- 贪心算法
- 回溯算法
- 分治算法
- 字符串匹配

## 如何使用

1. 克隆项目
2. 运行测试用例：`mvn test`
3. 查看性能分析：运行 `AlgorithmBenchmark` 类

## 性能测试

每个算法都包含性能测试用例，可以通过以下方式运行：
mvn test -Dtest=AlgorithmBenchmark