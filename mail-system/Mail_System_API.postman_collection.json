{"info": {"_postman_id": "b5e7b8d9-3a1c-4e8f-9f1d-8b2c3d4e5f6a", "name": "Mail System API", "description": "基于Spring Boot的邮件系统API接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "发送简单文本邮件", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "to", "value": "<EMAIL>", "description": "收件人邮箱", "type": "text"}, {"key": "subject", "value": "测试邮件", "description": "邮件主题", "type": "text"}, {"key": "content", "value": "这是一封测试邮件的内容", "description": "邮件内容", "type": "text"}]}, "url": {"raw": "http://localhost:8080/api/email/simple", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "email", "simple"]}, "description": "发送简单的文本邮件"}}, {"name": "发送HTML邮件", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "to", "value": "<EMAIL>", "description": "收件人邮箱", "type": "text"}, {"key": "subject", "value": "HTML测试邮件", "description": "邮件主题", "type": "text"}, {"key": "content", "value": "<h1>这是一封HTML邮件</h1><p>这是邮件内容</p>", "description": "HTML格式的邮件内容", "type": "text"}]}, "url": {"raw": "http://localhost:8080/api/email/html", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "email", "html"]}, "description": "发送HTML格式的邮件"}}, {"name": "发送带附件的邮件", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "to", "value": "<EMAIL>", "description": "收件人邮箱", "type": "text"}, {"key": "subject", "value": "带附件的测试邮件", "description": "邮件主题", "type": "text"}, {"key": "content", "value": "这是一封带附件的测试邮件", "description": "邮件内容", "type": "text"}, {"key": "attachments", "type": "file", "description": "附件文件", "src": []}]}, "url": {"raw": "http://localhost:8080/api/email/attachment", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "email", "attachment"]}, "description": "发送带附件的邮件"}}, {"name": "发送带内嵌图片的邮件", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "to", "value": "<EMAIL>", "description": "收件人邮箱", "type": "text"}, {"key": "subject", "value": "带图片的测试邮件", "description": "邮件主题", "type": "text"}, {"key": "content", "value": "<h1>这是一封带图片的邮件</h1>", "description": "HTML格式的邮件内容", "type": "text"}, {"key": "images", "type": "file", "description": "内嵌图片文件", "src": []}]}, "url": {"raw": "http://localhost:8080/api/email/inline-image", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "email", "inline-image"]}, "description": "发送带内嵌图片的邮件"}}]}