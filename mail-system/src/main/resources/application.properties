# Server Configuration
server.port=8080

# Mail Configuration
spring.mail.host=smtp.163.com
spring.mail.port=465
spring.mail.username=${MAIL_USERNAME:<EMAIL>}
spring.mail.password=${MAIL_PASSWORD:your-authorization-code}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.ssl.enable=true
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory

# Application Name
spring.application.name=mail-system 