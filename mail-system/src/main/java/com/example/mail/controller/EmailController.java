package com.example.mail.controller;

import com.example.mail.service.EmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/email")
@RequiredArgsConstructor
public class EmailController {

    private final EmailService emailService;

    @PostMapping("/simple")
    public ResponseEntity<String> sendSimpleEmail(
            @RequestParam String to,
            @RequestParam String subject,
            @RequestParam String content) {
        try {
            emailService.sendSimpleEmail(to, subject, content);
            return ResponseEntity.ok("Simple email sent successfully");
        } catch (Exception e) {
            log.error("Failed to send simple email", e);
            return ResponseEntity.internalServerError().body("Failed to send simple email: " + e.getMessage());
        }
    }

    @PostMapping("/html")
    public ResponseEntity<String> sendHtmlEmail(
            @RequestParam String to,
            @RequestParam String subject,
            @RequestParam String content) {
        try {
            emailService.sendHtmlEmail(to, subject, content);
            return ResponseEntity.ok("HTML email sent successfully");
        } catch (Exception e) {
            log.error("Failed to send HTML email", e);
            return ResponseEntity.internalServerError().body("Failed to send HTML email: " + e.getMessage());
        }
    }

    @PostMapping("/attachment")
    public ResponseEntity<String> sendAttachmentEmail(
            @RequestParam String to,
            @RequestParam String subject,
            @RequestParam String content,
            @RequestParam(required = false) List<MultipartFile> attachments) {
        try {
            emailService.sendAttachmentEmail(to, subject, content, attachments);
            return ResponseEntity.ok("Email with attachments sent successfully");
        } catch (Exception e) {
            log.error("Failed to send email with attachments", e);
            return ResponseEntity.internalServerError().body("Failed to send email with attachments: " + e.getMessage());
        }
    }

    @PostMapping("/inline-image")
    public ResponseEntity<String> sendInlineImageEmail(
            @RequestParam String to,
            @RequestParam String subject,
            @RequestParam String content,
            @RequestParam(required = false) List<MultipartFile> images) {
        try {
            emailService.sendInlineImageEmail(to, subject, content, images);
            return ResponseEntity.ok("Email with inline images sent successfully");
        } catch (Exception e) {
            log.error("Failed to send email with inline images", e);
            return ResponseEntity.internalServerError().body("Failed to send email with inline images: " + e.getMessage());
        }
    }
} 