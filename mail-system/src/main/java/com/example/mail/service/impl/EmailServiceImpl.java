package com.example.mail.service.impl;

import com.example.mail.service.EmailService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {

    private final JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String from;

    @Override
    public void sendSimpleEmail(String to, String subject, String content) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(from);
        message.setTo(to);
        message.setSubject(subject);
        message.setText(content);
        mailSender.send(message);
        log.info("Simple email sent successfully to: {}", to);
    }

    @Override
    public void sendHtmlEmail(String to, String subject, String content) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            mailSender.send(message);
            log.info("HTML email sent successfully to: {}", to);
        } catch (MessagingException e) {
            log.error("Failed to send HTML email", e);
            throw new RuntimeException("Failed to send HTML email", e);
        }
    }

    @Override
    public void sendAttachmentEmail(String to, String subject, String content, List<MultipartFile> attachments) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content);

            // 添加附件
            if (attachments != null && !attachments.isEmpty()) {
                for (MultipartFile file : attachments) {
                    helper.addAttachment(
                            file.getOriginalFilename(),
                            new ByteArrayResource(file.getBytes())
                    );
                }
            }

            mailSender.send(message);
            log.info("Email with attachments sent successfully to: {}", to);
        } catch (MessagingException | IOException e) {
            log.error("Failed to send email with attachments", e);
            throw new RuntimeException("Failed to send email with attachments", e);
        }
    }

    @Override
    public void sendInlineImageEmail(String to, String subject, String content, List<MultipartFile> images) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);

            // 处理内嵌图片
            if (images != null && !images.isEmpty()) {
                StringBuilder htmlContent = new StringBuilder(content);
                for (MultipartFile image : images) {
                    String contentId = UUID.randomUUID().toString();
                    helper.addInline(contentId, new ByteArrayResource(image.getBytes()), image.getContentType());
                    htmlContent.append(String.format("<img src='cid:%s' />", contentId));
                }
                helper.setText(htmlContent.toString(), true);
            } else {
                helper.setText(content, true);
            }

            mailSender.send(message);
            log.info("Email with inline images sent successfully to: {}", to);
        } catch (MessagingException | IOException e) {
            log.error("Failed to send email with inline images", e);
            throw new RuntimeException("Failed to send email with inline images", e);
        }
    }
} 