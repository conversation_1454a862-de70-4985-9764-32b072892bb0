package com.example.mail.service;

import org.springframework.web.multipart.MultipartFile;
import java.util.List;

public interface EmailService {
    /**
     * 发送简单文本邮件
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     */
    void sendSimpleEmail(String to, String subject, String content);

    /**
     * 发送HTML格式邮件
     * @param to 收件人
     * @param subject 主题
     * @param content HTML内容
     */
    void sendHtmlEmail(String to, String subject, String content);

    /**
     * 发送带附件的邮件
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param attachments 附件列表
     */
    void sendAttachmentEmail(String to, String subject, String content, List<MultipartFile> attachments);

    /**
     * 发送带内嵌图片的邮件
     * @param to 收件人
     * @param subject 主题
     * @param content HTML内容
     * @param imageMap 图片Map，key为cid，value为图片文件
     */
    void sendInlineImageEmail(String to, String subject, String content, List<MultipartFile> images);
} 