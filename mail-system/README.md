# Mail System

基于Spring Boot的邮件系统，支持使用163邮箱发送各种类型的邮件。

## 功能特性

1. 发送简单文本邮件
2. 发送HTML格式邮件
3. 发送带附件的邮件
4. 发送带内嵌图片的邮件

## 技术栈

- Java 17
- Spring Boot 3.1.0
- Spring Boot Mail
- Lombok

## 快速开始

### 1. 配置邮箱

在 `application.properties` 文件中配置你的163邮箱信息：

```properties
spring.mail.username=<EMAIL>
spring.mail.password=your-authorization-code
```

注意：password不是邮箱密码，而是163邮箱的授权码。获取方式：
1. 登录163邮箱
2. 点击"设置" -> "POP3/SMTP/IMAP"
3. 开启"POP3/SMTP服务"
4. 获取授权码

### 2. 启动应用

运行 `MailSystemApplication` 类启动应用。

### 3. API使用说明

#### 发送简单文本邮件
```http
POST /api/email/simple
Content-Type: application/x-www-form-urlencoded

to=<EMAIL>
subject=邮件主题
content=邮件内容
```

#### 发送HTML邮件
```http
POST /api/email/html
Content-Type: application/x-www-form-urlencoded

to=<EMAIL>
subject=邮件主题
content=<h1>HTML邮件内容</h1>
```

#### 发送带附件的邮件
```http
POST /api/email/attachment
Content-Type: multipart/form-data

to=<EMAIL>
subject=邮件主题
content=邮件内容
attachments=@file1.txt
attachments=@file2.pdf
```

#### 发送带内嵌图片的邮件
```http
POST /api/email/inline-image
Content-Type: multipart/form-data

to=<EMAIL>
subject=邮件主题
content=<h1>带图片的邮件</h1>
images=@image1.jpg
images=@image2.png
```

## 注意事项

1. 确保163邮箱已开启SMTP服务
2. 使用正确的授权码而不是邮箱密码
3. 附件大小不要超过邮箱限制
4. 批量发送邮件时注意频率限制

## 开发计划

- [ ] 添加邮件模板功能
- [ ] 支持批量发送邮件
- [ ] 添加邮件发送队列
- [ ] 支持更多邮箱服务商
- [ ] 添加邮件发送记录和统计 