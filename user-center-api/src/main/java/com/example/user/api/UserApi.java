package com.example.user.api;

import com.example.common.model.Result;
import com.example.user.dto.UserDTO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
public interface UserApi {
    @PostMapping("/api/users/register")
    Result<UserDTO> register(@Validated @RequestBody UserDTO userDTO);
    
    @PostMapping("/api/users/login")
    Result<String> login(@RequestParam("username") String username, @RequestParam("password") String password);
    
    @GetMapping("/api/users/validate")
    Result<UserDTO> validateToken(@RequestHeader("Authorization") String token);
    
    @PutMapping("/api/users/{id}")
    Result<UserDTO> update(@PathVariable("id") Long id, @Validated @RequestBody UserDTO userDTO);
    
    @DeleteMapping("/api/users/{id}")
    Result<Void> delete(@PathVariable("id") Long id);
    
    @GetMapping("/api/users/{id}")
    Result<UserDTO> findById(@PathVariable("id") Long id);
} 