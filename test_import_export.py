# -*- coding: utf-8 -*-
"""
测试导入导出功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.config_manager import ConfigManager
from logger import Kc<PERSON>un<PERSON>ogger

def test_import_export():
    """测试导入导出功能"""
    print("🧪 开始测试导入导出功能...")
    
    # 初始化组件
    logger = KcptunLogger()
    config_manager = ConfigManager("test_config_output.json", logger)
    
    print("\n1️⃣ 测试导出功能...")
    
    # 创建一个测试配置
    test_profile = {
        "kcp_ip": "test.example.com",
        "kcp_port": "8080",
        "kcp_password": "testpassword",
        "local_port": "1080",
        "advanced": {
            "mtu": "1350",
            "mode": "fast3",
            "crypt": "aes-256"
        }
    }
    
    # 添加测试配置
    config_manager.add_profile("导出测试", test_profile)
    
    # 导出配置
    export_path = "exported_config.json"
    if config_manager.export_config(export_path, "导出测试"):
        print(f"✅ 导出成功: {export_path}")
        
        # 检查导出文件
        if os.path.exists(export_path):
            with open(export_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📄 导出文件内容预览:\n{content[:200]}...")
        else:
            print("❌ 导出文件不存在")
    else:
        print("❌ 导出失败")
    
    print("\n2️⃣ 测试导入功能...")
    
    # 测试导入现有的测试配置文件
    test_import_path = "test_config.json"
    if os.path.exists(test_import_path):
        if config_manager.import_config(test_import_path):
            print("✅ 导入成功")
            
            # 检查导入的配置
            profiles = config_manager.get_profile_names()
            print(f"📋 当前配置列表: {profiles}")
            
            # 检查导入的配置内容
            if "测试配置" in profiles:
                imported_profile = config_manager.get_profile("测试配置")
                print(f"📝 导入的配置内容:")
                print(f"   服务器IP: {imported_profile.get('kcp_ip')}")
                print(f"   服务器端口: {imported_profile.get('kcp_port')}")
                print(f"   传输模式: {imported_profile.get('advanced', {}).get('mode')}")
            else:
                print("⚠️ 未找到导入的配置")
        else:
            print("❌ 导入失败")
    else:
        print(f"❌ 测试配置文件不存在: {test_import_path}")
    
    print("\n3️⃣ 测试批量导出...")
    
    # 导出所有配置
    all_export_path = "all_configs.json"
    if config_manager.export_config(all_export_path):
        print(f"✅ 批量导出成功: {all_export_path}")
        
        # 检查文件大小
        if os.path.exists(all_export_path):
            file_size = os.path.getsize(all_export_path)
            print(f"📊 导出文件大小: {file_size} 字节")
    else:
        print("❌ 批量导出失败")
    
    print("\n🎉 导入导出功能测试完成！")

if __name__ == "__main__":
    test_import_export()
