import win32print
import win32api
import os

# 正确的打印机名称
PRINTER_NAME = "EPSON326C48 (L3250 Series)"
PDF_PATH = "D:\\简历2018.doc"  # 先用 PDF 测试更稳定

def print_pdf():
    try:
        if not os.path.exists(PDF_PATH):
            print(f"文件不存在: {PDF_PATH}")
            return
        win32print.SetDefaultPrinter(PRINTER_NAME)
        win32api.ShellExecute(0, "print", PDF_PATH, None, ".", 0)
        print("已发送打印任务。")
    except Exception as e:
        print(f"打印失败: {e}")

print_pdf()
