import os
import sys
import shutil
import subprocess
import time
import psutil  # 需要先安装：pip install psutil
import PyInstaller.__main__

def is_process_running(process_name):
    """检查进程是否在运行"""
    for proc in psutil.process_iter(['name']):
        try:
            if process_name.lower() in proc.info['name'].lower():
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return False

def wait_for_file_release(file_path, timeout=10):
    """等待文件释放"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # 尝试打开文件
            with open(file_path, 'a+b') as f:
                return True
        except PermissionError:
            print(f"等待文件释放: {file_path}")
            time.sleep(1)
    return False

def build():
    # 检查并关闭正在运行的计算器程序
    if is_process_running('calculator.exe'):
        print("请先关闭正在运行的计算器程序")
        return

    # 清理之前的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    
    # 设置图标路径
    icon_path = os.path.join('src', 'icon.ico')
    
    # PyInstaller 参数
    args = [
        'src/calculator_qt.py',  # 主程序文件
        '--name=calculator',     # 输出文件名
        '--onefile',            # 打包成单个文件
        '--noconsole',          # 不显示控制台
        f'--icon={icon_path}',  # 设置图标
        '--clean',              # 清理临时文件
        '--windowed',           # Windows下使用GUI模式
        '--noconfirm',          # 覆盖输出目录
        '--add-data', f'src/icon.ico{os.pathsep}.',  # 添加图标文件
        # 优化选项
        '--optimize=2',         # 优化级别
        # 只包含必要的 PyQt6 模块
        '--hidden-import=PyQt6.QtCore',
        '--hidden-import=PyQt6.QtGui',
        '--hidden-import=PyQt6.QtWidgets',
        # 排除所有不需要的模块
        '--exclude-module=matplotlib',
        '--exclude-module=numpy',
        '--exclude-module=PIL',
        '--exclude-module=pandas',
        '--exclude-module=scipy',
        '--exclude-module=PyQt6.QtNetwork',
        '--exclude-module=PyQt6.QtQml',
        '--exclude-module=PyQt6.QtQuick',
        '--exclude-module=PyQt6.QtSql',
        '--exclude-module=PyQt6.QtTest',
        '--exclude-module=PyQt6.QtXml',
        '--exclude-module=PyQt6.QtMultimedia',
        '--exclude-module=PyQt6.QtOpenGL',
        '--exclude-module=PyQt6.QtPrintSupport',
        '--exclude-module=PyQt6.QtSvg',
        '--exclude-module=PyQt6.QtWebEngine',
        '--exclude-module=PyQt6.QtWebEngineCore',
        '--exclude-module=PyQt6.QtWebEngineWidgets',
        # 压缩选项
        '--noupx',              # 不使用UPX压缩
    ]
    
    # 执行打包
    PyInstaller.__main__.run(args)
    print("程序文件打包完成！")
    
    # 等待文件释放
    exe_path = os.path.join('dist', 'calculator.exe')  # 修改回单文件路径
    if not wait_for_file_release(exe_path):
        print("无法访问生成的exe文件，请确保没有程序正在使用它")
        return
    
    # 创建安装程序
    try:
        # 检查是否安装了 Inno Setup
        iscc_path = r'"C:\Program Files (x86)\Inno Setup 6\ISCC.exe"'
        subprocess.run(f'{iscc_path} installer.iss', check=True)
        print("安装程序创建成功！输出文件在 Output 目录下")
    except subprocess.CalledProcessError:
        print("创建安装程序失败！请确保已安装 Inno Setup")
    except FileNotFoundError:
        print("未找到 Inno Setup，请先安装 Inno Setup")

if __name__ == '__main__':
    build()