package com.example.common.logging.config;

import com.example.common.logging.handler.LogHandler;
import com.example.common.logging.interceptor.LoggingInterceptor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 日志配置类
 */
@Configuration
public class LoggingConfiguration implements WebMvcConfigurer {

    private final LogHandler logHandler;
    private final ObjectMapper objectMapper;

    public LoggingConfiguration(Log<PERSON>and<PERSON> logHandler, ObjectMapper objectMapper) {
        this.logHandler = logHandler;
        this.objectMapper = objectMapper;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new LoggingInterceptor(logHand<PERSON>, objectMapper))
                .addPathPatterns("/**")
                .order(1);
    }
} 