package com.example.common.logging.context;

import org.slf4j.MDC;
import java.util.UUID;

/**
 * 日志上下文
 * 用于管理MDC（Mapped Diagnostic Context）
 */
public class LogContext {
    public static final String REQUEST_ID = "requestId";
    public static final String USER_ID = "userId";
    public static final String SERVICE_NAME = "serviceName";
    public static final String ENVIRONMENT = "environment";
    public static final String TRACE_ID = "traceId";
    public static final String SPAN_ID = "spanId";

    public static void setRequestId(String requestId) {
        MDC.put(REQUEST_ID, requestId);
    }

    public static void setUserId(String userId) {
        MDC.put(USER_ID, userId);
    }

    public static void setServiceName(String serviceName) {
        MDC.put(SERVICE_NAME, serviceName);
    }

    public static void setEnvironment(String environment) {
        MDC.put(ENVIRONMENT, environment);
    }

    public static void setTraceId(String traceId) {
        MDC.put(TRACE_ID, traceId);
    }

    public static void setSpanId(String spanId) {
        MDC.put(SPAN_ID, spanId);
    }

    public static String getRequestId() {
        return MDC.get(REQUEST_ID);
    }

    public static String getUserId() {
        return MDC.get(USER_ID);
    }

    public static String getServiceName() {
        return MDC.get(SERVICE_NAME);
    }

    public static String getEnvironment() {
        return MDC.get(ENVIRONMENT);
    }

    public static String getTraceId() {
        return MDC.get(TRACE_ID);
    }

    public static String getSpanId() {
        return MDC.get(SPAN_ID);
    }

    public static void clear() {
        MDC.clear();
    }

    public static void initContext() {
        if (getRequestId() == null) {
            setRequestId(UUID.randomUUID().toString());
        }
        if (getTraceId() == null) {
            setTraceId(UUID.randomUUID().toString());
        }
        if (getSpanId() == null) {
            setSpanId(UUID.randomUUID().toString());
        }
    }
} 