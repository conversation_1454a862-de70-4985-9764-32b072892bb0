package com.example.common.logging.p6spy;

import com.example.common.logging.model.LogLevel;
import com.example.common.logging.model.LogMessage;
import com.example.common.logging.handler.LogHandler;
import com.p6spy.engine.spy.appender.MessageFormattingStrategy;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.slf4j.MDC;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * P6Spy SQL日志记录器
 */
@Component
public class P6SpyLogger implements MessageFormattingStrategy, ApplicationContextAware {
    
    private static ApplicationContext applicationContext;
    private static LogHandler logHandler;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        P6SpyLogger.applicationContext = applicationContext;
    }

    private static LogHandler getLogHandler() {
        if (logHandler == null && applicationContext != null) {
            logHandler = applicationContext.getBean(LogHandler.class);
        }
        return logHandler;
    }

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category, String prepared, String sql, String url) {
        if (!StringUtils.hasText(sql)) {
            return "";
        }

        String traceId = MDC.get("traceId");
        if (traceId == null) {
            traceId = UUID.randomUUID().toString().replace("-", "").substring(0, 6);
            MDC.put("traceId", traceId);
        }

        LogMessage logMessage = LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message("SQL执行")
                .timestamp(LocalDateTime.now())
                .build();

        logMessage.getTags().put("sql", sql);
        logMessage.getTags().put("prepared", prepared);
        logMessage.getTags().put("category", category);
        logMessage.getTags().put("elapsed", String.valueOf(elapsed));
        logMessage.getTags().put("url", url);
        logMessage.getTags().put("traceId", traceId);

        LogHandler handler = getLogHandler();
        if (handler != null) {
            handler.handle(logMessage);
        }

        return String.format("[TraceId: %s] SQL执行 - 耗时: %dms | SQL: %s", traceId, elapsed, sql);
    }
} 