package com.example.common.logging.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 日志消息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogMessage {
    /**
     * 日志ID
     */
    private String id;

    /**
     * 日志级别
     */
    private LogLevel level;

    /**
     * 日志内容
     */
    private String message;

    /**
     * 日志时间
     */
    private LocalDateTime timestamp;

    /**
     * 线程名称
     */
    private String threadName;

    /**
     * 类名
     */
    private String className;

    /**
     * 方法名
     */
    private String methodName;

    /**
     * 行号
     */
    private int lineNumber;

    /**
     * 异常信息
     */
    private Throwable throwable;

    /**
     * MDC上下文信息
     */
    private Map<String, String> mdcContext;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 请求URI
     */
    private String requestUri;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 响应状态码
     */
    private int responseStatus;

    /**
     * 响应内容
     */
    private String responseBody;

    /**
     * 执行时间（毫秒）
     */
    private long executionTime;

    /**
     * 方法返回值
     */
    private String result;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 环境标识
     */
    private String environment;

    /**
     * 标签
     */
    @Builder.Default
    private Map<String, String> tags = new HashMap<>();
} 