package com.example.common.logging.interceptor;

import com.example.common.logging.handler.LogHandler;
import com.example.common.logging.model.LogLevel;
import com.example.common.logging.model.LogMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class LoggingInterceptor implements HandlerInterceptor {

    private final LogHandler logHandler;
    private final ObjectMapper objectMapper;
    private static final String START_TIME = "request-start-time";
    private static final String TRACE_ID = "traceId";
    private final ThreadLocal<String> traceIdThreadLocal = new ThreadLocal<>();

    // ANSI颜色代码
    private static final String ANSI_RESET = "\u001B[0m";
    private static final String ANSI_BLACK = "\u001B[30m";
    private static final String ANSI_RED = "\u001B[31m";
    private static final String ANSI_GREEN = "\u001B[32m";
    private static final String ANSI_YELLOW = "\u001B[33m";
    private static final String ANSI_BLUE = "\u001B[34m";
    private static final String ANSI_MAGENTA = "\u001B[35m";
    private static final String ANSI_CYAN = "\u001B[36m";
    private static final String ANSI_WHITE = "\u001B[37m";

    public LoggingInterceptor(LogHandler logHandler, ObjectMapper objectMapper) {
        this.logHandler = logHandler;
        this.objectMapper = objectMapper;
    }

    private String colorize(String text, String color) {
        if (text == null) {
            return "";
        }
        String ansiColor = switch (color.toLowerCase()) {
            case "black" -> ANSI_BLACK;
            case "red" -> ANSI_RED;
            case "green" -> ANSI_GREEN;
            case "yellow" -> ANSI_YELLOW;
            case "blue" -> ANSI_BLUE;
            case "magenta" -> ANSI_MAGENTA;
            case "cyan" -> ANSI_CYAN;
            case "white" -> ANSI_WHITE;
            default -> "";
        };
        return ansiColor + text + ANSI_RESET;
    }

    private String formatTraceId(String traceId) {
        traceId = traceId != null && !traceId.trim().isEmpty() ? traceId : "N/A";
        return "[TraceId: " + colorize(traceId, "blue") + "]";
    }

    private String formatClassName(String className, String methodName) {
        String fullName = className + "." + methodName;
        return "[" + colorize(fullName, "cyan") + "]";
    }

    private String getHandlerClassName(Object handler) {
        if (handler instanceof HandlerMethod handlerMethod) {
            return handlerMethod.getBeanType().getName();
        } else if (handler != null) {
            return handler.getClass().getName();
        }
        return "UnknownHandler";
    }

    private String getHandlerMethodName(Object handler, String defaultMethodName) {
        if (handler instanceof HandlerMethod handlerMethod) {
            return handlerMethod.getMethod().getName();
        }
        return defaultMethodName;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String traceId = UUID.randomUUID().toString().substring(0, 6);
        
        // 设置traceId到ThreadLocal、MDC和request中
        traceIdThreadLocal.set(traceId);
        MDC.put(TRACE_ID, traceId);
        request.setAttribute(TRACE_ID, traceId);
        request.setAttribute(START_TIME, System.currentTimeMillis());
        
        String className = getHandlerClassName(handler);
        String methodName = getHandlerMethodName(handler, "preHandle");

        StringBuilder message = new StringBuilder();
        message.append(formatTraceId(traceId))
               .append(" ")
               .append(formatClassName(className, methodName))
               .append(" Start | ")
               .append(request.getMethod())
               .append(" ")
               .append(request.getRequestURI());

        LogMessage logMessage = LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message(message.toString())
                .timestamp(LocalDateTime.now())
                .className(className)
                .methodName(methodName)
                .threadName(Thread.currentThread().getName())
                .build();
        logHandler.info(logMessage);
        
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        // 在这里不做处理，因为响应内容还没有完全写入
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        try {
            String traceId = traceIdThreadLocal.get();
            if (traceId == null) {
                traceId = (String) request.getAttribute(TRACE_ID);
            }
            
            // 确保MDC中的traceId和ThreadLocal中的一致
            if (traceId != null) {
                MDC.put(TRACE_ID, traceId);
            }

            Long startTime = (Long) request.getAttribute(START_TIME);
            long executionTime = System.currentTimeMillis() - startTime;

            // 获取响应内容
            String responseContent = "";
            if (response instanceof ContentCachingResponseWrapper wrapper) {
                try {
                    byte[] buf = wrapper.getContentAsByteArray();
                    if (buf.length > 0) {
                        responseContent = new String(buf, StandardCharsets.UTF_8);
                        // 尝试解析和格式化JSON
                        try {
                            Object json = objectMapper.readValue(responseContent, Object.class);
                            responseContent = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(json);
                        } catch (Exception e) {
                            // 如果不是JSON格式，保持原样
                        }
                    }
                } catch (Exception e) {
                    logHandler.error("Failed to read response content: " + e.getMessage());
                }
            }

            String className = getHandlerClassName(handler);
            String methodName = getHandlerMethodName(handler, "afterCompletion");

            // 构建消息
            StringBuilder message = new StringBuilder();
            message.append(formatTraceId(traceId))
                   .append(" ")
                   .append(formatClassName(className, methodName))
                   .append(" End | Time: ")
                   .append(colorize(String.valueOf(executionTime), "yellow"))
                   .append("ms");

            if (responseContent != null && !responseContent.isEmpty()) {
                message.append(" | Response: ").append(responseContent);
            }

            LogMessage logMessage = LogMessage.builder()
                    .id(UUID.randomUUID().toString())
                    .level(ex != null ? LogLevel.ERROR : LogLevel.INFO)
                    .message(message.toString())
                    .timestamp(LocalDateTime.now())
                    .className(className)
                    .methodName(methodName)
                    .threadName(Thread.currentThread().getName())
                    .build();
            logHandler.info(logMessage);
        } finally {
            // 清理ThreadLocal和MDC
            traceIdThreadLocal.remove();
            MDC.remove(TRACE_ID);
        }
    }
} 