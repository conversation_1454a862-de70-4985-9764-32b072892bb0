package com.example.common.logging.filter;

import com.example.common.logging.handler.LogHandler;
import com.example.common.logging.model.LogLevel;
import com.example.common.logging.model.LogMessage;
import com.example.common.logging.sensitive.SensitiveDataConverter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.UUID;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class LoggingFilter extends OncePerRequestFilter {

    private final LogHandler logHandler;
    private final SensitiveDataConverter sensitiveDataConverter;

    public LoggingFilter(LogHandler logHandler, SensitiveDataConverter sensitiveDataConverter) {
        this.logHandler = logHandler;
        this.sensitiveDataConverter = sensitiveDataConverter;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        if (shouldNotFilter(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 包装请求和响应以便多次读取
        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);

        String traceId = UUID.randomUUID().toString().replace("-", "").substring(0, 6);
        MDC.put("traceId", traceId);

        long startTime = System.currentTimeMillis();
        try {
            // 记录请求开始
            logRequest(requestWrapper, traceId);
            
            // 执行过滤链
            filterChain.doFilter(requestWrapper, responseWrapper);
            
            // 记录响应
            logResponse(responseWrapper, traceId, System.currentTimeMillis() - startTime);
        } finally {
            responseWrapper.copyBodyToResponse();
            MDC.remove("traceId");
        }
    }

    private void logRequest(ContentCachingRequestWrapper request, String traceId) {
        LogMessage logMessage = LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message("Request Start")
                .timestamp(LocalDateTime.now())
                .className(this.getClass().getSimpleName())
                .methodName("doFilter")
                .threadName(Thread.currentThread().getName())
                .tags(new HashMap<>())
                .build();

        // 添加请求信息
        logMessage.getTags().put("traceId", traceId);
        logMessage.getTags().put("method", request.getMethod());
        logMessage.getTags().put("path", request.getRequestURI());
        logMessage.getTags().put("queryString", request.getQueryString());
        
        // 处理请求体
        String requestBody = new String(request.getContentAsByteArray());
        if (!requestBody.isEmpty()) {
            logMessage.getTags().put("requestBody", sensitiveDataConverter.convert(requestBody));
        }

        logHandler.handle(logMessage);
    }

    private void logResponse(ContentCachingResponseWrapper response, String traceId, long executionTime) {
        LogMessage logMessage = LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message("Request End")
                .timestamp(LocalDateTime.now())
                .className(this.getClass().getSimpleName())
                .methodName("doFilter")
                .threadName(Thread.currentThread().getName())
                .tags(new HashMap<>())
                .build();

        // 添加响应信息
        logMessage.getTags().put("traceId", traceId);
        logMessage.getTags().put("status", String.valueOf(response.getStatus()));
        logMessage.getTags().put("executionTime", String.valueOf(executionTime));
        
        // 处理响应体
        String responseBody = new String(response.getContentAsByteArray());
        if (!responseBody.isEmpty()) {
            logMessage.getTags().put("responseBody", sensitiveDataConverter.convert(responseBody));
        }

        logHandler.handle(logMessage);
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getRequestURI();
        return path.contains("/actuator") || 
               path.contains("/swagger") || 
               path.contains("/v3/api-docs") ||
               path.contains("/favicon.ico");
    }
} 