package com.example.common.logging.handler;

import com.example.common.logging.model.LogMessage;

/**
 * 日志处理接口
 */
public interface LogHandler {
    /**
     * 处理日志消息
     *
     * @param logMessage 日志消息
     */
    void handle(LogMessage logMessage);

    /**
     * 处理异常日志
     *
     * @param throwable 异常
     * @param message 消息
     */
    void handleException(Throwable throwable, String message);

    /**
     * 处理业务日志
     *
     * @param message 日志消息
     * @param args 参数
     */
    void info(String message, Object... args);

    /**
     * 处理警告日志
     *
     * @param message 日志消息
     * @param args 参数
     */
    void warn(String message, Object... args);

    /**
     * 处理错误日志
     *
     * @param message 日志消息
     * @param args 参数
     */
    void error(String message, Object... args);

    /**
     * 处理调试日志
     *
     * @param message 日志消息
     * @param args 参数
     */
    void debug(String message, Object... args);

    /**
     * 处理业务日志
     *
     * @param logMessage 日志消息
     */
    void info(LogMessage logMessage);

    /**
     * 处理警告日志
     *
     * @param logMessage 日志消息
     */
    void warn(LogMessage logMessage);

    /**
     * 处理错误日志
     *
     * @param logMessage 日志消息
     */
    void error(LogMessage logMessage);

    /**
     * 处理调试日志
     *
     * @param logMessage 日志消息
     */
    void debug(LogMessage logMessage);
} 