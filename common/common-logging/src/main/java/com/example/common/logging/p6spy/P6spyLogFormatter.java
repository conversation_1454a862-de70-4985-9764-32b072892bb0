package com.example.common.logging.p6spy;

import com.p6spy.engine.spy.appender.MessageFormattingStrategy;
import org.slf4j.MDC;

public class P6spyLogFormatter implements MessageFormattingStrategy {
    
    // ANSI颜色代码
    private static final String ANSI_RESET = "\u001B[0m";
    private static final String ANSI_BLUE = "\u001B[34m";
    private static final String ANSI_YELLOW = "\u001B[33m";
    private static final String ANSI_CYAN = "\u001B[36m";
    private static final String TRACE_ID = "traceId";

    private String colorize(String text, String ansiColor) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        return ansiColor + text + ANSI_RESET;
    }

    private String formatTraceId(String traceId) {
        if (traceId == null || traceId.trim().isEmpty()) {
            traceId = MDC.get(TRACE_ID);
        }
        traceId = traceId != null && !traceId.trim().isEmpty() ? traceId : "N/A";
        return "[TraceId: " + colorize(traceId, ANSI_BLUE) + "]";
    }

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category,
                              String prepared, String sql, String url) {
        String traceId = MDC.get(TRACE_ID);
        
        StringBuilder sb = new StringBuilder();
        sb.append(formatTraceId(traceId))
          .append(" SQL执行 - 耗时: ")
          .append(colorize(String.valueOf(elapsed), ANSI_YELLOW)).append("ms")
          .append(" | SQL: ")
          .append(colorize(sql, ANSI_CYAN));
        
        return sb.toString();
    }
} 