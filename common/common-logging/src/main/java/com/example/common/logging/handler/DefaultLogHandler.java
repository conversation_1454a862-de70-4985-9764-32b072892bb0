package com.example.common.logging.handler;

import com.example.common.logging.model.LogMessage;
import com.example.common.logging.model.LogLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 默认的日志处理器实现
 * 使用 SLF4J 输出日志
 */
public class DefaultLogHandler implements LogHandler {
    private static final Logger log = LoggerFactory.getLogger(DefaultLogHandler.class);

    @Override
    public void handle(LogMessage message) {
        switch (message.getLevel()) {
            case TRACE -> log.trace(buildLogMessage(message), message.getThrowable());
            case DEBUG -> log.debug(buildLogMessage(message), message.getThrowable());
            case INFO -> log.info(buildLogMessage(message), message.getThrowable());
            case WARN -> log.warn(buildLogMessage(message), message.getThrowable());
            case ERROR -> log.error(buildLogMessage(message), message.getThrowable());
        }
    }

    @Override
    public void handleException(Throwable e, String context) {
        LogMessage message = LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.ERROR)
                .message(context)
                .throwable(e)
                .timestamp(LocalDateTime.now())
                .build();
        handle(message);
    }

    @Override
    public void info(String message, Object... args) {
        if (log.isInfoEnabled()) {
            log.info(message, args);
        }
    }

    @Override
    public void warn(String message, Object... args) {
        if (log.isWarnEnabled()) {
            log.warn(message, args);
        }
    }

    @Override
    public void error(String message, Object... args) {
        if (log.isErrorEnabled()) {
            log.error(message, args);
        }
    }

    @Override
    public void debug(String message, Object... args) {
        if (log.isDebugEnabled()) {
            log.debug(message, args);
        }
    }

    @Override
    public void info(LogMessage logMessage) {
        handle(logMessage);
    }

    @Override
    public void warn(LogMessage logMessage) {
        handle(logMessage);
    }

    @Override
    public void error(LogMessage logMessage) {
        handle(logMessage);
    }

    @Override
    public void debug(LogMessage logMessage) {
        handle(logMessage);
    }

    private String buildLogMessage(LogMessage message) {
        StringBuilder sb = new StringBuilder();
        sb.append("[").append(message.getId()).append("] ");
        
        if (message.getRequestId() != null) {
            sb.append("[RequestId: ").append(message.getRequestId()).append("] ");
        }
        
        if (message.getUserId() != null) {
            sb.append("[UserId: ").append(message.getUserId()).append("] ");
        }
        
        if (message.getClassName() != null) {
            sb.append("[").append(message.getClassName());
            if (message.getMethodName() != null) {
                sb.append(".").append(message.getMethodName());
                if (message.getLineNumber() > 0) {
                    sb.append(":").append(message.getLineNumber());
                }
            }
            sb.append("] ");
        }
        
        sb.append(message.getMessage());
        
        if (message.getExecutionTime() > 0) {
            sb.append(" (").append(message.getExecutionTime()).append("ms)");
        }

        if (message.getResult() != null) {
            sb.append(" Result: ").append(message.getResult());
        }
        
        return sb.toString();
    }
} 