package com.example.common.logging.sensitive;

import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

@Component
public class SensitiveDataConverter {
    
    private static final Pattern PHONE_PATTERN = Pattern.compile("\"phone\"\\s*:\\s*\"(\\d{11})\"");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("\"email\"\\s*:\\s*\"([^\"@]+@[^\"]+)\"");
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("\"idCard\"\\s*:\\s*\"(\\d{15}|\\d{18})\"");
    private static final Pattern ADDRESS_PATTERN = Pattern.compile("\"address\"\\s*:\\s*\"([^\"]+)\"");
    private static final Pattern PASSWORD_PATTERN = Pattern.compile("\"password\"\\s*:\\s*\"([^\"]+)\"");
    private static final Pattern REAL_NAME_PATTERN = Pattern.compile("\"realName\"\\s*:\\s*\"([^\"]+)\"");
    private static final Pattern USERNAME_PATTERN = Pattern.compile("\"username\"\\s*:\\s*\"([^\"]+)\"");
    private static final Pattern ADDITIONAL_INFO_PATTERN = Pattern.compile("\"additionalInfo\"\\s*:\\s*\"([^\"]+)\"");

    public String convert(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }

        content = PHONE_PATTERN.matcher(content).replaceAll(mr -> 
            String.format("\"phone\":\"%s****%s\"", 
                mr.group(1).substring(0, 3), 
                mr.group(1).substring(7)));

        content = EMAIL_PATTERN.matcher(content).replaceAll(mr -> {
            String[] parts = mr.group(1).split("@");
            String name = parts[0];
            String maskedName = name.length() <= 3 ? 
                "*".repeat(name.length()) : 
                name.substring(0, 3) + "*".repeat(name.length() - 3);
            return String.format("\"email\":\"%s@%s\"", maskedName, parts[1]);
        });

        content = ID_CARD_PATTERN.matcher(content).replaceAll(mr ->
            String.format("\"idCard\":\"%s****%s\"",
                mr.group(1).substring(0, 6),
                mr.group(1).substring(mr.group(1).length() - 4)));

        content = ADDRESS_PATTERN.matcher(content).replaceAll(mr -> {
            String address = mr.group(1);
            if (address.length() <= 6) {
                return String.format("\"address\":\"***%s\"", address);
            }
            return String.format("\"address\":\"***%s\"", 
                address.substring(address.length() - 6));
        });

        content = PASSWORD_PATTERN.matcher(content).replaceAll("\"password\":\"******\"");

        content = REAL_NAME_PATTERN.matcher(content).replaceAll(mr -> {
            String name = mr.group(1);
            if (name.length() <= 1) return String.format("\"realName\":\"%s\"", "*");
            if (name.length() == 2) return String.format("\"realName\":\"%s*\"", name.substring(0, 1));
            return String.format("\"realName\":\"%s*%s\"", 
                name.substring(0, 1), 
                name.substring(name.length() - 1));
        });

        content = USERNAME_PATTERN.matcher(content).replaceAll(mr -> {
            String username = mr.group(1);
            if (username.length() <= 2) {
                return String.format("\"username\":\"%s\"", "*".repeat(username.length()));
            }
            return String.format("\"username\":\"%s%s%s\"",
                username.substring(0, 1),
                "*".repeat(username.length() - 2),
                username.substring(username.length() - 1));
        });

        // 处理additionalInfo字段中的敏感信息
        content = content.replaceAll("\\\\\"phone\\\\\":\\\\\"\\d{11}\\\\\"", "\\\\\"phone\\\\\":\\\\\"***********\\\\\"")
                       .replaceAll("\\\\\"address\\\\\":\\\\\"[^\\\\\"]+\\\\\"", "\\\\\"address\\\\\":\\\\\"***\\\\\"")
                       .replaceAll("\\\\\"realName\\\\\":\\\\\"[^\\\\\"]+\\\\\"", "\\\\\"realName\\\\\":\\\\\"***\\\\\"");

        return content;
    }
} 