package com.example.common.logging.handler;

import com.example.common.logging.model.LogLevel;
import com.example.common.logging.model.LogMessage;
import com.example.common.logging.sensitive.SensitiveDataConverter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
public class SyncLogHandler extends AbstractLogHandler {

    private final SensitiveDataConverter sensitiveDataConverter;

    public SyncLogHandler(SensitiveDataConverter sensitiveDataConverter) {
        this.sensitiveDataConverter = sensitiveDataConverter;
    }

    @Override
    public void handle(LogMessage logMessage) {
        String formattedMessage = buildLogMessage(logMessage);
        Logger logger = LoggerFactory.getLogger(logMessage.getClassName());

        switch (logMessage.getLevel()) {
            case INFO -> logger.info(formattedMessage);
            case WARN -> logger.warn(formattedMessage);
            case ERROR -> {
                if (logMessage.getThrowable() != null) {
                    logger.error(formattedMessage, logMessage.getThrowable());
                } else {
                    logger.error(formattedMessage);
                }
            }
            case DEBUG -> logger.debug(formattedMessage);
            case TRACE -> logger.trace(formattedMessage);
        }
    }

    @Override
    public void info(String message, Object... args) {
        handle(createLogMessage(LogLevel.INFO, message, args));
    }

    @Override
    public void warn(String message, Object... args) {
        handle(createLogMessage(LogLevel.WARN, message, args));
    }

    @Override
    public void error(String message, Object... args) {
        handle(createLogMessage(LogLevel.ERROR, message, args));
    }

    @Override
    public void debug(String message, Object... args) {
        handle(createLogMessage(LogLevel.DEBUG, message, args));
    }

    @Override
    public void handleException(Throwable throwable, String message) {
        LogMessage logMessage = LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.ERROR)
                .message(message)
                .throwable(throwable)
                .timestamp(LocalDateTime.now())
                .className(throwable.getStackTrace()[0].getClassName())
                .methodName(throwable.getStackTrace()[0].getMethodName())
                .threadName(Thread.currentThread().getName())
                .tags(new HashMap<>())
                .build();
        handle(logMessage);
    }

    private LogMessage createLogMessage(LogLevel level, String message, Object... args) {
        return LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(level)
                .message(String.format(message, args))
                .timestamp(LocalDateTime.now())
                .className(getCallerClassName())
                .methodName(getCallerMethodName())
                .threadName(Thread.currentThread().getName())
                .tags(new HashMap<>())
                .build();
    }

    private String getCallerClassName() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (int i = 1; i < stackTrace.length; i++) {
            if (!stackTrace[i].getClassName().equals(getClass().getName())) {
                return stackTrace[i].getClassName();
            }
        }
        return "Unknown";
    }

    private String getCallerMethodName() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (int i = 1; i < stackTrace.length; i++) {
            if (!stackTrace[i].getClassName().equals(getClass().getName())) {
                return stackTrace[i].getMethodName();
            }
        }
        return "unknown";
    }

    @Override
    protected String buildLogMessage(LogMessage logMessage) {
        StringBuilder message = new StringBuilder();
        
        // 添加基本信息
        message.append(String.format("[TraceId: %s] ", logMessage.getTags().getOrDefault("traceId", "N/A")));
        message.append(String.format("[%s.%s] ", logMessage.getClassName(), logMessage.getMethodName()));

        // 添加主要消息
        if (logMessage.getMessage() != null) {
            if (logMessage.getMessage().contains("Start")) {
                appendStartMessage(message, logMessage);
            } else if (logMessage.getMessage().contains("End")) {
                appendEndMessage(message, logMessage);
            } else if (logMessage.getLevel() == LogLevel.WARN || logMessage.getLevel() == LogLevel.ERROR) {
                appendErrorMessage(message, logMessage);
            } else {
                message.append(logMessage.getMessage());
            }
        }

        return message.toString();
    }

    private void appendStartMessage(StringBuilder message, LogMessage logMessage) {
        message.append("Start");
        Map<String, String> tags = logMessage.getTags();
        
        // 添加请求信息
        if (tags.containsKey("method") && tags.containsKey("path")) {
            message.append(String.format(" | %s %s", tags.get("method"), tags.get("path")));
        }
        
        // 添加参数信息
        if (tags.containsKey("params")) {
            message.append(String.format(" | Params: %s", tags.get("params")));
        }
    }

    private void appendEndMessage(StringBuilder message, LogMessage logMessage) {
        message.append("End");
        Map<String, String> tags = logMessage.getTags();
        
        // 添加执行时间
        if (tags.containsKey("executionTime")) {
            message.append(String.format(" | Time: %sms", tags.get("executionTime")));
        }
        
        // 添加响应结果
        if (tags.containsKey("response")) {
            message.append(String.format(" | Response: %s", tags.get("response")));
        }
    }

    private void appendErrorMessage(StringBuilder message, LogMessage logMessage) {
        Map<String, String> tags = logMessage.getTags();
        
        // 添加异常信息
        if (tags.containsKey("exceptionType")) {
            message.append(String.format("%s: ", tags.get("exceptionType")));
        }
        
        // 添加错误消息
        message.append(logMessage.getMessage());
        
        // 添加参数信息
        if (tags.containsKey("params")) {
            message.append(String.format(" | Params: %s", tags.get("params")));
        }
    }

    @Override
    public void info(LogMessage logMessage) {
        handle(logMessage);
    }

    @Override
    public void warn(LogMessage logMessage) {
        handle(logMessage);
    }

    @Override
    public void error(LogMessage logMessage) {
        handle(logMessage);
    }

    @Override
    public void debug(LogMessage logMessage) {
        handle(logMessage);
    }
} 