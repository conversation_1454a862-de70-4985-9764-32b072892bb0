package com.example.common.logging.handler;

import com.example.common.logging.model.LogLevel;
import com.example.common.logging.model.LogMessage;
import com.example.common.logging.sensitive.SensitiveDataConverter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
public class AsyncLogHandler extends AbstractLogHandler {

    private final ThreadPoolTaskExecutor asyncExecutor;
    private final SensitiveDataConverter sensitiveDataConverter;

    public AsyncLogHandler(ThreadPoolTaskExecutor asyncExecutor, SensitiveDataConverter sensitiveDataConverter) {
        this.asyncExecutor = asyncExecutor;
        this.sensitiveDataConverter = sensitiveDataConverter;
    }

    @Async("asyncLogExecutor")
    @Override
    public void handle(LogMessage logMessage) {
        if (logMessage == null) {
            log.warn("LogMessage is null, skipping logging");
            return;
        }

        // 获取当前线程的MDC上下文
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        
        try {
            // 如果有traceId在tags中，确保设置到MDC中
            if (logMessage.getTags() != null && logMessage.getTags().containsKey("traceId")) {
                MDC.put("traceId", logMessage.getTags().get("traceId"));
            }
            
            String className = logMessage.getClassName();
            if (className == null || className.trim().isEmpty()) {
                className = "UnknownClass";
            }
            
            String formattedMessage = buildLogMessage(logMessage);
            Logger logger = LoggerFactory.getLogger(className);

            switch (logMessage.getLevel()) {
                case INFO -> logger.info(formattedMessage);
                case WARN -> logger.warn(formattedMessage);
                case ERROR -> {
                    if (logMessage.getThrowable() != null) {
                        logger.error(formattedMessage, logMessage.getThrowable());
                    } else {
                        logger.error(formattedMessage);
                    }
                }
                case DEBUG -> logger.debug(formattedMessage);
                case TRACE -> logger.trace(formattedMessage);
            }
        } finally {
            // 清理当前线程的MDC上下文
            MDC.clear();
            // 恢复原始的MDC上下文
            if (contextMap != null) {
                MDC.setContextMap(contextMap);
            }
        }
    }

    @Override
    public void info(LogMessage logMessage) {
        handle(logMessage);
    }

    @Override
    public void warn(LogMessage logMessage) {
        handle(logMessage);
    }

    @Override
    public void error(LogMessage logMessage) {
        handle(logMessage);
    }

    @Override
    public void debug(LogMessage logMessage) {
        handle(logMessage);
    }

    @Override
    public void info(String message, Object... args) {
        handle(createLogMessage(LogLevel.INFO, message, args));
    }

    @Override
    public void warn(String message, Object... args) {
        handle(createLogMessage(LogLevel.WARN, message, args));
    }

    @Override
    public void error(String message, Object... args) {
        handle(createLogMessage(LogLevel.ERROR, message, args));
    }

    @Override
    public void debug(String message, Object... args) {
        handle(createLogMessage(LogLevel.DEBUG, message, args));
    }

    @Override
    public void handleException(Throwable throwable, String message) {
        if (throwable == null) {
            error(message != null ? message : "Unknown error");
            return;
        }

        StackTraceElement[] stackTrace = throwable.getStackTrace();
        String className = stackTrace != null && stackTrace.length > 0 
            ? stackTrace[0].getClassName() 
            : "UnknownClass";
        String methodName = stackTrace != null && stackTrace.length > 0 
            ? stackTrace[0].getMethodName() 
            : "unknownMethod";

        LogMessage logMessage = LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.ERROR)
                .message(message != null ? message : throwable.getMessage())
                .throwable(throwable)
                .timestamp(LocalDateTime.now())
                .className(className)
                .methodName(methodName)
                .threadName(Thread.currentThread().getName())
                .tags(new HashMap<>())
                .build();
        handle(logMessage);
    }

    private LogMessage createLogMessage(LogLevel level, String message, Object... args) {
        String className = getCallerClassName();
        if (className == null || className.trim().isEmpty()) {
            className = "UnknownClass";
        }
        
        String methodName = getCallerMethodName();
        if (methodName == null || methodName.trim().isEmpty()) {
            methodName = "unknownMethod";
        }
        
        return LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(level)
                .message(String.format(message, args))
                .timestamp(LocalDateTime.now())
                .className(className)
                .methodName(methodName)
                .threadName(Thread.currentThread().getName())
                .tags(new HashMap<>())
                .build();
    }

    private String getCallerClassName() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (int i = 1; i < stackTrace.length; i++) {
            String className = stackTrace[i].getClassName();
            if (!className.equals(getClass().getName()) && 
                !className.contains("java.lang.Thread") &&
                !className.contains("java.lang.reflect") &&
                !className.contains("org.springframework")) {
                return className;
            }
        }
        return this.getClass().getName();
    }

    private String getCallerMethodName() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (int i = 1; i < stackTrace.length; i++) {
            String className = stackTrace[i].getClassName();
            if (!className.equals(getClass().getName()) && 
                !className.contains("java.lang.Thread") &&
                !className.contains("java.lang.reflect") &&
                !className.contains("org.springframework")) {
                return stackTrace[i].getMethodName();
            }
        }
        return "unknown";
    }

    @Override
    protected String buildLogMessage(LogMessage logMessage) {
        if (logMessage == null) {
            return "Empty log message";
        }

        StringBuilder message = new StringBuilder();
        
        // 添加基本信息
        String traceId = logMessage.getTags() != null ? 
            logMessage.getTags().getOrDefault("traceId", "N/A") : "N/A";
        message.append(String.format("[TraceId: %s] ", traceId));

        // 添加类名和方法名
        String className = logMessage.getClassName() != null ? 
            logMessage.getClassName() : this.getClass().getName();
        String methodName = logMessage.getMethodName() != null ? 
            logMessage.getMethodName() : "unknown";
        message.append(String.format("[%s.%s] ", className, methodName));

        // 添加主要消息
        String logContent = logMessage.getMessage();
        if (logContent != null) {
            if (logContent.contains("Start")) {
                appendStartMessage(message, logMessage);
            } else if (logContent.contains("End")) {
                appendEndMessage(message, logMessage);
            } else if (logMessage.getLevel() == LogLevel.WARN || logMessage.getLevel() == LogLevel.ERROR) {
                appendErrorMessage(message, logMessage);
            } else {
                message.append(logContent);
            }
        } else {
            message.append("No message content");
        }

        return message.toString();
    }

    private void appendStartMessage(StringBuilder message, LogMessage logMessage) {
        if (message == null || logMessage == null || logMessage.getTags() == null) {
            return;
        }

        message.append("Start");
        Map<String, String> tags = logMessage.getTags();
        
        // 添加请求信息
        if (tags.containsKey("method") && tags.containsKey("path")) {
            message.append(String.format(" | %s %s", 
                tags.getOrDefault("method", "UNKNOWN"),
                tags.getOrDefault("path", "UNKNOWN")));
        }
        
        // 添加参数信息
        if (tags.containsKey("params")) {
            String params = tags.get("params");
            if (params != null) {
                message.append(String.format(" | Params: %s", sensitiveDataConverter.convert(params)));
            }
        }
    }

    private void appendEndMessage(StringBuilder message, LogMessage logMessage) {
        if (message == null || logMessage == null || logMessage.getTags() == null) {
            return;
        }

        message.append("End");
        Map<String, String> tags = logMessage.getTags();
        
        // 添加执行时间
        if (tags.containsKey("executionTime")) {
            message.append(String.format(" | Time: %sms", 
                tags.getOrDefault("executionTime", "0")));
        }
        
        // 添加响应结果
        if (tags.containsKey("response")) {
            String response = tags.get("response");
            if (response != null) {
                message.append(String.format(" | Response: %s", sensitiveDataConverter.convert(response)));
            }
        }
    }

    private void appendErrorMessage(StringBuilder message, LogMessage logMessage) {
        if (message == null || logMessage == null || logMessage.getTags() == null) {
            return;
        }

        Map<String, String> tags = logMessage.getTags();
        
        // 添加异常信息
        if (tags.containsKey("exceptionType")) {
            message.append(String.format("%s: ", 
                tags.getOrDefault("exceptionType", "UnknownException")));
        }
        
        // 添加错误消息
        String errorMessage = logMessage.getMessage();
        if (errorMessage != null) {
            message.append(errorMessage);
        } else {
            message.append("Unknown error occurred");
        }
        
        // 添加参数信息
        if (tags.containsKey("params")) {
            String params = tags.get("params");
            if (params != null) {
                message.append(String.format(" | Params: %s", sensitiveDataConverter.convert(params)));
            }
        }
    }
} 