package com.example.common.logging.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "common.logging")
public class LoggingConfig {
    /**
     * 是否启用异步日志
     */
    private boolean async = true;

    /**
     * 服务名称
     */
    private String serviceName = "unknown";

    /**
     * 环境标识
     */
    private String environment = "unknown";

    /**
     * 异步配置
     */
    private AsyncConfig asyncConfig = new AsyncConfig();

    /**
     * 敏感数据配置
     */
    private SensitiveConfig sensitiveConfig = new SensitiveConfig();

    @Data
    public static class AsyncConfig {
        private int corePoolSize = 2;
        private int maxPoolSize = 5;
        private int queueCapacity = 100;
        private String threadNamePrefix = "AsyncLogger-";
    }

    @Data
    public static class SensitiveConfig {
        private boolean enabled = true;
        private char maskChar = '*';
        private String[] sensitiveFields = {
            "password", "secret", "key", "token", "credential"
        };
    }
} 