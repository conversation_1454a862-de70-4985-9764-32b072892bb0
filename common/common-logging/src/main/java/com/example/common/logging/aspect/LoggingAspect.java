package com.example.common.logging.aspect;

import com.example.common.logging.handler.LogHandler;
import com.example.common.logging.model.LogLevel;
import com.example.common.logging.model.LogMessage;
import com.example.common.logging.sensitive.SensitiveDataConverter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Aspect
@Component
public class LoggingAspect {

    private final LogHandler logHandler;
    private final SensitiveDataConverter sensitiveDataConverter;
    private final ObjectMapper objectMapper;

    public LoggingAspect(LogHandler logHandler, SensitiveDataConverter sensitiveDataConverter) {
        this.logHandler = logHandler;
        this.sensitiveDataConverter = sensitiveDataConverter;
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
    }

    @Around("@within(org.springframework.web.bind.annotation.RestController) || " +
            "@within(org.springframework.stereotype.Service)")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNames = signature.getParameterNames();
        Object[] args = joinPoint.getArgs();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        String requestPath = request != null ? request.getRequestURI() : "";
        String requestMethod = request != null ? request.getMethod() : "";

        // 构建参数Map
        Map<String, Object> params = new HashMap<>();
        if (parameterNames != null && args != null) {
            for (int i = 0; i < parameterNames.length && i < args.length; i++) {
                if (args[i] != null) {
                    params.put(parameterNames[i], args[i]);
                }
            }
        }

        // 记录方法开始执行
        StringBuilder startMsg = new StringBuilder();
        startMsg.append(String.format("[%s.%s] Start", className, methodName));
        if (request != null) {
            startMsg.append(String.format(" | %s %s", requestMethod, requestPath));
        }

        LogMessage startMessage = LogMessage.builder()
                .id(UUID.randomUUID().toString())
                .level(LogLevel.INFO)
                .message(startMsg.toString())
                .timestamp(LocalDateTime.now())
                .className(className)
                .methodName(methodName)
                .threadName(Thread.currentThread().getName())
                .tags(new HashMap<>())
                .build();

        // 添加参数信息
        try {
            String paramsJson = objectMapper.writeValueAsString(params);
            startMessage.getTags().put("params", sensitiveDataConverter.convert(paramsJson));
        } catch (Exception e) {
            startMessage.getTags().put("params", "Failed to serialize parameters");
        }

        logHandler.handle(startMessage);

        long startTime = System.currentTimeMillis();
        Object result = null;
        try {
            result = joinPoint.proceed();
            return result;
        } catch (Throwable e) {
            // 判断是否为业务异常
            boolean isBusinessException = isBusinessException(e);
            
            // 构建错误信息
            StringBuilder errorMsg = new StringBuilder();
            errorMsg.append(String.format("[%s.%s]", className, methodName));
            
            if (isBusinessException) {
                // 对于业务异常，记录完整的错误信息和参数
                errorMsg.append(" Business Exception: ").append(e.getMessage());
                if (!params.isEmpty()) {
                    errorMsg.append(" | Params: ").append(objectMapper.writeValueAsString(params));
                }
            } else {
                // 对于系统异常，添加更多上下文信息
                errorMsg.append(" System Exception: ").append(e.getMessage());
                if (e.getCause() != null) {
                    errorMsg.append(" | Cause: ").append(e.getCause().getMessage());
                }
            }
            
            LogMessage exceptionMessage = LogMessage.builder()
                    .id(UUID.randomUUID().toString())
                    .level(isBusinessException ? LogLevel.WARN : LogLevel.ERROR)
                    .message(errorMsg.toString())
                    .timestamp(LocalDateTime.now())
                    .className(className)
                    .methodName(methodName)
                    .threadName(Thread.currentThread().getName())
                    .tags(new HashMap<>())
                    .build();

            // 添加异常相关信息到tags
            exceptionMessage.getTags().put("exceptionType", e.getClass().getSimpleName());
            if (!isBusinessException) {
                exceptionMessage.setThrowable(e);
            }

            logHandler.handle(exceptionMessage);
            throw e;
        } finally {
            // 只在Controller层记录结束日志
            if (className.endsWith("Controller")) {
                long endTime = System.currentTimeMillis();
                StringBuilder endMsg = new StringBuilder();
                endMsg.append(String.format("[%s.%s] End", className, methodName));
                endMsg.append(String.format(" | Time: %dms", endTime - startTime));
                
                if (request != null) {
                    endMsg.append(String.format(" | %s %s", requestMethod, requestPath));
                }

                LogMessage endMessage = LogMessage.builder()
                        .id(UUID.randomUUID().toString())
                        .level(LogLevel.INFO)
                        .message(endMsg.toString())
                        .timestamp(LocalDateTime.now())
                        .className(className)
                        .methodName(methodName)
                        .threadName(Thread.currentThread().getName())
                        .tags(new HashMap<>())
                        .build();

                if (result != null) {
                    try {
                        String resultJson = objectMapper.writeValueAsString(result);
                        endMessage.getTags().put("response", sensitiveDataConverter.convert(resultJson));
                    } catch (Exception e) {
                        endMessage.getTags().put("response", "Failed to serialize response");
                    }
                }

                logHandler.handle(endMessage);
            }
        }
    }

    private boolean isBusinessException(Throwable e) {
        return e.getClass().getName().startsWith("com.example.mybatis.common.exception.Business") ||
               e.getClass().getName().contains(".business.") ||
               "BusinessException".equals(e.getClass().getSimpleName()) ||
               (e.getClass().getSuperclass() != null && 
                "BusinessException".equals(e.getClass().getSuperclass().getSimpleName()));
    }
} 