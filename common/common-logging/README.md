# Common Logging Module

## 简介
Common Logging Module 是一个功能强大的日志记录模块，提供了统一的日志处理、脱敏、链路追踪等功能。该模块基于Spring Boot构建，可以轻松集成到任何Spring Boot应用中。

## 主要特性

### 1. 统一日志处理
- 支持同步和异步日志记录
- 统一的日志格式和输出
- 多环境日志配置（开发、测试、生产）
- 日志文件自动轮转和清理

### 2. 日志脱敏处理
- 内置常见敏感信息脱敏规则（手机号、邮箱、身份证等）
- 支持自定义脱敏规则
- JSON数据自动脱敏
- 灵活的掩码配置

### 3. MDC上下文管理
- 请求级别的上下文信息管理
- 分布式链路追踪支持
- 用户会话信息关联
- 自动上下文清理

### 4. 方法级日志记录
- @Logging注解支持
- 方法执行时间统计
- 入参和出参记录
- 异常信息捕获

### 5. 彩色日志输出
- 控制台彩色输出支持
- 不同日志级别使用不同颜色
- 自定义颜色方案
- 文件日志保持普通格式

## 快速开始

### 1. 添加依赖
```xml
<dependency>
    <groupId>com.example</groupId>
    <artifactId>common-logging</artifactId>
    <version>${project.version}</version>
</dependency>
```

### 2. 配置属性（application.yml）
```yaml
common:
  logging:
    # 异步日志配置
    async: true
    async-config:
      core-pool-size: 2
      max-pool-size: 10
      queue-capacity: 512
      thread-name-prefix: async-logging-
    
    # 文件配置
    file-config:
      path: logs
      max-file-size: 100MB
      max-history: 30
      total-size-cap: 10GB
    
    # 日志格式配置
    format-config:
      pattern: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
      show-mdc: true
      show-thread: true
      show-class-and-method: true
    
    # 敏感信息配置
    sensitive-config:
      enabled: true
      mask-char: "*"
      sensitive-fields:
        - password
        - pwd
        - secret
        - credential
        - token
        - cardNo
        - idCard
        - phone
        - mobile
        - email
      mask-rules:
        phone: "$1****$3"
        email: "$1****$3"
        idCard: "$1**********$3"
```

### 3. 使用示例

#### 3.1 使用@Logging注解记录方法日志
```java
@Service
public class UserService {
    @Logging(value = "创建用户", logParams = true, logResult = true)
    public User createUser(UserDTO userDTO) {
        // 业务逻辑
        return user;
    }
}
```

#### 3.2 使用LogContext管理上下文
```java
@Service
public class UserService {
    public void processUser(String userId) {
        try {
            LogContext.setUserId(userId);
            LogContext.setServiceName("user-service");
            // 业务逻辑
        } finally {
            LogContext.clear();
        }
    }
}
```

#### 3.3 手动记录日志
```java
@Service
public class UserService {
    @Autowired
    private LogHandler logHandler;

    public void someMethod() {
        logHandler.info("Processing user data: %s", userData);
        try {
            // 业务逻辑
        } catch (Exception e) {
            logHandler.error("Failed to process user data", e);
            throw e;
        }
    }
}
```

## 日志输出示例

### 方法调用日志
```
2024-01-06 16:30:45.123 [async-logging-1] INFO  com.example.service.UserService - Method [createUser] started with args: {"name":"John","email":"j***@example.com","phone":"138****1234"}
2024-01-06 16:30:45.456 [async-logging-1] INFO  com.example.service.UserService - Method [createUser] completed successfully in 333 ms with result: {"id":"123","name":"John","email":"j***@example.com","phone":"138****1234"}
```

### Web请求日志
```
2024-01-06 16:30:45.123 [http-nio-8080-exec-1] INFO  com.example.web.UserController - Incoming request: POST /api/users
2024-01-06 16:30:45.456 [http-nio-8080-exec-1] INFO  com.example.web.UserController - Outgoing response: 200 OK (333ms)
```

### 错误日志
```
2024-01-06 16:30:45.123 [async-logging-1] ERROR com.example.service.UserService - Method [createUser] failed after 100ms
com.example.exception.UserException: Invalid user data
    at com.example.service.UserService.createUser(UserService.java:42)
    ...
```

## 高级特性

### 1. 自定义脱敏规则
```java
@Configuration
public class LoggingConfig {
    @Bean
    public SensitiveDataConverter sensitiveDataConverter(LoggingProperties properties) {
        // 自定义脱敏规则
        properties.getSensitiveConfig().getMaskRules().put("customField", "$1****");
        return new SensitiveDataConverter(properties);
    }
}
```

### 2. 自定义日志处理器
```java
@Component
public class CustomLogHandler extends AbstractLogHandler {
    @Override
    public void handle(LogMessage logMessage) {
        // 自定义日志处理逻辑
    }
}
```

### 3. 扩展MDC上下文
```java
public class ExtendedLogContext extends LogContext {
    public static final String TENANT_ID = "tenantId";
    
    public static void setTenantId(String tenantId) {
        MDC.put(TENANT_ID, tenantId);
    }
}
```

## 注意事项

1. 性能考虑
   - 异步日志配置建议根据系统负载调整
   - 合理配置日志级别，避免过多DEBUG日志
   - 注意日志文件大小和保留策略

2. 安全考虑
   - 敏感信息配置要全面
   - 定期审查日志内容确保脱敏有效
   - 注意日志文件权限设置

3. 最佳实践
   - 合理使用日志级别
   - 保持日志信息简洁明确
   - 定期清理日志文件
   - 在关键节点添加跟踪日志

## 常见问题

1. Q: 日志文件没有按配置轮转？
   A: 检查文件权限和磁盘空间

2. Q: 敏感信息未被脱敏？
   A: 检查脱敏规则配置和字段名匹配

3. Q: 异步日志性能问题？
   A: 调整线程池和队列配置

## 版本历史

- 1.0.0
  - 初始版本
  - 基础日志功能
  - 日志脱敏支持
  - MDC上下文管理

## 维护者

- 开发团队 (<EMAIL>)

## 参与贡献

1. Fork 项目
2. 创建特性分支
3. 提交代码
4. 发起Pull Request

## 许可证

[MIT](LICENSE) 

### 日志颜色说明
在控制台输出中，不同级别的日志会使用不同的颜色：
- ERROR: 红色
- WARN: 黄色
- INFO: 绿色
- DEBUG: 青色
- TRACE: 灰色

其他元素的颜色：
- 时间戳: 灰色
- 线程名: 灰色
- 类名: 青色
- 分隔符: 灰色 